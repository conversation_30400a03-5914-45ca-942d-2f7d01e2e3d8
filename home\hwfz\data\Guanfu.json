[{"prize": 3100215, "number": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 1, "id": 1}, {"prize": 82000009, "number": "1,1,1,2,2,1,1,1,1,1,2,2,2,2,2,2,2,2,2,3,3,3,3,3,3,3,3,3,3,3", "value": 1, "full_zhian_pro": "5,31000007;5,31000008;5,31000009;5,31000010;5,31000011", "prize1": "30,3100215,1", "choiceid": 2, "id": 1}, {"prize": 1000001, "number": "2000,2400,2800,3200,4000,5000,6000,6400,7200,8000,8800,9600,10200,11000,11800,12600,13400,14600,16000,17200,18400,19800,21000,22400,23600,24800,25600,28800,32000,38400", "value": 1, "full_zhian_pro": "5,31000007;5,31000008;5,31000009;5,31000010;5,31000011", "prize1": "30,3100215,1", "choiceid": 3, "id": 1}, {"prize": 3100215, "number": "1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 4, "id": 1}, {"prize": 1000004, "number": "2000,2400,2800,3200,4000,5000,6000,6400,7200,8000,8800,9600,10200,11000,11800,12600,13400,14600,16000,17200,18400,19800,21000,22400,23600,24800,25600,28800,32000,38400", "value": 1, "full_zhian_pro": "5,31000007;5,31000008;5,31000009;5,31000010;5,31000011", "prize1": "50,3100215,1", "choiceid": 5, "id": 1}, {"prize": 1000001, "number": "-2000,-2400,-2800,-3200,-4000,-5000,-6000,-6400,-7200,-8000,-8800,-9600,-10200,-11000,-11800,-12600,-13400,-14600,-16000,-17200,-18400,-19800,-21000,-22400,-23600,-24800,-25600,-28800,-32000,-38400", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 6, "id": 1}, {"prize": 1000005, "number": "-20,-24,-28,-32,-40,-50,-60,-64,-72,-80,-88,-96,-102,-110,-118,-126,-134,-146,-160,-172,-184,-198,-210,-224,-236,-248,-256,-288,-320,-384", "value": 1, "full_zhian_pro": "5,31000007;5,31000008;5,31000009;5,31000010;5,31000011", "prize1": "100,3100215,1", "choiceid": 7, "id": 2}, {"prize": 3100215, "number": "1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 8, "id": 2}, {"prize": 3100215, "number": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 9, "id": 2}, {"prize": 1000004, "number": "2000,2400,2800,3200,4000,5000,6000,6400,7200,8000,8800,9600,10200,11000,11800,12600,13400,14600,16000,17200,18400,19800,21000,22400,23600,24800,25600,28800,32000,38400", "value": 1, "full_zhian_pro": "5,31000007;5,31000008;5,31000009;5,31000010;5,31000011", "prize1": "30,3100215,1", "choiceid": 10, "id": 2}, {"prize": 1000003, "number": "200,240,280,320,400,500,600,640,720,800,880,960,1020,1100,1180,1260,1340,1460,1600,1720,1840,1980,2100,2240,2360,2480,2560,2880,3200,3840", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 11, "id": 2}, {"prize": 1000004, "number": "2000,2400,2800,3200,4000,5000,6000,6400,7200,8000,8800,9600,10200,11000,11800,12600,13400,14600,16000,17200,18400,19800,21000,22400,23600,24800,25600,28800,32000,38400", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 12, "id": 2}, {"prize": 3100215, "number": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 13, "id": 3}, {"prize": 3100215, "number": "1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 14, "id": 3}, {"prize": 1000004, "number": "-2000,-2400,-2800,-3200,-4000,-5000,-6000,-6400,-7200,-8000,-8800,-9600,-10200,-11000,-11800,-12600,-13400,-14600,-16000,-17200,-18400,-19800,-21000,-22400,-23600,-24800,-25600,-28800,-32000,-38400", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 15, "id": 3}, {"prize": 1000005, "number": "20,24,28,32,40,50,60,64,72,80,88,96,102,110,118,126,134,146,160,172,184,198,210,224,236,248,256,288,320,384", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 16, "id": 3}, {"prize": 1000004, "number": "2000,2400,2800,3200,4000,5000,6000,6400,7200,8000,8800,9600,10200,11000,11800,12600,13400,14600,16000,17200,18400,19800,21000,22400,23600,24800,25600,28800,32000,38400", "value": 1, "full_zhian_pro": "5,31000007;5,31000008;5,31000009;5,31000010;5,31000011", "prize1": "80,3100215,1", "choiceid": 17, "id": 3}, {"prize": 1000001, "number": "-2000,-2400,-2800,-3200,-4000,-5000,-6000,-6400,-7200,-8000,-8800,-9600,-10200,-11000,-11800,-12600,-13400,-14600,-16000,-17200,-18400,-19800,-21000,-22400,-23600,-24800,-25600,-28800,-32000,-38400", "value": 1, "full_zhian_pro": "5,31000007;5,31000008;5,31000009;5,31000010;5,31000011", "prize1": "30,3100215,1", "choiceid": 18, "id": 3}, {"prize": 3100215, "number": "1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 19, "id": 4}, {"prize": 3100215, "number": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 20, "id": 4}, {"prize": 1000004, "number": "2000,2400,2800,3200,4000,5000,6000,6400,7200,8000,8800,9600,10200,11000,11800,12600,13400,14600,16000,17200,18400,19800,21000,22400,23600,24800,25600,28800,32000,38400", "value": 1, "full_zhian_pro": "5,31000007;5,31000008;5,31000009;5,31000010;5,31000011", "prize1": "60,3100215,1", "choiceid": 21, "id": 4}, {"prize": 1000001, "number": "-2000,-2400,-2800,-3200,-4000,-5000,-6000,-6400,-7200,-8000,-8800,-9600,-10200,-11000,-11800,-12600,-13400,-14600,-16000,-17200,-18400,-19800,-21000,-22400,-23600,-24800,-25600,-28800,-32000,-38400", "value": 1, "full_zhian_pro": "5,31000007;5,31000008;5,31000009;5,31000010;5,31000011", "prize1": "100,3100215,1", "choiceid": 22, "id": 4}, {"prize": 1000003, "number": "200,240,280,320,400,500,600,640,720,800,880,960,1020,1100,1180,1260,1340,1460,1600,1720,1840,1980,2100,2240,2360,2480,2560,2880,3200,3840", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 23, "id": 4}, {"prize": 1000004, "number": "-2000,-2400,-2800,-3200,-4000,-5000,-6000,-6400,-7200,-8000,-8800,-9600,-10200,-11000,-11800,-12600,-13400,-14600,-16000,-17200,-18400,-19800,-21000,-22400,-23600,-24800,-25600,-28800,-32000,-38400", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 24, "id": 4}, {"prize": 1000004, "number": "-2000,-2400,-2800,-3200,-4000,-5000,-6000,-6400,-7200,-8000,-8800,-9600,-10200,-11000,-11800,-12600,-13400,-14600,-16000,-17200,-18400,-19800,-21000,-22400,-23600,-24800,-25600,-28800,-32000,-38400", "value": 1, "full_zhian_pro": "5,31000007;5,31000008;5,31000009;5,31000010;5,31000011", "prize1": "80,3100215,1", "choiceid": 25, "id": 5}, {"prize": 3100215, "number": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 26, "id": 5}, {"prize": 3100215, "number": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 27, "id": 5}, {"prize": 3100215, "number": "1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 28, "id": 5}, {"prize": 1000005, "number": "20,24,28,32,40,50,60,64,72,80,88,96,102,110,118,126,134,146,160,172,184,198,210,224,236,248,256,288,320,384", "value": 1, "full_zhian_pro": "5,31000007;5,31000008;5,31000009;5,31000010;5,31000011", "prize1": "50,3100215,1", "choiceid": 29, "id": 5}, {"prize": 1000003, "number": "200,240,280,320,400,500,600,640,720,800,880,960,1020,1100,1180,1260,1340,1460,1600,1720,1840,1980,2100,2240,2360,2480,2560,2880,3200,3840", "value": 1, "full_zhian_pro": "5,31000007;5,31000008;5,31000009;5,31000010;5,31000011", "prize1": "50,3100215,1", "choiceid": 30, "id": 5}, {"prize": 1000004, "number": "-2000,-2400,-2800,-3200,-4000,-5000,-6000,-6400,-7200,-8000,-8800,-9600,-10200,-11000,-11800,-12600,-13400,-14600,-16000,-17200,-18400,-19800,-21000,-22400,-23600,-24800,-25600,-28800,-32000,-38400", "value": 1, "full_zhian_pro": "5,31000007;5,31000008;5,31000009;5,31000010;5,31000011", "prize1": "100,3100215,2", "choiceid": 31, "id": 6}, {"prize": 1000004, "number": "-2000,-2400,-2800,-3200,-4000,-5000,-6000,-6400,-7200,-8000,-8800,-9600,-10200,-11000,-11800,-12600,-13400,-14600,-16000,-17200,-18400,-19800,-21000,-22400,-23600,-24800,-25600,-28800,-32000,-38400", "value": 1, "full_zhian_pro": "5,31000007;5,31000008;5,31000009;5,31000010;5,31000011", "prize1": "100,3100215,2", "choiceid": 32, "id": 6}, {"prize": 3100215, "number": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 33, "id": 6}, {"prize": 1000002, "number": "-2000,-2400,-2800,-3200,-4000,-5000,-6000,-6400,-7200,-8000,-8800,-9600,-10200,-11000,-11800,-12600,-13400,-14600,-16000,-17200,-18400,-19800,-21000,-22400,-23600,-24800,-25600,-28800,-32000,-38400", "value": 1, "full_zhian_pro": "5,31000007;5,31000008;5,31000009;5,31000010;5,31000011", "prize1": "100,3100215,2", "choiceid": 34, "id": 6}, {"prize": 1000004, "number": "2000,2400,2800,3200,4000,5000,6000,6400,7200,8000,8800,9600,10200,11000,11800,12600,13400,14600,16000,17200,18400,19800,21000,22400,23600,24800,25600,28800,32000,38400", "value": 1, "full_zhian_pro": "5,31000007;5,31000008;5,31000009;5,31000010;5,31000011", "prize1": "100,3100215,-1", "choiceid": 35, "id": 6}, {"prize": 1000004, "number": "-2000,-2400,-2800,-3200,-4000,-5000,-6000,-6400,-7200,-8000,-8800,-9600,-10200,-11000,-11800,-12600,-13400,-14600,-16000,-17200,-18400,-19800,-21000,-22400,-23600,-24800,-25600,-28800,-32000,-38400", "value": 1, "full_zhian_pro": "5,31000007;5,31000008;5,31000009;5,31000010;5,31000011", "prize1": "100,3100215,1", "choiceid": 36, "id": 6}, {"prize": 3100215, "number": "1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 37, "id": 7}, {"prize": 1000003, "number": "-200,-240,-280,-320,-400,-500,-600,-640,-720,-800,-880,-960,-1020,-1100,-1180,-1260,-1340,-1460,-1600,-1720,-1840,-1980,-2100,-2240,-2360,-2480,-2560,-2880,-3200,-3840", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 38, "id": 7}, {"prize": 1000003, "number": "200,240,280,320,400,500,600,640,720,800,880,960,1020,1100,1180,1260,1340,1460,1600,1720,1840,1980,2100,2240,2360,2480,2560,2880,3200,3840", "value": 1, "full_zhian_pro": "5,31000007;5,31000008;5,31000009;5,31000010;5,31000011", "prize1": "50,3100215,1", "choiceid": 39, "id": 7}, {"prize": 3100215, "number": "1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 40, "id": 7}, {"prize": 1000004, "number": "-2000,-2400,-2800,-3200,-4000,-5000,-6000,-6400,-7200,-8000,-8800,-9600,-10200,-11000,-11800,-12600,-13400,-14600,-16000,-17200,-18400,-19800,-21000,-22400,-23600,-24800,-25600,-28800,-32000,-38400", "value": 1, "full_zhian_pro": "5,31000007;5,31000008;5,31000009;5,31000010;5,31000011", "prize1": "100,3100215,1", "choiceid": 41, "id": 7}, {"prize": 1000003, "number": "-200,-240,-280,-320,-400,-500,-600,-640,-720,-800,-880,-960,-1020,-1100,-1180,-1260,-1340,-1460,-1600,-1720,-1840,-1980,-2100,-2240,-2360,-2480,-2560,-2880,-3200,-3840", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 42, "id": 7}, {"prize": 3100215, "number": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 43, "id": 8}, {"prize": 1000004, "number": "-2000,-2400,-2800,-3200,-4000,-5000,-6000,-6400,-7200,-8000,-8800,-9600,-10200,-11000,-11800,-12600,-13400,-14600,-16000,-17200,-18400,-19800,-21000,-22400,-23600,-24800,-25600,-28800,-32000,-38400", "value": 1, "full_zhian_pro": "5,31000007;5,31000008;5,31000009;5,31000010;5,31000011", "prize1": "100,3100215,1", "choiceid": 44, "id": 8}, {"prize": 1000003, "number": "200,240,280,320,400,500,600,640,720,800,880,960,1020,1100,1180,1260,1340,1460,1600,1720,1840,1980,2100,2240,2360,2480,2560,2880,3200,3840", "value": 1, "full_zhian_pro": "5,31000007;5,31000008;5,31000009;5,31000010;5,31000011", "prize1": "100,3100215,-5", "choiceid": 45, "id": 8}, {"prize": 1000004, "number": "-2000,-2400,-2800,-3200,-4000,-5000,-6000,-6400,-7200,-8000,-8800,-9600,-10200,-11000,-11800,-12600,-13400,-14600,-16000,-17200,-18400,-19800,-21000,-22400,-23600,-24800,-25600,-28800,-32000,-38400", "value": 1, "full_zhian_pro": "5,31000007;5,31000008;5,31000009;5,31000010;5,31000011", "prize1": "100,3100215,1", "choiceid": 46, "id": 8}, {"prize": 1000001, "number": "-2000,-2400,-2800,-3200,-4000,-5000,-6000,-6400,-7200,-8000,-8800,-9600,-10200,-11000,-11800,-12600,-13400,-14600,-16000,-17200,-18400,-19800,-21000,-22400,-23600,-24800,-25600,-28800,-32000,-38400", "value": 1, "full_zhian_pro": "5,31000007;5,31000008;5,31000009;5,31000010;5,31000011", "prize1": "100,3100215,1", "choiceid": 47, "id": 8}, {"prize": 82000009, "number": "1,1,1,2,2,1,1,1,1,1,2,2,2,2,2,2,2,2,2,3,3,3,3,3,3,3,3,3,3,3", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 48, "id": 8}, {"prize": 3100215, "number": "1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 49, "id": 9}, {"prize": 1000004, "number": "2000,2400,2800,3200,4000,5000,6000,6400,7200,8000,8800,9600,10200,11000,11800,12600,13400,14600,16000,17200,18400,19800,21000,22400,23600,24800,25600,28800,32000,38400", "value": 1, "full_zhian_pro": "5,31000007;5,31000008;5,31000009;5,31000010;5,31000011", "prize1": "100,3100215,1", "choiceid": 50, "id": 9}, {"prize": 1000004, "number": "2000,2400,2800,3200,4000,5000,6000,6400,7200,8000,8800,9600,10200,11000,11800,12600,13400,14600,16000,17200,18400,19800,21000,22400,23600,24800,25600,28800,32000,38400", "value": 1, "full_zhian_pro": "5,31000007;5,31000008;5,31000009;5,31000010;5,31000011", "prize1": "100,3100215,1", "choiceid": 51, "id": 9}, {"prize": 3100215, "number": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 52, "id": 9}, {"prize": 3100215, "number": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 53, "id": 9}, {"prize": 82000009, "number": "1,1,1,2,2,1,1,1,1,1,2,2,2,2,2,2,2,2,2,3,3,3,3,3,3,3,3,3,3,3", "value": 1, "full_zhian_pro": "5,31000007;5,31000008;5,31000009;5,31000010;5,31000011", "prize1": "50,3100215,1", "choiceid": 54, "id": 9}, {"prize": 3100215, "number": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 55, "id": 10}, {"prize": 1000004, "number": "-2000,-2400,-2800,-3200,-4000,-5000,-6000,-6400,-7200,-8000,-8800,-9600,-10200,-11000,-11800,-12600,-13400,-14600,-16000,-17200,-18400,-19800,-21000,-22400,-23600,-24800,-25600,-28800,-32000,-38400", "value": 1, "full_zhian_pro": "5,31000007;5,31000008;5,31000009;5,31000010;5,31000011", "prize1": "100,3100215,1", "choiceid": 56, "id": 10}, {"prize": 3100215, "number": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 57, "id": 10}, {"prize": 3100215, "number": "1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 58, "id": 10}, {"prize": 1000001, "number": "-2000,-2400,-2800,-3200,-4000,-5000,-6000,-6400,-7200,-8000,-8800,-9600,-10200,-11000,-11800,-12600,-13400,-14600,-16000,-17200,-18400,-19800,-21000,-22400,-23600,-24800,-25600,-28800,-32000,-38400", "value": 1, "full_zhian_pro": "5,31000007;5,31000008;5,31000009;5,31000010;5,31000011", "prize1": "100,3100215,1", "choiceid": 59, "id": 10}, {"prize": 82000009, "number": "1,1,1,2,2,1,1,1,1,1,2,2,2,2,2,2,2,2,2,3,3,3,3,3,3,3,3,3,3,3", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 60, "id": 10}, {"prize": 3100215, "number": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 61, "id": 11}, {"prize": 1000004, "number": "-2000,-2400,-2800,-3200,-4000,-5000,-6000,-6400,-7200,-8000,-8800,-9600,-10200,-11000,-11800,-12600,-13400,-14600,-16000,-17200,-18400,-19800,-21000,-22400,-23600,-24800,-25600,-28800,-32000,-38400", "value": 1, "full_zhian_pro": "5,31000007;5,31000008;5,31000009;5,31000010;5,31000011", "prize1": "100,3100215,1", "choiceid": 62, "id": 11}, {"prize": 3100215, "number": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 63, "id": 11}, {"prize": 3100215, "number": "1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 64, "id": 11}, {"prize": 1000001, "number": "-2000,-2400,-2800,-3200,-4000,-5000,-6000,-6400,-7200,-8000,-8800,-9600,-10200,-11000,-11800,-12600,-13400,-14600,-16000,-17200,-18400,-19800,-21000,-22400,-23600,-24800,-25600,-28800,-32000,-38400", "value": 1, "full_zhian_pro": "5,31000007;5,31000008;5,31000009;5,31000010;5,31000011", "prize1": "100,3100215,1", "choiceid": 65, "id": 11}, {"prize": 1000004, "number": "-2000,-2400,-2800,-3200,-4000,-5000,-6000,-6400,-7200,-8000,-8800,-9600,-10200,-11000,-11800,-12600,-13400,-14600,-16000,-17200,-18400,-19800,-21000,-22400,-23600,-24800,-25600,-28800,-32000,-38400", "value": 1, "full_zhian_pro": "5,31000007;5,31000008;5,31000009;5,31000010;5,31000011", "prize1": "100,3100215,1", "choiceid": 66, "id": 11}, {"prize": 1000001, "number": "-2000,-2400,-2800,-3200,-4000,-5000,-6000,-6400,-7200,-8000,-8800,-9600,-10200,-11000,-11800,-12600,-13400,-14600,-16000,-17200,-18400,-19800,-21000,-22400,-23600,-24800,-25600,-28800,-32000,-38400", "value": 1, "full_zhian_pro": "5,31000007;5,31000008;5,31000009;5,31000010;5,31000011", "prize1": "100,3100215,1", "choiceid": 67, "id": 12}, {"prize": 3100215, "number": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 68, "id": 12}, {"prize": 3100215, "number": "1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 69, "id": 12}, {"prize": 3100215, "number": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 70, "id": 12}, {"prize": 1000001, "number": "2000,2400,2800,3200,4000,5000,6000,6400,7200,8000,8800,9600,10200,11000,11800,12600,13400,14600,16000,17200,18400,19800,21000,22400,23600,24800,25600,28800,32000,38400", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 71, "id": 12}, {"prize": 82000009, "number": "1,1,1,2,2,1,1,1,1,1,2,2,2,2,2,2,2,2,2,3,3,3,3,3,3,3,3,3,3,3", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 72, "id": 12}, {"prize": 1000004, "number": "-2000,-2400,-2800,-3200,-4000,-5000,-6000,-6400,-7200,-8000,-8800,-9600,-10200,-11000,-11800,-12600,-13400,-14600,-16000,-17200,-18400,-19800,-21000,-22400,-23600,-24800,-25600,-28800,-32000,-38400", "value": 1, "full_zhian_pro": "5,31000007;5,31000008;5,31000009;5,31000010;5,31000011", "prize1": "100,3100215,1", "choiceid": 73, "id": 13}, {"prize": 3100215, "number": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 74, "id": 13}, {"prize": 3100215, "number": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 75, "id": 13}, {"prize": 1000002, "number": "2000,2400,2800,3200,4000,5000,6000,6400,7200,8000,8800,9600,10200,11000,11800,12600,13400,14600,16000,17200,18400,19800,21000,22400,23600,24800,25600,28800,32000,38400", "value": 1, "full_zhian_pro": "5,31000007;5,31000008;5,31000009;5,31000010;5,31000011", "prize1": "100,3100215,1", "choiceid": 76, "id": 13}, {"prize": 1000006, "number": "5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 77, "id": 13}, {"prize": 1000003, "number": "200,240,280,320,400,500,600,640,720,800,880,960,1020,1100,1180,1260,1340,1460,1600,1720,1840,1980,2100,2240,2360,2480,2560,2880,3200,3840", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 78, "id": 13}, {"prize": 3100215, "number": "1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 79, "id": 14}, {"prize": 1000004, "number": "-2000,-2400,-2800,-3200,-4000,-5000,-6000,-6400,-7200,-8000,-8800,-9600,-10200,-11000,-11800,-12600,-13400,-14600,-16000,-17200,-18400,-19800,-21000,-22400,-23600,-24800,-25600,-28800,-32000,-38400", "value": 1, "full_zhian_pro": "5,31000007;5,31000008;5,31000009;5,31000010;5,31000011", "prize1": "100,3100215,1", "choiceid": 80, "id": 14}, {"prize": 3100215, "number": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 81, "id": 14}, {"prize": 82000009, "number": "1,1,1,2,2,1,1,1,1,1,2,2,2,2,2,2,2,2,2,3,3,3,3,3,3,3,3,3,3,3", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 82, "id": 14}, {"prize": 1000004, "number": "-2000,-2400,-2800,-3200,-4000,-5000,-6000,-6400,-7200,-8000,-8800,-9600,-10200,-11000,-11800,-12600,-13400,-14600,-16000,-17200,-18400,-19800,-21000,-22400,-23600,-24800,-25600,-28800,-32000,-38400", "value": 1, "full_zhian_pro": "5,31000007;5,31000008;5,31000009;5,31000010;5,31000011", "prize1": "100,3100215,1", "choiceid": 83, "id": 14}, {"prize": 3100215, "number": "1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 84, "id": 14}, {"prize": 1000004, "number": "-2000,-2400,-2800,-3200,-4000,-5000,-6000,-6400,-7200,-8000,-8800,-9600,-10200,-11000,-11800,-12600,-13400,-14600,-16000,-17200,-18400,-19800,-21000,-22400,-23600,-24800,-25600,-28800,-32000,-38400", "value": 1, "full_zhian_pro": "5,31000007;5,31000008;5,31000009;5,31000010;5,31000011", "prize1": "100,3100215,1", "choiceid": 85, "id": 15}, {"prize": 1000004, "number": "-2000,-2400,-2800,-3200,-4000,-5000,-6000,-6400,-7200,-8000,-8800,-9600,-10200,-11000,-11800,-12600,-13400,-14600,-16000,-17200,-18400,-19800,-21000,-22400,-23600,-24800,-25600,-28800,-32000,-38400", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 86, "id": 15}, {"prize": 1000003, "number": "-200,-240,-280,-320,-400,-500,-600,-640,-720,-800,-880,-960,-1020,-1100,-1180,-1260,-1340,-1460,-1600,-1720,-1840,-1980,-2100,-2240,-2360,-2480,-2560,-2880,-3200,-3840", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 87, "id": 15}, {"prize": 82000009, "number": "1,1,1,2,2,1,1,1,1,1,2,2,2,2,2,2,2,2,2,3,3,3,3,3,3,3,3,3,3,3", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 88, "id": 15}, {"prize": 3100215, "number": "1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 89, "id": 15}, {"prize": 3100215, "number": "1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1", "value": 1, "full_zhian_pro": "", "prize1": "", "choiceid": 90, "id": 15}]