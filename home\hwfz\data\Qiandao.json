[{"reward": "1,27010000,150;2,27010100,300;3,27010200,400;4,27010300,100,5,27010400,50", "id": 1, "best_reward": "9000002,1;82010000,1"}, {"reward": "1,27010001,150;2,27010101,300;3,27010201,400;4,27010301,100,5,27010401,50", "id": 2, "best_reward": "9000002,1;82001030,1"}, {"reward": "1,27010002,150;2,27010102,300;3,27010202,400;4,27010302,100,5,27010402,50", "id": 3, "best_reward": "9000002,1;82001027,1"}, {"reward": "1,27010003,150;2,27010103,300;3,27010203,400;4,27010303,100,5,27010403,50", "id": 4, "best_reward": "9000002,1;8000004,1"}, {"reward": "1,27010004,150;2,27010104,300;3,27010204,400;4,27010304,100,5,27010404,50", "id": 5, "best_reward": "9000002,1;82000000,1"}, {"reward": "1,27010005,150;2,27010105,300;3,27010205,400;4,27010305,100,5,27010405,50", "id": 6, "best_reward": "9000002,1;82000001,1"}, {"reward": "1,27010006,150;2,27010106,300;3,27010206,400;4,27010306,100,5,27010406,50", "id": 7, "best_reward": "9000002,1;82000011,1"}, {"reward": "1,27010007,150;2,27010107,300;3,27010207,400;4,27010307,100,5,27010407,50", "id": 8, "best_reward": "9000002,1;8000058,1"}, {"reward": "1,27010008,150;2,27010108,300;3,27010208,400;4,27010308,100,5,27010408,50", "id": 9, "best_reward": "9000002,1;8000044,1"}, {"reward": "1,27010009,150;2,27010109,300;3,27010209,400;4,27010309,100,5,27010409,50", "id": 10, "best_reward": "9000002,1;1000003,50000"}, {"reward": "1,27010010,150;2,27010110,300;3,27010210,400;4,27010310,100,5,27010410,50", "id": 11, "best_reward": "9000002,1;1000005,5000"}, {"reward": "1,27010011,150;2,27010111,300;3,27010211,400;4,27010311,100,5,27010411,50", "id": 12, "best_reward": "9000002,1;1000002,500000"}, {"reward": "1,27010012,150;2,27010112,300;3,27010212,400;4,27010312,100,5,27010412,50", "id": 13, "best_reward": "9000002,1;8000036,1"}, {"reward": "1,27010013,150;2,27010113,300;3,27010213,400;4,27010313,100,5,27010413,50", "id": 14, "best_reward": "9000002,1;8000012,1"}, {"reward": "1,27010014,150;2,27010114,300;3,27010214,400;4,27010314,100,5,27010414,50", "id": 15, "best_reward": "9000002,1;8000004,1"}, {"reward": "1,27010015,150;2,27010115,300;3,27010215,400;4,27010315,100,5,27010415,50", "id": 16, "best_reward": "9000002,1;8000028,1"}, {"reward": "1,27010016,150;2,27010116,300;3,27010216,400;4,27010316,100,5,27010416,50", "id": 17, "best_reward": "9000002,1;1000004,500000"}, {"reward": "1,27010017,150;2,27010117,300;3,27010217,400;4,27010317,100,5,27010417,50", "id": 18, "best_reward": "9000002,1;8000060,1"}, {"reward": "1,27010018,150;2,27010118,300;3,27010218,400;4,27010318,100,5,27010418,50", "id": 19, "best_reward": "9000002,1;8000020,1"}, {"reward": "1,27010019,150;2,27010119,300;3,27010219,400;4,27010319,100,5,27010419,50", "id": 20, "best_reward": "9000002,1;82010000,1"}, {"reward": "1,27010020,150;2,27010120,300;3,27010220,400;4,27010320,100,5,27010420,50", "id": 21, "best_reward": "9000002,1;82001030,1"}, {"reward": "1,27010021,150;2,27010121,300;3,27010221,400;4,27010321,100,5,27010421,50", "id": 22, "best_reward": "9000002,1;82001027,1"}, {"reward": "1,27010022,150;2,27010122,300;3,27010222,400;4,27010322,100,5,27010422,50", "id": 23, "best_reward": "9000002,1;8000004,1"}, {"reward": "1,27010023,150;2,27010123,300;3,27010223,400;4,27010323,100,5,27010423,50", "id": 24, "best_reward": "9000002,1;82000000,1"}, {"reward": "1,27010024,150;2,27010124,300;3,27010224,400;4,27010324,100,5,27010424,50", "id": 25, "best_reward": "9000002,1;82000001,1"}, {"reward": "1,27010025,150;2,27010125,300;3,27010225,400;4,27010325,100,5,27010425,50", "id": 26, "best_reward": "9000002,1;82000011,1"}, {"reward": "1,27010026,150;2,27010126,300;3,27010226,400;4,27010326,100,5,27010426,50", "id": 27, "best_reward": "9000002,1;1000003,50000"}, {"reward": "1,27010027,150;2,27010127,300;3,27010227,400;4,27010327,100,5,27010427,50", "id": 28, "best_reward": "9000002,1;1000005,5000"}, {"reward": "1,27010028,150;2,27010128,300;3,27010228,400;4,27010328,100,5,27010428,50", "id": 29, "best_reward": "9000002,1;1000002,500000"}, {"reward": "1,27010029,150;2,27010129,300;3,27010229,400;4,27010329,100,5,27010429,50", "id": 30, "best_reward": "9000002,1;1000004,500000"}, {"reward": "1,27010030,150;2,27010130,300;3,27010230,400;4,27010330,100,5,27010430,50", "id": 31, "best_reward": "9000002,1;8000004,1"}]