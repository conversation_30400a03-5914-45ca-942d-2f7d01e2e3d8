#####################################################################
# Copyright (c) 2004 Contributors.
# All rights reserved. 
# This program and the accompanying materials are made available 
# under the terms of the Eclipse Public License v1.0 
# which accompanies this distribution and is available at 
# http://www.eclipse.org/legal/epl-v10.html 
#  
# Contributors: 
#     Adrian Colyer - Initial version
#####################################################################

# Messages output by the AspectJ Weaver

# Pointcut related messages...
argsInDeclare=args() pointcut designator cannot be used in declare statement
cflowInDeclare=cflow{0}() pointcut designator cannot be used in declare statement
ifInDeclare=if() pointcut designator cannot be used in declare statement
thisOrTargetInDeclare={0}() pointcut designator cannot be used in declare statement
abstractPointcut={0} is abstract
abstractPointcutNotMadeConcrete=inherited abstract {0} is not made concrete in {1}
conflictingInheritedPointcuts=conflicting inherited pointcuts in {0}
circularPointcutDeclaration=circular pointcut declaration involving: {0}
cantFindPointcut=can''t find pointcut ''{0}'' on {1}
exactTypePatternRequired=exact type pattern required
pointcutNotVisible=pointcut ''{0}'' is not visible from type ''{1}'' - cannot override
cantBindType=can''t bind type name ''{0}''
wildcardTypePatternNotAllowed=wildcard type pattern not allowed, must use type name
fieldCantBeVoid=fields cannot have a void type
noNewArrayJoinpointsByDefault=There are no join points for array construction unless -Xjoinpoints:arrayconstruction is specified
unsupportedPointcutPrimitive=Pointcut expression ''{0}'' contains unsupported pointcut primitive ''{1}''
missingTypePreventsMatch="Unable to determine match at this join point because the type ''{0}'' cannot be found"

# Declare parents messages...
decpObject=can''t change the parents of java.lang.Object
cantExtendSelf=type ''{0}''can not extend itself
interfaceExtendClass=interface can not extend a class
decpHierarchy=can only insert a class into hierarchy, but {0} is not a subtype of {1}

# declare precedence messages...
multipleMatchesInPrecedence=multiple matches for {0}, matches both {1} and {2}
circularityInPrecedenceStar=circularity in declare precedence, ''*'' occurs more than once
nonAspectTypesInPrecedence=Non-aspect types can only be specified in a declare precedence statement when subtypes are included.  Non-aspect type is : {0}
circularityInPrecedenceTwo=circularity in declare precedence, ''{0}'' matches two patterns

# declare soft messages...
notThrowable={0} is not a subtype of Throwable

# itd messages...
itdConsOnAspect=can''t declare constructor on an aspect
returnTypeMismatch=can''t override {0} with {1} return types don''t match
paramTypeMismatch=can''t override {0} with {1} parameter types don''t match
visibilityReduction=can''t override {0} with {1} visibility is reduced
cantOverrideFinalMember=can''t override final {0}
doesntThrow=overriden method doesn't throw {0}
overriddenStatic={0} cannot override {1}; overridden method is static
overridingStatic={0} cannot override {1}; overriding method is static
itdConflict=intertype declaration from {0} conflicts with intertype declaration: {1} from {2}
itdMemberConflict=inter-type declaration from {0} conflicts with existing member: {1}
itdNonExposedImplementor=type {0} must be accessible for weaving interface inter type declaration from aspect {1}
itdAbstractMustBePublicOnInterface=abstract intertype method declaration ''{0}'' on interface {1} must be declared public (compiler limitation)

# advice messages...
nonVoidReturn=applying to join point that doesn''t return void: {0}
incompatibleReturnType=incompatible return type applying to {0}
cantThrowChecked=can''t throw checked exception ''{0}'' at this join point ''{1}''
circularDependency=circular advice precedence: can''t determine precedence between two or more pieces of advice that apply to the same join point: {0}

# aspect messages..
missingPerClause=expected per clause on super aspect not found on {0}
wrongPerClause=wrong kind of per clause on super, expected {0} but found {1}

# Reweavable messages...
alreadyWoven=class ''{0}'' is already woven and has not been built in reweavable mode
reweavableMode=weaver operating in reweavable mode.  Need to verify any required types exist.
processingReweavable=processing reweavable type {0}: {1}
missingReweavableType=type {0} is needed by reweavable type {1}
verifiedReweavableType=successfully verified type {0} exists.  Originates from {1}
aspectNeeded=aspect {0} is needed when using type {1}
reweavableAspectNotRegistered=aspect ''{0}'' woven into ''{1}'' must be defined to the weaver (placed on the aspectpath, or defined in an aop.xml file if using LTW).

# The infamous and deserving a category all of its own...
cantFindType=can''t find type {0}
cantFindCoreType=can''t find critical required type {0}
cantFindTypeWithinpcd=Unable to find type {0} whilst processing within() pointcut at this source location
cftDuringAroundWeave=Can't find type {0} whilst applying around advice
cftDuringAroundWeavePreinit=Can't find type {0} whilst applying around advice to preinitialization join point
cftExceptionType=Can't find exception type {0} whilst processing advice
cftArgType=Can't find type {0} whilst processing args() pcd
cantFindParentType=can''t find type {0} needed to evaluate methods inherited by subtype {1}
cantFindParentTypeNoSub=can''t find type {0} when attempting to find the set of methods it declares
cantFindTypeFields=can''t find fields of missing type {0}
cantFindTypeSuperclass=can''t determine superclass of missing type {0}
cantFindTypeInterfaces=can''t determine implemented interfaces of missing type {0}
cantFindTypeMethods=can''t determine methods of missing type {0}
cantFindTypePointcuts=can''t determine pointcuts declared in missing type {0}
cantFindTypeModifiers=can''t determine modifiers of missing type {0}
cantFindTypeAnnotation=can''t determine annotations of missing type {0}
cantFindTypeAssignable=can''t determine whether missing type {0} is an instance of {1}
cantFindTypeCoerceable=can''t determine whether missing type {0} can be coerced from {1}
cantFindTypeJoinPoint=can''t find type {0} whilst determining signatures of call or execution join point for {1}, this may cause a pointcut to fail to match at this join point 
cantFindTypeInterfaceMethods=can''t find type {0} whilst determining all methods of an implementing subtype, this may cause a pointcut to fail to match at a call or execution join point, or an illegal method override via an ITD to go undetected

# Implementation limitations...
decpBinaryLimitation=can''t use declare parents to change superclass of binary form ''{0}'' (implementation limitation)
overwriteJSR45=overwriting JSR45 information for {0} (compiler limitation)
ifInPerClause=if() pointcut designator cannot be used directly in a per clause (compiler limitation).  Create a named pointcut containing the if() and refer to it
ifLexicallyInCflow=if not supported lexically within cflow (compiler limitation)
onlyBeforeOnHandler=Only before advice is supported on handler join points (compiler limitation)
noAroundOnSynchronization=Around advice is not supported on the lock and unlock join points (compiler limitation)
aroundOnPreInit=around on pre-initialization not supported (compiler limitation)
aroundOnInit=around on initialization not supported (compiler limitation)
aroundOnInterfaceStaticInit=around on staticinitialization of interface ''{0}'' not supported (compiler limitation)
unsupportedAnnotationValueType=Compiler limitation: annotation value support not implemented for type {0}

# Bytecode generation nasties...
problemGeneratingMethod=problem generating method {0}.{1} : {2}
classTooBig=The class {0} exceeds the maximum class size supported by the JVM (constant pool too big).

# Classpath messages
zipfileEntryMissing=zipfile classpath entry does not exist: {0}
zipfileEntryInvalid=zipfile classpath entry is invalid: {0} <{1}>
directoryEntryMissing=directory classpath entry does not exist: {0}
outjarInInputPath=-outjar cannot be a member of -injars, -inpath or -aspectpath

# Lint messages
problemLoadingXLint=problem loading Xlint properties file: {0}, {1}
unableToLoadXLintDefault=couldn''t load XlintDefault.properties
errorLoadingXLintDefault=problem loading XlintDefault.properties, {0}
invalidXLintKey=invalid Xlint key: {0}
invalidXLintMessageKind=invalid Xlint message kind (must be one of ignore, warning, error): {0}

# Binding of formals
unboundFormalInPC=the parameter {0} is not bound in [all branches of] pointcut
ambiguousBindingInPC=the binding of parameter {0} is ambiguous in pointcut
ambiguousBindingInOrPC=ambiguous binding of parameter(s) {0} across ''||'' in pointcut
negationDoesntAllowBinding=cannot bind a parameter in a negated expression

# Java5 

# Enum
itdcOnEnumNotAllowed=can''t make inter-type constructor declarations on enum types
itdmOnEnumNotAllowed=can''t make inter-type method declarations on enum types
itdfOnEnumNotAllowed=can''t make inter-type field declarations on enum types
cantDecpOnEnumToImplInterface=can''t use declare parents to make enum type {0} implement an interface
cantDecpOnEnumToExtendClass=can''t use declare parents to alter supertype of enum type {0}
cantDecpToMakeEnumSupertype=can''t use declare parents to make ''java.lang.Enum'' the parent of type {0}

# Annotation
itdcOnAnnotationNotAllowed=can''t make inter-type constructor declarations on annotation types
itdmOnAnnotationNotAllowed=can''t make inter-type method declarations on annotation types
itdfOnAnnotationNotAllowed=can''t make inter-type field declarations on annotation types
cantDecpOnAnnotationToImplInterface=can''t use declare parents to make annotation type {0} implement an interface
cantDecpOnAnnotationToExtendClass=can''t use declare parents to alter supertype of annotation type {0}
cantDecpToMakeAnnotationSupertype=can''t use declare parents to make ''java.lang.annotation.Annotation'' the parent of type {0}
incorrectTargetForDeclareAnnotation={0} is not a valid target for annotation {1}, this annotation can only be applied to these element types {2}

referenceToNonAnnotationType=Type referred to is not an annotation type: {0}
bindingNonRuntimeRetentionAnnotation=Annotation type {0} does not have runtime retention
noMatchBecauseSourceRetention=Failing match because annotation ''{0}'' on type ''{1}'' has SOURCE retention.  Matching allowed when RetentionPolicy is CLASS or RUNTIME

# Annotation value
invalidAnnotationValue=Invalid annotation value ''{0}'', expected {1} value
unknownAnnotationValue=The annotation ''{0}'' does not define a value named ''{1}''

# Generics
cantDecpMultipleParameterizations=Cannot declare parent {0} onto type {1} since it already has {2} in its hierarchy
noParameterizedTypePatternInHandler=a parameterized type pattern may not be used in a handler pointcut expression
incorrectNumberOfTypeArguments=Type pattern does not match because the wrong number of type parameters are specified: Type {0} requires {1} parameter(s)
violatesTypeVariableBounds=Type {0} does not meet the specification for type parameter {1} ({2}) in generic type {3}
notAGenericType=Type pattern does not match because {0} is not a generic type
noStaticInitJPsForParameterizedTypes=no static initialization join points for parameterized types, use raw type instead
noParameterizedTypePatternInWithin=parameterized type pattern not supported by 'within', use a raw type pattern instead
noParameterizedTypesInThisAndTarget=parameterized types not supported for this and target pointcuts (erasure limitation)
noParameterizedTypesInGetAndSet=can't use parameterized type patterns for the declaring type of a get or set pointcut expression (use the raw type instead)
noInitJPsForParameterizedTypes=no [pre]initialization join points for parameterized types, use raw type instead
noGenericThrowables=invalid throws pattern: a generic class may not be a direct or indirect subclass of Throwable
noParameterizedDeclaringTypesWithinCode=can't use parameterized type patterns for the declaring type of a withincode pointcut expression (use the raw type instead)
noParameterizedDeclaringTypesInExecution=can't use parameterized type patterns for the declaring type of an execution pointcut expression (use the raw type instead)
noParameterizedDeclaringTypesInCall=can't use parameterized type patterns for the declaring type of a call pointcut expression (use the raw type instead)
noRawTypePointcutReferences=cannot use a raw type reference to refer to a pointcut in a generic type (use a parameterized reference instead)

hasMemberNotEnabled=the type pattern {0} can only be used when the -XhasMember option is set

# Java5 features used in pre-Java 5 environment
atannotationNeedsJava5=the @annotation pointcut expression is only supported at Java 5 compliance level or above
atwithinNeedsJava5=the @within pointcut expression is only supported at Java 5 compliance level or above
atwithincodeNeedsJava5=the @withincode pointcut expression is only supported at Java 5 compliance level or above
atthisNeedsJava5=the @this pointcut expression is only supported at Java 5 compliance level or above
attargetNeedsJava5=the @target pointcut expression is only supported at Java 5 compliance level or above
atargsNeedsJava5=the @args pointcut expression is only supported at Java 5 compliance level or above
declareAtTypeNeedsJava5=declare @type is only supported at Java 5 compliance level or above
declareAtMethodNeedsJava5=declare @method is only supported at Java 5 compliance level or above
declareAtFieldNeedsJava5=declare @field is only supported at Java 5 compliance level or above
declareAtConsNeedsJava5=declare @constructor is only supported at Java 5 compliance level or above
annotationsRequireJava5=annotation type patterns are only supported at Java 5 compliance level or above

# @AspectJ
returningFormalNotDeclaredInAdvice=the last parameter of this advice must be named ''{0}'' to bind the returning value
thrownFormalNotDeclaredInAdvice=the last parameter of this advice must be named ''{0}'' and be of a subtype of Throwable