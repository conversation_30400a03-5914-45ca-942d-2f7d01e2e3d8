<?xml version="1.0" encoding="UTF-8" standalone="no"?>

<xsd:schema xmlns="http://www.springframework.org/schema/tool"
		xmlns:xsd="http://www.w3.org/2001/XMLSchema"
		targetNamespace="http://www.springframework.org/schema/tool"
		elementFormDefault="qualified">

	<xsd:import namespace="http://www.w3.org/XML/1998/namespace"/>

	<xsd:annotation>
		<xsd:documentation><![CDATA[
	Defines the tool support annotations for Spring's configuration namespaces.
	Used in other namespace XSD files; not intended for direct use in config files.
		]]></xsd:documentation>
	</xsd:annotation>

	<xsd:element name="annotation">
		<xsd:complexType>
			<xsd:sequence minOccurs="0">
				<xsd:element name="expected-type" type="typedParameterType" minOccurs="0" maxOccurs="1"/>
				<xsd:element name="assignable-to" type="typedParameterType" minOccurs="0" maxOccurs="1"/>
				<xsd:element name="exports" type="exportsType" minOccurs="0" maxOccurs="1"/>
				<xsd:element name="registers-scope" type="registersScopeType" minOccurs="0" maxOccurs="unbounded"/>
			</xsd:sequence>
			<xsd:attribute name="kind" default="direct">
				<xsd:simpleType>
					<xsd:restriction base="xsd:string">
						<xsd:enumeration value="ref"/>
						<xsd:enumeration value="direct"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:attribute>
		</xsd:complexType>
	</xsd:element>

	<xsd:complexType name="typedParameterType">
		<xsd:attribute name="type" type="xsd:string" use="required"/>
	</xsd:complexType>

	<xsd:complexType name="exportsType">
		<xsd:annotation>
			<xsd:documentation><![CDATA[
	Indicates that an annotated type exports an application visible component.
		]]></xsd:documentation>
		</xsd:annotation>
		<xsd:attribute name="type" type="xsd:string">
			<xsd:annotation>
				<xsd:documentation><![CDATA[
	The type of the exported component. May be null if the type is not known until runtime.
		]]></xsd:documentation>
			</xsd:annotation>
		</xsd:attribute>
		<xsd:attribute name="identifier" type="xsd:string" default="@id">
			<xsd:annotation>
				<xsd:documentation><![CDATA[
	Defines an XPath query that can be executed against the node annotated with this
	type to determine the identifier of any exported component.
		]]></xsd:documentation>
			</xsd:annotation>
		</xsd:attribute>
	</xsd:complexType>

	<xsd:complexType name="registersScopeType">
		<xsd:attribute name="name" type="xsd:string" use="required">
			<xsd:annotation>
				<xsd:documentation><![CDATA[
	Defines the name of a custom bean scope that the annotated type registers, e.g. "conversation".
	Such a scope will be available in addition to the standard "singleton" and "prototype" scopes
	(plus "request", "session" and "globalSession" in a web application environment).
		]]></xsd:documentation>
			</xsd:annotation>
		</xsd:attribute>
	</xsd:complexType>

</xsd:schema>
