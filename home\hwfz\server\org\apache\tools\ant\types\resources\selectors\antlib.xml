<?xml version="1.0"?>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<antlib>
  <typedef name="and"
    classname="org.apache.tools.ant.types.resources.selectors.And" />
  <typedef name="compare"
    classname="org.apache.tools.ant.types.resources.selectors.Compare" />
  <typedef name="contains"
    classname="org.apache.tools.ant.types.selectors.ContainsSelector" />
  <typedef name="containsregexp"
    classname="org.apache.tools.ant.types.selectors.ContainsRegexpSelector" />
  <typedef name="date"
    classname="org.apache.tools.ant.types.resources.selectors.Date" />
  <typedef name="exists"
    classname="org.apache.tools.ant.types.resources.selectors.Exists" />
  <typedef name="instanceof"
    classname="org.apache.tools.ant.types.resources.selectors.InstanceOf" />
  <typedef name="majority"
    classname="org.apache.tools.ant.types.resources.selectors.Majority" />
  <typedef name="modified"
    classname="org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector" />
  <typedef name="name"
    classname="org.apache.tools.ant.types.resources.selectors.Name" />
  <typedef name="none"
    classname="org.apache.tools.ant.types.resources.selectors.None" />
  <typedef name="not"
    classname="org.apache.tools.ant.types.resources.selectors.Not" />
  <typedef name="or"
    classname="org.apache.tools.ant.types.resources.selectors.Or" />
  <typedef name="readable"
    classname="org.apache.tools.ant.types.selectors.ReadableSelector" />
  <typedef name="size"
    classname="org.apache.tools.ant.types.resources.selectors.Size" />
  <typedef name="type"
    classname="org.apache.tools.ant.types.resources.selectors.Type" />
  <typedef name="writable"
    classname="org.apache.tools.ant.types.selectors.WritableSelector" />
</antlib>
