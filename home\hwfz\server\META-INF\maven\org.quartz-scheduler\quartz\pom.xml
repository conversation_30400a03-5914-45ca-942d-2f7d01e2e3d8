<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.quartz-scheduler</groupId>
    <artifactId>quartz-parent</artifactId>
    <version>2.2.3</version>
  </parent>

  <artifactId>quartz</artifactId>
  <packaging>jar</packaging>
  <name>quartz</name>
  <description>Enterprise Job Scheduler</description>

  <properties>
    <gmaven-plugin.version>1.4</gmaven-plugin.version>
  </properties>

  <dependencies>  
    <!-- internal components used for shading which will be removed in final pom -->
    <dependency>
      <groupId>org.quartz-scheduler.internal</groupId>
      <artifactId>quartz-terracotta-bootstrap</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>org.quartz-scheduler.internal</groupId>
      <artifactId>quartz-core</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>org.quartz-scheduler.internal</groupId>
      <artifactId>quartz-commonj</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>org.quartz-scheduler.internal</groupId>
      <artifactId>quartz-jboss</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>org.quartz-scheduler.internal</groupId>
      <artifactId>quartz-oracle</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>org.quartz-scheduler.internal</groupId>
      <artifactId>quartz-weblogic</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>org.quartz-scheduler.internal</groupId>
      <artifactId>quartz-plugins</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>org.quartz-scheduler</groupId>
      <artifactId>quartz-jobs</artifactId>
      <version>${project.version}</version>
      <scope>provided</scope>
    </dependency>    
    <dependency>
      <groupId>org.terracotta</groupId>
      <artifactId>product-upgradability-testing-utils</artifactId>
      <version>0.9.0</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
  
  <build>
    <resources>
      <resource>
        <directory>${basedir}/src/main/resources</directory>
        <filtering>true</filtering>
      </resource>
    </resources>
    
    <plugins>   
      <plugin>
        <groupId>org.terracotta</groupId>
        <artifactId>maven-forge-plugin</artifactId>
        <version>${maven-forge-plugin.version}</version>
        <executions>
          <execution>
            <id>create-manifest</id>
            <phase>process-resources</phase>
            <goals>
              <goal>manifest</goal>
            </goals>
            <configuration>
              <rootPath>${basedir}/..</rootPath>
              <manifestFile>${project.build.directory}/MANIFEST.MF</manifestFile>
              <manifestEntries>
                <License>Apache Software License, Version 2.0</License>
                <Terracotta-ProjectStatus>Supported</Terracotta-ProjectStatus>
                <Terracotta-Name>${project.artifactId}</Terracotta-Name>
                <Terracotta-Description>${project.description}</Terracotta-Description>
              </manifestEntries>
            </configuration>            
          </execution>
          <execution>
            <id>enforce-dependencies</id>
            <goals>
              <goal>enforceDependencies</goal>
            </goals>
            <phase>verify</phase>
            <configuration>
              <enforceGroupId>org.quartz-scheduler.internal</enforceGroupId>
              <enforceArtifactId>quartz-core</enforceArtifactId>
              <enforceVersion>${project.version}</enforceVersion>
            </configuration>
          </execution>           
        </executions>
      </plugin>
      
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>2.9</version>
        <configuration>
          <skip>${skipJavadoc}</skip>
          <quiet>true</quiet>
          <doctitle>Quartz Enterprise Job Scheduler ${project.version} API</doctitle>
          <windowtitle>Quartz Enterprise Job Scheduler ${project.version} API</windowtitle>
          <bottom>Copyright 2001-{currentYear}, Terracotta, Inc.</bottom>
          <includeDependencySources>true</includeDependencySources>      
          <dependencySourceIncludes>
            <dependencySourceInclude>org.quartz-scheduler.internal:*</dependencySourceInclude>
            <dependencySourceInclude>org.quartz-scheduler:quartz-jobs</dependencySourceInclude>
          </dependencySourceIncludes>
        </configuration>
        <executions>
          <execution>
            <id>create-javadoc</id>
            <phase>package</phase>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>      
      
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-shade-plugin</artifactId>
        <version>2.1</version>
        <configuration>
          <artifactSet>
            <includes>
              <include>org.quartz-scheduler.internal:*</include>
            </includes>
            <excludes>
              <exclude>*:quartz-stubs:*</exclude>
            </excludes>
          </artifactSet>       
          <filters>
            <filter>
              <artifact>*:*</artifact>
              <excludes>
                <exclude>**/META-INF/maven/org.quartz-scheduler.internal/**</exclude>
                <exclude>**/license.txt</exclude>
                <exclude>**/thirdpartylicenses.txt</exclude>
              </excludes>
            </filter>		  
          </filters>    
          <createSourcesJar>true</createSourcesJar>
          <useBaseVersion>true</useBaseVersion>
          <promoteTransitiveDependencies>true</promoteTransitiveDependencies>
          <dependencyReducedPomLocation>${project.build.directory}/dependency-reduced-pom.xml</dependencyReducedPomLocation>           
          <transformers>
            <transformer implementation="org.apache.maven.plugins.shade.resource.DontIncludeResourceTransformer">
              <resource>MANIFEST.MF</resource>
            </transformer>          
            <transformer implementation="org.apache.maven.plugins.shade.resource.IncludeResourceTransformer">
              <resource>META-INF/MANIFEST.MF</resource>
              <file>${project.build.directory}/MANIFEST.MF</file>
            </transformer>          
          </transformers>            
        </configuration>
        <executions>
          <execution>
            <id>shade-jar</id>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
          </execution>
        </executions>
      </plugin>      
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-antrun-plugin</artifactId>
        <version>1.7</version>
        <executions>
          <execution>
            <id>unpack-jar-for-bundling</id>
            <phase>package</phase>
            <goals>
              <goal>run</goal>
            </goals>
            <configuration>
              <target>
                <unzip src="${project.build.directory}/${project.artifactId}-${project.version}.jar" dest="${project.build.outputDirectory}"/>
              </target>
            </configuration>
          </execution>
        </executions>
      </plugin>           
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <version>2.3.7</version>
        <configuration>
          <manifestLocation>${project.build.directory}</manifestLocation>
          <instructions>
            <Import-Package>
            javax.servlet;resolution:=optional,
            javax.servlet.http;resolution:=optional,
            javax.sql;resolution:=optional,
            javax.mail;resolution:=optional,
            javax.mail.internet;resolution:=optional,
            javax.transaction;resolution:=optional,
            javax.ejb;resolution:=optional,
            javax.jms;resolution:=optional,
            org.terracotta.toolkit.*;resolution:=optional,
            weblogic.jdbc.*;resolution:=optional,
            oracle.sql;resolution:=optional,
            org.jboss.logging;resolution:=optional,
            org.jboss.naming;resolution:=optional,
            org.jboss.system;resolution:=optional,
            commonj.work;resolution:=optional,
            org.quartz.jobs;resolution:=optional,*</Import-Package>
            <Export-Package>org.quartz.*</Export-Package>
            <Private-Package>org.terracotta.quartz.*</Private-Package>
            <Bundle-RequiredExecutionEnvironment>JavaSE-1.6</Bundle-RequiredExecutionEnvironment>
          </instructions>
          <excludeDependencies>*;scope=provided|runtime</excludeDependencies>
        </configuration>
        <executions>
          <execution>
            <id>generate-osgi-headers</id>
            <phase>package</phase>
            <goals>
              <goal>manifest</goal>
            </goals>
          </execution>
        </executions>
      </plugin> 
      <plugin>
        <groupId>org.codehaus.gmaven</groupId>
        <artifactId>gmaven-plugin</artifactId>
        <version>${gmaven-plugin.version}</version>
        <executions>
          <execution>
            <id>repackage-jar</id>
            <phase>package</phase>
            <goals>
              <goal>execute</goal>
            </goals>
            <configuration>
              <source>
                println "repackaging the jar"
                def jarFile = new File(project.build.directory, "${project.artifactId}-${project.version}.jar")
                ant.copy(file: new File(project.build.directory, "dependency-reduced-pom.xml"),
                         tofile: new File(project.build.outputDirectory, "META-INF/maven/${project.groupId}/${project.artifactId}/pom.xml"))
                ant.jar(destfile: jarFile, basedir: new File(project.build.outputDirectory).getAbsolutePath(), manifest: new File(project.build.directory, "MANIFEST.MF"))
              </source>
            </configuration>
          </execution>
        </executions>
      </plugin>       
    </plugins>
  </build>
  
  <repositories>
    <repository>
      <id>terracotta-snapshots</id>
      <url>http://www.terracotta.org/download/reflector/snapshots</url>
    </repository>
    <repository>
      <id>terracotta-releases</id>
      <url>http://www.terracotta.org/download/reflector/releases</url>
    </repository>
  </repositories>

  <pluginRepositories>
    <pluginRepository>
      <id>terracotta-snapshots</id>
      <url>http://www.terracotta.org/download/reflector/snapshots</url>
    </pluginRepository>
    <pluginRepository>
      <id>terracotta-releases</id>
      <url>http://www.terracotta.org/download/reflector/releases</url>
    </pluginRepository>   
  </pluginRepositories>

</project>
