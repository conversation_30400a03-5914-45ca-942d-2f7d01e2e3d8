[{"huodong_id": "10001", "prize": "1000006,200", "target_type": 0, "prize_des": "主角等级到达7级", "small_type": "", "max_num": 1, "id": 2, "target": "10,7"}, {"huodong_id": "10001", "prize": "1000006,300", "target_type": 0, "prize_des": "主角等级到达8级", "small_type": "", "max_num": 1, "id": 3, "target": "10,8"}, {"huodong_id": "10001", "prize": "1000006,400", "target_type": 0, "prize_des": "主角等级到达9级", "small_type": "", "max_num": 1, "id": 4, "target": "10,9"}, {"huodong_id": "10001", "prize": "1000006,400", "target_type": 0, "prize_des": "主角等级到达10级", "small_type": "", "max_num": 1, "id": 5, "target": "10,10"}, {"huodong_id": "10001", "prize": "1000006,500", "target_type": 0, "prize_des": "主角等级到达11级", "small_type": "", "max_num": 1, "id": 6, "target": "10,11"}, {"huodong_id": "10001", "prize": "1000006,500", "target_type": 0, "prize_des": "主角等级到达12级", "small_type": "", "max_num": 1, "id": 7, "target": "10,12"}, {"huodong_id": "10001", "prize": "1000006,600", "target_type": 0, "prize_des": "主角等级到达13级", "small_type": "", "max_num": 1, "id": 8, "target": "10,13"}, {"huodong_id": "10001", "prize": "1000006,700", "target_type": 0, "prize_des": "主角等级到达14级", "small_type": "", "max_num": 1, "id": 9, "target": "10,14"}, {"huodong_id": "2017060701", "prize": "8000002,5;12100006,160;12100000,240;12100030,160;12100024,160", "target_type": 0, "prize_des": "主殿达到19级", "small_type": "", "max_num": 1, "id": 9813, "target": "1,19"}, {"huodong_id": "2017060701", "prize": "8000002,5;12100006,175;12100000,260;12100030,175;12100024,175", "target_type": 0, "prize_des": "主殿达到20级", "small_type": "", "max_num": 1, "id": 9814, "target": "1,20"}, {"huodong_id": "2017060701", "prize": "8000002,5;12100006,200;12100000,280;12100030,200;12100024,200", "target_type": 0, "prize_des": "主殿达到21级", "small_type": "", "max_num": 1, "id": 9815, "target": "1,21"}, {"huodong_id": "2017060701", "prize": "8000002,7;12100006,360;12100000,400;12100030,360;12100024,360", "target_type": 0, "prize_des": "主殿达到22级", "small_type": "", "max_num": 1, "id": 9816, "target": "1,22"}, {"huodong_id": "2017060701", "prize": "8000002,7;12100006,450;12100000,650;12100030,450;12100024,450", "target_type": 0, "prize_des": "主殿达到23级", "small_type": "", "max_num": 1, "id": 9817, "target": "1,23"}, {"huodong_id": "2017060701", "prize": "8000002,7;12100006,700;12100000,900;12100030,700;12100024,700", "target_type": 0, "prize_des": "主殿达到24级", "small_type": "", "max_num": 1, "id": 9818, "target": "1,24"}, {"huodong_id": "2017060701", "prize": "8000002,9;12100006,1200;12100000,1400;12100030,1200;12100024,1200", "target_type": 0, "prize_des": "主殿达到25级", "small_type": "", "max_num": 1, "id": 9819, "target": "1,25"}, {"huodong_id": "201110", "prize": "82001027,5;8000002,5;82001028,5;12102003,5;1000042,500", "target_type": 0, "prize_des": "累计充值达30元", "small_type": "", "max_num": 1, "id": 18050391, "target": "5,30"}, {"huodong_id": "10002", "prize": "1,12100038,1,1000006,50;2,8000034,10,1000006,30", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 11, "target": "11,1"}, {"huodong_id": "10002", "prize": "3,82001026,10,1000006,10;4,82001027,10,1000006,100", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 12, "target": "11,2"}, {"huodong_id": "10002", "prize": "5,8000018,4,1000006,25;6,5100290,1,1000006,980", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 13, "target": "11,3"}, {"huodong_id": "10002", "prize": "7,82000009,20,1000006,10;8,8000010,40,1000006,30", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 14, "target": "11,4"}, {"huodong_id": "10002", "prize": "9,82001027,2,1000006,100;10,82001027,20,1000006,100", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 15, "target": "11,5"}, {"huodong_id": "10002", "prize": "11,12100000,200,1000006,2;12,12100067,100,1000006,30", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 10116, "target": "11,6"}, {"huodong_id": "10002", "prize": "13,8000002,10,1000006,25;14,82010109,50,1000006,60", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 10117, "target": "11,7"}, {"huodong_id": "2018021201", "prize": "82010109,5;8000002,1;82003000,1;12100006,20;12100000,20;12100030,20;12100024,20", "target_type": 0, "prize_des": "第1天", "small_type": "", "max_num": 1, "id": 18042801, "target": "2,1"}, {"huodong_id": "2018021201", "prize": "9000002,1;8000002,1;82003000,1;12100006,20;12100000,20;12100030,20;12100024,20", "target_type": 0, "prize_des": "第2天", "small_type": "", "max_num": 1, "id": 18042802, "target": "2,2"}, {"huodong_id": "2018021201", "prize": "82010109,5;8000002,1;82003000,1;12100006,20;12100000,20;12100030,20;12100024,20", "target_type": 0, "prize_des": "第3天", "small_type": "", "max_num": 1, "id": 18042803, "target": "2,3"}, {"huodong_id": "2018021201", "prize": "82010109,5;8000002,1;82003000,1;12100006,30;12100000,30;12100030,30;12100024,30", "target_type": 0, "prize_des": "第4天", "small_type": "", "max_num": 1, "id": 18042804, "target": "2,4"}, {"huodong_id": "2018021201", "prize": "82010109,5;8000002,1;82003000,1;12100006,30;12100000,30;12100030,30;12100024,30", "target_type": 0, "prize_des": "第5天", "small_type": "", "max_num": 1, "id": 18042805, "target": "2,5"}, {"huodong_id": "2018021201", "prize": "82010109,5;8000002,1;82003000,1;12100006,40;12100000,40;12100030,40;12100024,40", "target_type": 0, "prize_des": "第6天", "small_type": "", "max_num": 1, "id": 18042806, "target": "2,6"}, {"huodong_id": "2018021201", "prize": "82010109,5;8000002,1;82003000,1;12100006,40;12100000,40;12100030,40;12100024,40", "target_type": 0, "prize_des": "第7天", "small_type": "", "max_num": 1, "id": 18042807, "target": "2,7"}, {"huodong_id": "", "prize": "82010109,5;8000002,1;82003000,1;12100006,20;12100000,20;12100030,20;12100024,20", "target_type": 0, "prize_des": "第1天", "small_type": "", "max_num": 1, "id": 2018112801, "target": "2,1"}, {"huodong_id": "", "prize": "82010109,5;8000002,1;82003000,1;12100006,20;12100000,20;12100030,20;12100024,20", "target_type": 0, "prize_des": "第2天", "small_type": "", "max_num": 1, "id": 2018112802, "target": "2,2"}, {"huodong_id": "", "prize": "82010109,5;8000002,1;82003000,1;12100006,20;12100000,20;12100030,20;12100024,20", "target_type": 0, "prize_des": "第3天", "small_type": "", "max_num": 1, "id": 2018112803, "target": "2,3"}, {"huodong_id": "", "prize": "82010109,5;8000002,1;82003000,1;12100006,30;12100000,30;12100030,30;12100024,30", "target_type": 0, "prize_des": "第4天", "small_type": "", "max_num": 1, "id": 2018112804, "target": "2,4"}, {"huodong_id": "", "prize": "82010109,5;8000002,1;82003000,1;12100006,30;12100000,30;12100030,30;12100024,30", "target_type": 0, "prize_des": "第5天", "small_type": "", "max_num": 1, "id": 2018112805, "target": "2,5"}, {"huodong_id": "", "prize": "82010109,5;8000002,1;82003000,1;12100006,40;12100000,40;12100030,40;12100024,40", "target_type": 0, "prize_des": "第6天", "small_type": "", "max_num": 1, "id": 2018112806, "target": "2,6"}, {"huodong_id": "", "prize": "82010109,5;8000002,1;82003000,1;12100006,40;12100000,40;12100030,40;12100024,40", "target_type": 0, "prize_des": "第7天", "small_type": "", "max_num": 1, "id": 2018112807, "target": "2,7"}, {"huodong_id": "201110", "prize": "12102003,4;1000042,100;12100000,10;12100012,10", "target_type": 0, "prize_des": "累计充值达10元", "small_type": "", "max_num": 1, "id": 18042811, "target": "5,10"}, {"huodong_id": "201110", "prize": "12102003,10;1000042,400;12100000,40;12100012,40", "target_type": 0, "prize_des": "累计充值达50元", "small_type": "", "max_num": 1, "id": 18042812, "target": "5,50"}, {"huodong_id": "201110", "prize": "12102003,20;1000042,800;12100000,80;12100012,80", "target_type": 0, "prize_des": "累计充值达100元", "small_type": "", "max_num": 1, "id": 18042813, "target": "5,100"}, {"huodong_id": "201110", "prize": "12102003,40;1000042,1200;12100000,120;12100012,120", "target_type": 0, "prize_des": "累计充值达300元", "small_type": "", "max_num": 1, "id": 18042814, "target": "5,300"}, {"huodong_id": "201110", "prize": "12102003,60;1000042,2000;12100000,200;12100012,200", "target_type": 0, "prize_des": "累计充值达500元", "small_type": "", "max_num": 1, "id": 18042815, "target": "5,500"}, {"huodong_id": "201110", "prize": "5440101,1;12102003,100;1000042,3000;12100000,300;12100012,300", "target_type": 0, "prize_des": "累计充值达1000元", "small_type": "", "max_num": 1, "id": 18042816, "target": "5,1000"}, {"huodong_id": "201110", "prize": "5300107,1;12102003,100;1000042,3000;12100000,300;12100012,300", "target_type": 0, "prize_des": "累计充值达2000元", "small_type": "", "max_num": 1, "id": 18042817, "target": "5,2000"}, {"huodong_id": "", "prize": "1,82001027,1,1000006,150;2,12100067,10,1000006,40;3,8000002,10,1000006,30;4,82005000,50,1000006,168;5,8000079,1,1000006,100;6,8000080,1,1000006,200;7,12000091,10,1000006,80;8,82005001,2000,1000006,1", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 18081001, "target": "9,1"}, {"huodong_id": "", "prize": "1,82007002,20,1000006,25;2,82007003,20,1000006,25;3,82007004,20,1000006,25;4,82007005,20,1000006,25;5,82001027,1,1000006,150;6,12100067,10,1000006,40;7,8000002,10,1000006,30;8,82005000,50,1000006,168;9,8000079,1,1000006,100;10,8000080,1,1000006,200;11,12000091,10,1000006,80;12,82005001,2000,1000006,1", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 18081002, "target": "9,1"}, {"huodong_id": "", "prize": "1,82000120,20,1000006,25;2,82000119,20,1000006,25;3,82000118,20,1000006,25;4,82000117,20,1000006,25;5,82001027,1,1000006,150;6,12100067,10,1000006,40;7,8000002,10,1000006,30;8,82005000,50,1000006,168;9,8000079,1,1000006,100;10,8000080,1,1000006,200;11,12000091,10,1000006,80;12,82005001,2000,1000006,1", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 18081003, "target": "9,1"}, {"huodong_id": "2018021901", "prize": "82003118,20;12100036,1;8000057,1;82001026,1", "target_type": 0, "prize_des": "日盛三伏", "small_type": "", "max_num": 1, "id": 18050301, "target": "1,1"}, {"huodong_id": "", "prize": "82003118,100;12100012,50;8000018,1;8000001,6;12000090,20", "target_type": 0, "prize_des": "今日充值达6元", "small_type": "", "max_num": 1, "id": 18050302, "target": "5,6"}, {"huodong_id": "2018021901", "prize": "82003118,40;12100000,20;12100006,20;12100030,20;12100024,20", "target_type": 0, "prize_des": "进行10次跑商", "small_type": "", "max_num": 1, "id": 18050303, "target": "25,10"}, {"huodong_id": "2018021901", "prize": "82003118,40;12100000,10;12100006,10;12100030,10;12100024,10", "target_type": 0, "prize_des": "拜访世界中的宝箱达10次", "small_type": "", "max_num": 1, "id": 18050304, "target": "17,10"}, {"huodong_id": "", "prize": "82003118,20;12100000,10;12100006,10;12100030,10;12100024,10", "target_type": 0, "prize_des": "击败20个系统部队", "small_type": "", "max_num": 1, "id": 18050305, "target": "14,20"}, {"huodong_id": "", "prize": "82003118,40;12100000,20;12100006,20;12100030,20;12100024,20", "target_type": 0, "prize_des": "击败50个系统部队", "small_type": "", "max_num": 1, "id": 18050306, "target": "14,50"}, {"huodong_id": "2017110901", "prize": "82003118,40;12100000,10;12100006,10;12100030,10;12100024,10", "target_type": 0, "prize_des": "累计消费达300元宝", "small_type": "", "max_num": 1, "id": 18050307, "target": "4,300"}, {"huodong_id": "2017110901", "prize": "82003118,200;12100000,20;12100006,20;12100030,20;12100024,20", "target_type": 0, "prize_des": "累计消费达800元宝", "small_type": "", "max_num": 1, "id": 18050308, "target": "4,800"}, {"huodong_id": "", "prize": "1,12100008,100,82003114,2;2,12100002,100,82003114,2;3,12100032,100,82003114,2;4,12100026,100,82003114,2;5,12100014,100,82003114,2;6,82000009,200,82003114,4;7,12000091,100,82003114,8;8,6100025,300,82003114,2;9,6100022,300,82003114,2;10,6100023,300,82003114,2;11,6100024,300,82003114,2;12,6100021,300,82003114,2;13,82010109,100,82003114,12;14,8000002,200,82003114,4;15,12100067,100,82003114,5;16,12102020,10,82003114,100", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 2018041800, "target": "9,1"}, {"huodong_id": "", "prize": "1000006,5;82000005,1;12100036,1;82001028,1;8000057,1", "target_type": 0, "prize_des": "中元祭祀", "small_type": "", "max_num": 1, "id": 2018041801, "target": "1,1"}, {"huodong_id": "", "prize": "1000006,5;12100000,20;12100006,20;12100030,20;12100024,20", "target_type": 0, "prize_des": "击败20个系统部队", "small_type": "", "max_num": 1, "id": 2018041802, "target": "14,20"}, {"huodong_id": "", "prize": "1000006,5;12100000,20;12100006,20;12100030,20;12100024,20", "target_type": 0, "prize_des": "采集资源负重达360000", "small_type": "", "max_num": 1, "id": 2018041803, "target": "15,360000"}, {"huodong_id": "", "prize": "1000006,15;12100012,20;8000018,1;8000001,6;12000090,20", "target_type": 0, "prize_des": "市场内的神秘商人处购买20次道具", "small_type": "", "max_num": 1, "id": 2018041804, "target": "24,20"}, {"huodong_id": "", "prize": "1000006,10;12100000,20;12100006,20;12100030,20;12100024,20", "target_type": 0, "prize_des": "拜访世界中的宝箱达10次", "small_type": "", "max_num": 1, "id": 2018041805, "target": "17,10"}, {"huodong_id": "", "prize": "1000006,30;12100012,50;8000018,1;8000001,6;12000090,20", "target_type": 0, "prize_des": "今日充值达6元", "small_type": "", "max_num": 1, "id": 2018041806, "target": "5,6"}, {"huodong_id": "", "prize": "1000006,5;12100000,20;12100006,20;12100030,20;12100024,20", "target_type": 0, "prize_des": "训练2000个士兵", "small_type": "", "max_num": 1, "id": 2018041807, "target": "23,2000"}, {"huodong_id": "", "prize": "1000006,20;12100012,30;8000018,1;8000001,6;12000090,20", "target_type": 0, "prize_des": "高级招募达2次", "small_type": "", "max_num": 1, "id": 2018041808, "target": "44,2"}, {"huodong_id": "", "prize": "1000006,5;12100000,20;12100006,20;12100030,20;12100024,20", "target_type": 0, "prize_des": "进行10次跑商", "small_type": "", "max_num": 1, "id": 2018041809, "target": "25,10"}, {"huodong_id": "", "prize": "82003124,20;12100036,1;8000057,1;82001026,1", "target_type": 0, "prize_des": "中秋团圆", "small_type": "", "max_num": 1, "id": 2018092101, "target": "1,1"}, {"huodong_id": "", "prize": "82003124,20;12100012,10;12100006,10;12100030,10;12100024,10", "target_type": 0, "prize_des": "训练2000个士兵", "small_type": "", "max_num": 1, "id": 2018092102, "target": "23,2000"}, {"huodong_id": "", "prize": "82003124,40;12100000,10;12100006,10;12100030,10;12100024,10", "target_type": 0, "prize_des": "进行10次跑商", "small_type": "", "max_num": 1, "id": 2018092103, "target": "25,10"}, {"huodong_id": "", "prize": "82003124,20;12100000,10;12100006,10;12100030,10;12100024,10", "target_type": 0, "prize_des": "拜访世界中的宝箱达10次", "small_type": "", "max_num": 1, "id": 2018092104, "target": "17,10"}, {"huodong_id": "", "prize": "82003124,20;12100000,10;12100006,10;12100030,10;12100024,10", "target_type": 0, "prize_des": "市场内的神秘商人处购买20次道具", "small_type": "", "max_num": 1, "id": 2018092105, "target": "24,20"}, {"huodong_id": "", "prize": "82003124,40;12100000,10;12100006,10;12100030,10;12100024,10", "target_type": 0, "prize_des": "击败20个系统部队", "small_type": "", "max_num": 1, "id": 2018092106, "target": "14,20"}, {"huodong_id": "", "prize": "82003124,20;12100000,10;12100006,10;12100030,10;12100024,10", "target_type": 0, "prize_des": "采集资源负重达360000", "small_type": "", "max_num": 1, "id": 2018092107, "target": "15,360000"}, {"huodong_id": "", "prize": "82003124,100;12100000,10;12100006,10;12100030,10;12100024,10", "target_type": 0, "prize_des": "高级招募达2次", "small_type": "", "max_num": 1, "id": 2018092108, "target": "44,2"}, {"huodong_id": "", "prize": "12102050,2;12100036,1;8000057,1;82001026,1", "target_type": 0, "prize_des": "欢度国庆", "small_type": "", "max_num": 1, "id": 2018100110, "target": "1,1"}, {"huodong_id": "", "prize": "12102050,2;12100000,10;12100006,10;12100030,10;12100024,10", "target_type": 0, "prize_des": "训练2000个士兵", "small_type": "", "max_num": 1, "id": 2018100111, "target": "23,2000"}, {"huodong_id": "", "prize": "12102050,4;12100000,10;12100006,10;12100030,10;12100024,10", "target_type": 0, "prize_des": "进行10次跑商", "small_type": "", "max_num": 1, "id": 2018100112, "target": "25,10"}, {"huodong_id": "", "prize": "12102050,2;12100000,10;12100006,10;12100030,10;12100024,10", "target_type": 0, "prize_des": "拜访世界中的宝箱达10次", "small_type": "", "max_num": 1, "id": 2018100113, "target": "17,10"}, {"huodong_id": "", "prize": "12102050,2;82007002,1;12100006,10;12100030,10;12100024,10", "target_type": 0, "prize_des": "市场内的神秘商人处购买20次道具", "small_type": "", "max_num": 1, "id": 2018100114, "target": "24,20"}, {"huodong_id": "", "prize": "12102050,4;82007003,1;12100006,10;12100030,10;12100024,10", "target_type": 0, "prize_des": "击败20个系统部队", "small_type": "", "max_num": 1, "id": 2018100115, "target": "14,20"}, {"huodong_id": "", "prize": "12102050,2;82007004,1;12100006,10;12100030,10;12100024,10", "target_type": 0, "prize_des": "采集资源负重达360000", "small_type": "", "max_num": 1, "id": 2018100116, "target": "15,360000"}, {"huodong_id": "", "prize": "12102050,10;82007005,1;12100006,10;12100030,10;12100024,10", "target_type": 0, "prize_des": "高级招募达2次", "small_type": "", "max_num": 1, "id": 2018100117, "target": "44,2"}, {"huodong_id": "", "prize": "1000006,5;82000005,1;12100036,1;82001028,1;8000057,1", "target_type": 0, "prize_des": "九九重阳节", "small_type": "", "max_num": 1, "id": 2018101701, "target": "1,1"}, {"huodong_id": "", "prize": "1000006,5;12100000,20;12100006,20;12100030,20;12100024,20", "target_type": 0, "prize_des": "击败20个系统部队", "small_type": "", "max_num": 1, "id": 2018101702, "target": "14,20"}, {"huodong_id": "", "prize": "1000006,5;12100000,20;12100006,20;12100030,20;12100024,20", "target_type": 0, "prize_des": "采集资源负重达360000", "small_type": "", "max_num": 1, "id": 2018101703, "target": "15,360000"}, {"huodong_id": "", "prize": "1000006,15;12100012,20;8000018,1;8000001,6;12000090,20", "target_type": 0, "prize_des": "市场内的神秘商人处购买20次道具", "small_type": "", "max_num": 1, "id": 2018101704, "target": "24,20"}, {"huodong_id": "", "prize": "1000006,10;12100000,20;12100006,20;12100030,20;12100024,20", "target_type": 0, "prize_des": "拜访世界中的宝箱达10次", "small_type": "", "max_num": 1, "id": 2018101705, "target": "17,10"}, {"huodong_id": "", "prize": "1000006,30;12100012,50;8000018,1;8000001,6;12000090,20", "target_type": 0, "prize_des": "今日充值达6元", "small_type": "", "max_num": 1, "id": 2018101706, "target": "5,6"}, {"huodong_id": "", "prize": "1000006,5;12100000,20;12100006,20;12100030,20;12100024,20", "target_type": 0, "prize_des": "训练2000个士兵", "small_type": "", "max_num": 1, "id": 2018101707, "target": "23,2000"}, {"huodong_id": "", "prize": "1000006,20;12100012,30;8000018,1;8000001,6;12000090,20", "target_type": 0, "prize_des": "高级招募达2次", "small_type": "", "max_num": 1, "id": 2018101708, "target": "44,2"}, {"huodong_id": "", "prize": "1000006,5;12100000,20;12100006,20;12100030,20;12100024,20", "target_type": 0, "prize_des": "进行10次跑商", "small_type": "", "max_num": 1, "id": 2018101709, "target": "25,10"}, {"huodong_id": "", "prize": "82003126,20;82000005,1;12100036,1;82001028,1", "target_type": 0, "prize_des": "万圣节狂欢", "small_type": "", "max_num": 1, "id": 2018110130, "target": "1,1"}, {"huodong_id": "", "prize": "82003126,20;12100000,20;12100006,20;12100030,20;12100024,20", "target_type": 0, "prize_des": "训练2000个士兵", "small_type": "", "max_num": 1, "id": 2018110131, "target": "23,2000"}, {"huodong_id": "", "prize": "82003126,40;12100000,20;12100006,20;12100030,20;12100024,20", "target_type": 0, "prize_des": "进行10次跑商", "small_type": "", "max_num": 1, "id": 2018110132, "target": "25,10"}, {"huodong_id": "", "prize": "82003126,20;12100012,20;8000018,1;8000001,6;12000090,20", "target_type": 0, "prize_des": "拜访世界中的宝箱达10次", "small_type": "", "max_num": 1, "id": 2018110133, "target": "17,10"}, {"huodong_id": "", "prize": "82003126,20;12100000,20;12100006,20;12100030,20;12100024,20", "target_type": 0, "prize_des": "市场内的神秘商人处购买20次道具", "small_type": "", "max_num": 1, "id": 2018110134, "target": "24,20"}, {"huodong_id": "", "prize": "82003126,40;12100012,50;8000018,1;8000001,6;12000090,20", "target_type": 0, "prize_des": "击败20个系统部队", "small_type": "", "max_num": 1, "id": 2018110135, "target": "14,20"}, {"huodong_id": "", "prize": "82003126,20;12100000,20;12100006,20;12100030,20;12100024,20", "target_type": 0, "prize_des": "采集资源负重达360000", "small_type": "", "max_num": 1, "id": 2018110136, "target": "15,360000"}, {"huodong_id": "", "prize": "82003126,100;12100012,30;8000018,1;8000001,6;12000090,20", "target_type": 0, "prize_des": "高级招募达2次", "small_type": "", "max_num": 1, "id": 2018110137, "target": "44,2"}, {"huodong_id": "", "prize": "12102051,2;82000005,1;12100036,1;82001028,1", "target_type": 0, "prize_des": "周年庆有礼", "small_type": "", "max_num": 1, "id": 2018112830, "target": "1,1"}, {"huodong_id": "", "prize": "12102051,2;12100000,20;12100006,20;12100030,20;12100024,20", "target_type": 0, "prize_des": "训练2000个士兵", "small_type": "", "max_num": 1, "id": 2018112831, "target": "23,2000"}, {"huodong_id": "", "prize": "12102051,4;12100000,20;12100006,20;12100030,20;12100024,20", "target_type": 0, "prize_des": "进行10次跑商", "small_type": "", "max_num": 1, "id": 2018112832, "target": "25,10"}, {"huodong_id": "", "prize": "12102051,2;12100012,20;8000018,1;8000001,6;12000090,20", "target_type": 0, "prize_des": "拜访世界中的宝箱达10次", "small_type": "", "max_num": 1, "id": 2018112833, "target": "17,10"}, {"huodong_id": "", "prize": "12102051,2;12100000,20;12100006,20;12100030,20;12100024,20", "target_type": 0, "prize_des": "市场内的神秘商人处购买20次道具", "small_type": "", "max_num": 1, "id": 2018112834, "target": "24,20"}, {"huodong_id": "", "prize": "12102051,4;12100012,50;8000018,1;8000001,6;12000090,20", "target_type": 0, "prize_des": "击败20个系统部队", "small_type": "", "max_num": 1, "id": 2018112835, "target": "14,20"}, {"huodong_id": "", "prize": "12102051,2;12100000,20;12100006,20;12100030,20;12100024,20", "target_type": 0, "prize_des": "采集资源负重达360000", "small_type": "", "max_num": 1, "id": 2018112836, "target": "15,360000"}, {"huodong_id": "", "prize": "12102051,10;12100012,30;8000018,1;8000001,6;12000090,20", "target_type": 0, "prize_des": "高级招募达2次", "small_type": "", "max_num": 1, "id": 2018112837, "target": "44,2"}, {"huodong_id": "", "prize": "1,5300100,5,82006050,1;2,5300102,5,82006050,1;3,5300104,5,82006050,1;4,5300106,5,82006050,1;5,5300108,5,82006050,1;6,5300110,5,82006050,1;7,5300101,5,82006050,1;8,5300103,5,82006050,1;9,5300105,5,82006050,1;10,5300107,5,82006050,1;11,5300109,5,82006050,1;12,5300111,5,82006050,1", "target_type": 0, "prize_des": "周年庆商城", "small_type": "", "max_num": 1, "id": 2018112840, "target": "9,1"}, {"huodong_id": "", "prize": "82001020,10;82001028,1;12100006,5;12100000,5", "target_type": 0, "prize_des": "万圣节大狂欢！", "small_type": "", "max_num": 1, "id": 2018110140, "target": "1,1"}, {"huodong_id": "", "prize": "1,12000091,20,82003126,80;2,12100067,10,82003126,40;3,82001027,3,82003126,200;4,82010109,50,82003126,120;5,82005000,20,82003126,168;6,82005001,2000,82003126,1;7,1000043,2000,82003126,1;8,6100016,50,82003126,5;9,6100019,50,82003126,5;10,6100017,50,82003126,5;11,6100020,50,82003126,5;12,6100018,50,82003126,5;13,8000001,20,82003126,2;14,12100014,10,82003126,30;15,12100008,10,82003126,30;16,12100002,10,82003126,30;17,12100032,10,82003126,30;18,12100026,10,82003126,30", "target_type": 0, "prize_des": "万圣节商城", "small_type": "", "max_num": 1, "id": 2018110120, "target": "9,1"}, {"huodong_id": "", "prize": "82001020,10;82001028,1;12100006,5;12100000,5", "target_type": 0, "prize_des": "祝大家国庆节快乐！", "small_type": "", "max_num": 1, "id": 2018100101, "target": "1,1"}, {"huodong_id": "", "prize": "82001020,10;82001028,1;12100006,5;12100000,5", "target_type": 0, "prize_des": "祝大家国庆节快乐！", "small_type": "", "max_num": 1, "id": 2018100102, "target": "1,1"}, {"huodong_id": "", "prize": "82001020,10;82001028,3;12100006,10;12100000,10", "target_type": 0, "prize_des": "喜迎汉王周年庆！", "small_type": "", "max_num": 1, "id": 2018112811, "target": "1,1"}, {"huodong_id": "", "prize": "82001020,10;82001028,3;12100006,10;12100000,10", "target_type": 0, "prize_des": "喜迎汉王周年庆！", "small_type": "", "max_num": 1, "id": 2018112812, "target": "1,1"}, {"huodong_id": "2017040303", "prize": "12410018,1;12100000,10;12100012,10", "target_type": 0, "prize_des": "今日充值达6元", "small_type": "", "max_num": 1, "id": 2018032900, "target": "5,6"}, {"huodong_id": "20180329", "prize": "1,82010109,20,1000006,100;2,82000009,50,1000006,20;3,12100067,10,1000006,50;4,8000002,30,1000006,25;5,12000090,500,1000006,10;6,12102020,1,1000006,1000;7,6100017,100,1000006,3;8,6100020,100,1000006,3;9,6100019,100,1000006,3;10,6100018,100,1000006,3;11,6100016,100,1000006,3;12,6102040,50,1000006,4", "target_type": 0, "prize_des": "每日限购", "small_type": "", "max_num": 1, "id": 2018032901, "target": "9,1"}, {"huodong_id": "20180519", "prize": "1,82010109,20,1000006,100;2,82000009,50,1000006,20;3,12100067,10,1000006,30;4,8000002,30,1000006,25;5,12000090,500,1000006,10;6,12102020,1,1000006,1000;7,6100017,100,1000006,3;8,6100020,100,1000006,3;9,6100019,100,1000006,3;10,6100018,100,1000006,3;11,6100016,100,1000006,3;12,82001027,2,1000006,160", "target_type": 0, "prize_des": "每日限购", "small_type": "", "max_num": 1, "id": 2018032902, "target": "9,1"}, {"huodong_id": "", "prize": "1,12100015,500,6102040,60;2,12100003,500,6102040,60;3,12100027,500,6102040,60;4,12100015,100,82001040,50;5,12100003,100,82001040,50;6,12100027,100,82001040,50;7,12100015,100,82000005,25;8,12100003,100,82000005,25;9,12100027,100,82000005,25;10,12100015,100,8000059,5;11,12100003,100,8000059,5;12,12100027,100,8000059,5;13,12100015,100,8000057,15;14,12100003,100,8000057,15;15,12100027,100,8000057,15;16,12100069,200,82001026,20", "target_type": 0, "prize_des": "回收商城", "small_type": "", "max_num": 1, "id": 2018110199, "target": "9,1"}, {"huodong_id": "20180519", "prize": "1,12000091,50,82003124,80;2,12100067,20,82003124,40;3,82001027,5,82003124,200;4,82010109,20,82003124,120;5,82001029,3,82003124,300;6,82000011,1,82003124,300;7,82005000,20,82003124,168;8,82005001,2000,82003124,1;9,1000043,2000,82003124,1;10,12102003,50,82003124,20;11,8000001,50,82003124,2;12,12100014,20,82003124,30;13,12100008,20,82003124,30;14,12100002,20,82003124,30;15,12100032,20,82003124,30;16,12100026,20,82003124,30", "target_type": 0, "prize_des": "中秋商城", "small_type": "", "max_num": 1, "id": 2018092140, "target": "9,1"}, {"huodong_id": "", "prize": "82001020,10;82001028,1;12100006,5;12100000,5", "target_type": 0, "prize_des": "祝大家中秋节快乐！", "small_type": "", "max_num": 1, "id": 2018092141, "target": "1,1"}, {"huodong_id": "", "prize": "82001020,10;82001028,1;12100006,5;12100000,5", "target_type": 0, "prize_des": "武将福利", "small_type": "", "max_num": 1, "id": 2018070659, "target": "1,1"}, {"huodong_id": "2017110901", "prize": "82005001,50;82001020,4", "target_type": 0, "prize_des": "累计消费达1元宝", "small_type": "", "max_num": 1, "id": 2018082201, "target": "4,1"}, {"huodong_id": "2017110901", "prize": "82005001,100;8000002,2;12100000,20;12100012,20;12100024,20", "target_type": 0, "prize_des": "累计消费达300元宝", "small_type": "", "max_num": 1, "id": 2018082202, "target": "4,300"}, {"huodong_id": "2017110901", "prize": "82005001,150;8000002,3;12100000,40;12100012,40;12100024,40", "target_type": 0, "prize_des": "累计消费达750元宝", "small_type": "", "max_num": 1, "id": 2018082203, "target": "4,750"}, {"huodong_id": "2017110901", "prize": "82005001,250;8000002,5;12100000,120;12100012,120;12100024,120", "target_type": 0, "prize_des": "累计消费达1500元宝", "small_type": "", "max_num": 1, "id": 2018082204, "target": "4,1500"}, {"huodong_id": "2017110901", "prize": "82005001,500;8000002,10;12100000,160;12100012,160;12100024,160", "target_type": 0, "prize_des": "累计消费达3000元宝", "small_type": "", "max_num": 1, "id": 2018082205, "target": "4,3000"}, {"huodong_id": "2017110901", "prize": "82005001,1000;8000002,20;12100000,240;12100012,240;12100024,240", "target_type": 0, "prize_des": "累计消费达6000元宝", "small_type": "", "max_num": 1, "id": 2018082206, "target": "4,6000"}, {"huodong_id": "2017110901", "prize": "82005000,15;8000002,26;12100000,400;12100012,400;12100024,400", "target_type": 0, "prize_des": "累计消费达10000元宝", "small_type": "", "max_num": 1, "id": 2018082207, "target": "4,10000"}, {"huodong_id": "2017110901", "prize": "82005000,40;8000002,100;12100000,800;12100012,800;12100024,800", "target_type": 0, "prize_des": "累计消费达25000元宝", "small_type": "", "max_num": 1, "id": 2018082208, "target": "4,25000"}, {"huodong_id": "201110", "prize": "9000007,1", "target_type": 0, "prize_des": "累计充值达1元", "small_type": "", "max_num": 1, "id": 2018282601, "target": "5,1"}, {"huodong_id": "201110", "prize": "8000002,8;82010109,4;1000043,200;12100012,40", "target_type": 0, "prize_des": "累计充值达88元", "small_type": "", "max_num": 1, "id": 2018282602, "target": "5,88"}, {"huodong_id": "201110", "prize": "8000002,12;82010109,6;1000043,240;12100012,60", "target_type": 0, "prize_des": "累计充值达188元", "small_type": "", "max_num": 1, "id": 2018282603, "target": "5,188"}, {"huodong_id": "201110", "prize": "8000002,18;82010109,8;1000043,454;12100012,90", "target_type": 0, "prize_des": "累计充值达388元", "small_type": "", "max_num": 1, "id": 2018282604, "target": "5,388"}, {"huodong_id": "201110", "prize": "8000002,45;82005000,22;1000043,1136;12100012,227", "target_type": 0, "prize_des": "累计充值达888元", "small_type": "", "max_num": 1, "id": 2018282605, "target": "5,888"}, {"huodong_id": "201110", "prize": "5300109,1;82005000,45;1000043,2272;12100012,454", "target_type": 0, "prize_des": "累计充值达1888元", "small_type": "", "max_num": 1, "id": 2018282606, "target": "5,1888"}, {"huodong_id": "201110", "prize": "5100310,1;82005000,60;1000043,4545;12100012,909", "target_type": 0, "prize_des": "累计充值达3888元", "small_type": "", "max_num": 1, "id": 2018282607, "target": "5,3888"}, {"huodong_id": "2018021203", "prize": "1,12000091,20,82003126,80;2,12100067,10,82003126,40", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 201908, "target": "9,1"}, {"huodong_id": "2018021203", "prize": "3,82001027,3,82003126,200;4,82010109,50,82003126,120", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 201909, "target": "9,1"}, {"huodong_id": "2018021203", "prize": "5,82005000,20,82003126,168;6,82005001,2000,82003126,1", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 201910, "target": "9,1"}, {"huodong_id": "2018021203", "prize": "7,1000043,2000,82003126,1;8,6100016,50,82003126,5", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 201911, "target": "9,1"}, {"huodong_id": "2018021203", "prize": "9,6100019,50,82003126,5;10,6100017,50,82003126,5", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 201912, "target": "9,1"}, {"huodong_id": "2018021203", "prize": "11,6100020,50,82003126,5;12,6100018,50,82003126,5", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 201913, "target": "9,1"}, {"huodong_id": "2018021203", "prize": "13,8000001,20,82003126,2;14,12100014,10,82003126,30", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 201914, "target": "9,1"}, {"huodong_id": "2018021203", "prize": "15,12100008,10,82003126,30;16,12100002,10,82003126,30", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 201915, "target": "9,1"}, {"huodong_id": "2018021203", "prize": "17,12100032,10,82003126,30;18,12100026,10,82003126,30", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 201916, "target": "9,1"}, {"huodong_id": "2018021203", "prize": "1,12100015,500,6102040,60;2,12100003,500,6102040,60", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 201918, "target": "9,1"}, {"huodong_id": "2018021203", "prize": "3,12100027,500,6102040,60;4,12100015,100,82001040,50", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 201919, "target": "9,1"}, {"huodong_id": "2018021203", "prize": "5,12100003,100,82001040,50;6,12100027,100,82001040,50", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 201920, "target": "9,1"}, {"huodong_id": "2018021203", "prize": "7,12100015,100,82000005,25;8,12100003,100,82000005,25", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 201921, "target": "9,1"}, {"huodong_id": "2018021203", "prize": "9,12100027,100,82000005,25;10,12100015,100,8000059,5", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 201922, "target": "9,1"}, {"huodong_id": "2018021203", "prize": "11,12100003,100,8000059,5;12,12100027,100,8000059,5", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 201923, "target": "9,1"}, {"huodong_id": "2018021203", "prize": "13,12100015,100,8000057,15;14,12100003,100,8000057,15", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 201924, "target": "9,1"}, {"huodong_id": "2018021203", "prize": "15,12100027,100,8000057,15;16,12100069,200,82001026,20", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 201925, "target": "9,1"}, {"huodong_id": "2017010101", "prize": "12100006,6;12100000,6", "target_type": 0, "prize_des": "主殿到达9级", "small_type": "", "max_num": 1, "id": 21, "target": "1,9"}, {"huodong_id": "2017010101", "prize": "12100006,8;12100000,8;8000017,8", "target_type": 0, "prize_des": "主殿到达12级", "small_type": "", "max_num": 1, "id": 22, "target": "1,12"}, {"huodong_id": "2017010102", "prize": "12100006,10;12100000,10", "target_type": 0, "prize_des": "拥有20个武将", "small_type": "", "max_num": 1, "id": 23, "target": "7,20"}, {"huodong_id": "2017010102", "prize": "12100006,12;12100000,12;8000017,12", "target_type": 0, "prize_des": "全服累计招募武将数量达10000", "small_type": "", "max_num": 1, "id": 24, "target": "40,10000"}, {"huodong_id": "2017010103", "prize": "12100006,14;12100000,14", "target_type": 0, "prize_des": "采集180000负重的资源", "small_type": "", "max_num": 1, "id": 25, "target": "15,180000"}, {"huodong_id": "2017010103", "prize": "12100006,16;12100000,16;8000017,16", "target_type": 0, "prize_des": "全服累计采集资源总负重达2.5亿", "small_type": "", "max_num": 1, "id": 26, "target": "41,250000000"}, {"huodong_id": "2017010104", "prize": "12100006,18;12100000,18", "target_type": 0, "prize_des": "训练3000个士兵", "small_type": "", "max_num": 1, "id": 27, "target": "23,3000"}, {"huodong_id": "2017010104", "prize": "12100006,20;12100000,20;8000017,20", "target_type": 0, "prize_des": "全服累计训练士兵达500000", "small_type": "", "max_num": 1, "id": 28, "target": "42,500000"}, {"huodong_id": "2017010105", "prize": "1000006,50;1000026,500;12100066,10;8000018,2", "target_type": 0, "prize_des": "[FFF7D8FF]击败所有倭人，收复夷州！当前剩余[FF9C10FF]{0}[-][-]", "small_type": "", "max_num": 1, "id": 29, "target": "43,500"}, {"huodong_id": "2017010201", "prize": "1000006,25;12100006,10;12100000,10", "target_type": 0, "prize_des": "主殿达到5级", "small_type": "", "max_num": 1, "id": 31, "target": "1,5"}, {"huodong_id": "2017010201", "prize": "1000006,50;12100006,20;12100000,20;12100030,20", "target_type": 0, "prize_des": "主殿达到10级", "small_type": "", "max_num": 1, "id": 32, "target": "1,10"}, {"huodong_id": "2017010201", "prize": "1000006,100;12100006,30;12100000,30;12100030,30;12100024,30", "target_type": 0, "prize_des": "主殿达到15级", "small_type": "", "max_num": 1, "id": 33, "target": "1,15"}, {"huodong_id": "2017010201", "prize": "1000006,150;12100006,40;12100000,40;12100030,40;12100024,40", "target_type": 0, "prize_des": "主殿达到20级", "small_type": "", "max_num": 1, "id": 34, "target": "1,20"}, {"huodong_id": "2017010201", "prize": "1000006,200;12100006,50;12100000,50;12100030,50;12100024,50", "target_type": 0, "prize_des": "主殿达到22级", "small_type": "", "max_num": 1, "id": 35, "target": "1,22"}, {"huodong_id": "2017010201", "prize": "1000006,300;12100006,60;12100000,60;12100030,60;12100024,60", "target_type": 0, "prize_des": "主殿达到24级", "small_type": "", "max_num": 1, "id": 36, "target": "1,24"}, {"huodong_id": "2017010201", "prize": "1000006,400;12100006,70;12100000,70;12100030,70;12100024,70", "target_type": 0, "prize_des": "主殿达到26级", "small_type": "", "max_num": 1, "id": 1037, "target": "1,26"}, {"huodong_id": "2017010201", "prize": "1000006,500;12100006,80;12100000,80;12100030,80;12100024,80", "target_type": 0, "prize_des": "主殿达到28级", "small_type": "", "max_num": 1, "id": 1038, "target": "1,28"}, {"huodong_id": "2017010201", "prize": "1000006,600;12100006,90;12100000,90;12100030,90;12100024,90", "target_type": 0, "prize_des": "主殿达到30级", "small_type": "", "max_num": 1, "id": 1039, "target": "1,30"}, {"huodong_id": "2017010301", "prize": "12102001,6;12100006,4;12100000,4", "target_type": 0, "prize_des": "累计拥有1个武将", "small_type": "", "max_num": 1, "id": 41, "target": "7,1"}, {"huodong_id": "2017010301", "prize": "8000002,1;12102001,8;82001028,2", "target_type": 0, "prize_des": "累计拥有5个武将", "small_type": "", "max_num": 1, "id": 42, "target": "7,5"}, {"huodong_id": "2017010301", "prize": "1000006,100;82001028,4;1000026,500;12102002,8", "target_type": 0, "prize_des": "累计拥有10个武将", "small_type": "", "max_num": 1, "id": 43, "target": "7,10"}, {"huodong_id": "2017010301", "prize": "1000006,100;82001028,4;1000026,500;12102002,8", "target_type": 0, "prize_des": "累计拥有20个武将", "small_type": "", "max_num": 1, "id": 44, "target": "7,20"}, {"huodong_id": "2017010301", "prize": "1000006,200;82001028,6;1000026,750;12102002,12", "target_type": 0, "prize_des": "累计拥有30个武将", "small_type": "", "max_num": 1, "id": 45, "target": "7,30"}, {"huodong_id": "2017010301", "prize": "1000006,200;82001028,6;1000026,750;12102002,12", "target_type": 0, "prize_des": "累计拥有40个武将", "small_type": "", "max_num": 1, "id": 46, "target": "7,40"}, {"huodong_id": "2017010301", "prize": "1000006,350;82001028,8;1000026,1000;12102003,6", "target_type": 0, "prize_des": "累计拥有50个武将", "small_type": "", "max_num": 1, "id": 47, "target": "7,50"}, {"huodong_id": "2017010301", "prize": "1000006,350;82001028,8;1000026,1000;12102003,6", "target_type": 0, "prize_des": "累计拥有60个武将", "small_type": "", "max_num": 1, "id": 48, "target": "7,60"}, {"huodong_id": "2017010301", "prize": "1000006,500;82001028,10;1000026,1500;12102003,10", "target_type": 0, "prize_des": "累计拥有70个武将", "small_type": "", "max_num": 1, "id": 49, "target": "7,70"}, {"huodong_id": "2017010301", "prize": "1000006,500;82001028,10;1000026,1500;12102003,10", "target_type": 0, "prize_des": "累计拥有80个武将", "small_type": "", "max_num": 1, "id": 50, "target": "7,80"}, {"huodong_id": "2017010301", "prize": "1000006,500;82001028,10;1000026,1500;12102003,10", "target_type": 0, "prize_des": "累计拥有90个武将", "small_type": "", "max_num": 1, "id": 51, "target": "7,90"}, {"huodong_id": "2017010402", "prize": "9000007,1", "target_type": 0, "prize_des": "累计充值达1元", "small_type": "", "max_num": 1, "id": 513, "target": "5,1"}, {"huodong_id": "2017010402", "prize": "5100150,1;8000061,4;8000062,4;82003013,10", "target_type": 0, "prize_des": "累计充值达88元", "small_type": "", "max_num": 1, "id": 514, "target": "5,88"}, {"huodong_id": "2017010402", "prize": "8000002,5;12100006,40;12100000,40;8000063,8", "target_type": 0, "prize_des": "累计充值达188元", "small_type": "", "max_num": 1, "id": 515, "target": "5,188"}, {"huodong_id": "2017010402", "prize": "8000002,10;12100030,60;12100012,60;8000057,2", "target_type": 0, "prize_des": "累计充值达388元", "small_type": "", "max_num": 1, "id": 516, "target": "5,388"}, {"huodong_id": "2017010402", "prize": "5100160,1;12100006,80;12100000,80;8000059,4;8000057,4", "target_type": 0, "prize_des": "累计充值达888元", "small_type": "", "max_num": 1, "id": 517, "target": "5,888"}, {"huodong_id": "2017010402", "prize": "8000002,20;12100030,100;12100012,100;8000060,4;8000058,4", "target_type": 0, "prize_des": "累计充值达1888元", "small_type": "", "max_num": 1, "id": 518, "target": "5,1888"}, {"huodong_id": "2017010402", "prize": "5100310,1;8000026,10;8000010,10;8000034,10;8000018,10", "target_type": 0, "prize_des": "累计充值达3888元", "small_type": "", "max_num": 1, "id": 519, "target": "5,3888"}, {"huodong_id": "2017010402", "prize": "9000008,1;12220049,1", "target_type": 0, "prize_des": "累计充值达8888元", "small_type": "", "max_num": 1, "id": 520, "target": "5,8888"}, {"huodong_id": "2017010501", "prize": "12100006,10;12100000,10;8000057,4;12100037,3", "target_type": 0, "prize_des": "第1天", "small_type": "", "max_num": 1, "id": 63, "target": "2,1"}, {"huodong_id": "2017010501", "prize": "12201034,1;12100012,10;8000057,2;12100037,2", "target_type": 0, "prize_des": "第2天", "small_type": "", "max_num": 1, "id": 64, "target": "2,2"}, {"huodong_id": "2017010501", "prize": "82010006,10;1000026,500;8000057,2;12100037,2", "target_type": 0, "prize_des": "第3天", "small_type": "", "max_num": 1, "id": 65, "target": "2,3"}, {"huodong_id": "2017010501", "prize": "82010006,20;12100006,10;12100000,10;12100037,2", "target_type": 0, "prize_des": "第4天", "small_type": "", "max_num": 1, "id": 66, "target": "2,4"}, {"huodong_id": "2017010501", "prize": "82010006,30;12100012,10;12100030,10;12100037,2", "target_type": 0, "prize_des": "第5天", "small_type": "", "max_num": 1, "id": 67, "target": "2,5"}, {"huodong_id": "2017010501", "prize": "82010006,30;12100006,20;8000025,12;12100037,2", "target_type": 0, "prize_des": "第6天", "small_type": "", "max_num": 1, "id": 68, "target": "2,6"}, {"huodong_id": "2017010501", "prize": "12201036,1;1000006,50;8000058,1;8000059,1", "target_type": 0, "prize_des": "第7天", "small_type": "", "max_num": 1, "id": 69, "target": "2,7"}, {"huodong_id": "2017010501", "prize": "8000066,1;12100006,20;8000061,2;12100037,2", "target_type": 0, "prize_des": "第8天", "small_type": "", "max_num": 1, "id": 1070, "target": "2,8"}, {"huodong_id": "2017010501", "prize": "82010008,10;12100000,20;8000062,2;12100037,2", "target_type": 0, "prize_des": "第9天", "small_type": "", "max_num": 1, "id": 1071, "target": "2,9"}, {"huodong_id": "2017010501", "prize": "8000065,1;12100030,20;8000063,2;12100037,2", "target_type": 0, "prize_des": "第10天", "small_type": "", "max_num": 1, "id": 1072, "target": "2,10"}, {"huodong_id": "2017010501", "prize": "82010008,30;82000010,1;8000001,12;12100037,2", "target_type": 0, "prize_des": "第11天", "small_type": "", "max_num": 1, "id": 1073, "target": "2,11"}, {"huodong_id": "2017010501", "prize": "8000066,1;12100006,20;8000061,2;12100037,2", "target_type": 0, "prize_des": "第12天", "small_type": "", "max_num": 1, "id": 1074, "target": "2,12"}, {"huodong_id": "2017010501", "prize": "8000065,1;12100000,20;8000062,2;12100037,2", "target_type": 0, "prize_des": "第13天", "small_type": "", "max_num": 1, "id": 1075, "target": "2,13"}, {"huodong_id": "2017010501", "prize": "8000060,1;12100030,20;8000063,2;12100037,2", "target_type": 0, "prize_des": "第14天", "small_type": "", "max_num": 1, "id": 1076, "target": "2,14"}, {"huodong_id": "2017010501", "prize": "82010008,50;82001030,1;12100024,40;8000001,12", "target_type": 0, "prize_des": "第15天", "small_type": "", "max_num": 1, "id": 1077, "target": "2,15"}, {"huodong_id": "", "prize": "82010057,1;12100037,1;82001020,4", "target_type": 0, "prize_des": "开服庆典第1天", "small_type": "1,征战天下", "max_num": 1, "id": 100000, "target": "1,1"}, {"huodong_id": "", "prize": "82010057,1;82001020,4;12100000,2;12100006,2;82001040,4", "target_type": 0, "prize_des": "击败5个系统部队", "small_type": "1,征战天下", "max_num": 1, "id": 100010, "target": "14,5"}, {"huodong_id": "", "prize": "82010057,1;82001020,6;12100000,4;12100006,4;82001040,4", "target_type": 0, "prize_des": "击败10个系统部队", "small_type": "1,征战天下", "max_num": 1, "id": 100020, "target": "14,10"}, {"huodong_id": "", "prize": "82010057,1;82001020,8;12100000,6;12100006,6;82001040,4", "target_type": 0, "prize_des": "击败15个系统部队", "small_type": "1,征战天下", "max_num": 1, "id": 100030, "target": "14,15"}, {"huodong_id": "", "prize": "82010057,1;82001020,10;12100000,8;12100006,8;82001040,4", "target_type": 0, "prize_des": "击败20个系统部队", "small_type": "1,征战天下", "max_num": 1, "id": 100040, "target": "14,20"}, {"huodong_id": "", "prize": "82010057,1;82001020,12;12100000,10;12100006,10;82001040,4", "target_type": 0, "prize_des": "击败30个系统部队", "small_type": "1,征战天下", "max_num": 1, "id": 100050, "target": "14,30"}, {"huodong_id": "", "prize": "82010057,1;8000009,4;12100006,2;12100000,2;12100012,2;82001040,4", "target_type": 0, "prize_des": "演武通关5个关卡", "small_type": "2,通关演武", "max_num": 1, "id": 100060, "target": "28,5"}, {"huodong_id": "", "prize": "82010057,1;8000009,6;12100006,4;12100000,4;12100012,4;82001040,4", "target_type": 0, "prize_des": "演武通关10个关卡", "small_type": "2,通关演武", "max_num": 1, "id": 100070, "target": "28,10"}, {"huodong_id": "", "prize": "82010057,1;8000009,8;12100006,6;12100000,6;12100012,6;82001040,4", "target_type": 0, "prize_des": "演武通关20个关卡", "small_type": "2,通关演武", "max_num": 1, "id": 100080, "target": "28,20"}, {"huodong_id": "", "prize": "82010057,1;8000009,10;12100006,8;12100000,8;12100012,8;82001040,4", "target_type": 0, "prize_des": "演武通关30个关卡", "small_type": "2,通关演武", "max_num": 1, "id": 100090, "target": "28,30"}, {"huodong_id": "", "prize": "82010057,1;8000009,12;12100006,10;12100000,10;12100012,10;82001040,4", "target_type": 0, "prize_des": "演武通关40个关卡", "small_type": "2,通关演武", "max_num": 1, "id": 100100, "target": "28,40"}, {"huodong_id": "", "prize": "82010057,1;12100006,4;12100000,4;12100030,4;82001040,4", "target_type": 0, "prize_des": "采集资源负重达36000", "small_type": "3,采集资源", "max_num": 1, "id": 100110, "target": "15,36000"}, {"huodong_id": "", "prize": "82010057,1;12100006,6;12100000,6;12100030,6;82001040,4", "target_type": 0, "prize_des": "采集资源负重达72000", "small_type": "3,采集资源", "max_num": 1, "id": 100120, "target": "15,72000"}, {"huodong_id": "", "prize": "82010057,1;12100006,8;12100000,8;12100030,8;82001040,4", "target_type": 0, "prize_des": "采集资源负重达144000", "small_type": "3,采集资源", "max_num": 1, "id": 100130, "target": "15,144000"}, {"huodong_id": "", "prize": "82010057,1;12100006,10;12100000,10;12100030,10;82001040,4", "target_type": 0, "prize_des": "采集资源负重达540000", "small_type": "3,采集资源", "max_num": 1, "id": 100140, "target": "15,540000"}, {"huodong_id": "", "prize": "82010057,1;12100006,12;12100000,12;12100030,12;82001040,4", "target_type": 0, "prize_des": "采集资源负重达1080000", "small_type": "3,采集资源", "max_num": 1, "id": 100150, "target": "15,1080000"}, {"huodong_id": "", "prize": "82010057,1;12100037,1;82001020,4", "target_type": 0, "prize_des": "开服庆典第2天", "small_type": "1,训练士兵", "max_num": 1, "id": 100160, "target": "1,1"}, {"huodong_id": "", "prize": "82010057,1;8000001,2;12100000,2;12100030,2;82001040,4", "target_type": 0, "prize_des": "训练200个士兵", "small_type": "1,训练士兵", "max_num": 1, "id": 100170, "target": "23,200"}, {"huodong_id": "", "prize": "82010057,1;8000001,4;12100000,4;12100030,4;82001040,4", "target_type": 0, "prize_des": "训练600个士兵", "small_type": "1,训练士兵", "max_num": 1, "id": 100180, "target": "23,600"}, {"huodong_id": "", "prize": "82010057,1;8000001,6;12100000,6;12100030,6;82001040,4", "target_type": 0, "prize_des": "训练1000个士兵", "small_type": "1,训练士兵", "max_num": 1, "id": 100190, "target": "23,1000"}, {"huodong_id": "", "prize": "82010057,1;8000001,8;12100000,8;12100030,8;82001040,4", "target_type": 0, "prize_des": "训练2000个士兵", "small_type": "1,训练士兵", "max_num": 1, "id": 100200, "target": "23,2000"}, {"huodong_id": "", "prize": "82010057,1;8000001,10;12100000,10;12100030,10;82001040,4", "target_type": 0, "prize_des": "训练3000个士兵", "small_type": "1,训练士兵", "max_num": 1, "id": 100210, "target": "23,3000"}, {"huodong_id": "", "prize": "82010057,1;8000033,4;12100006,2;12100012,4;12100000,2;82001040,4", "target_type": 0, "prize_des": "活动开启后累计研究任意2个科技", "small_type": "2,研究科技", "max_num": 1, "id": 100220, "target": "31,2"}, {"huodong_id": "", "prize": "82010057,1;8000033,6;12100006,4;12100012,6;12100000,4;82001040,4", "target_type": 0, "prize_des": "活动开启后累计研究任意4个科技", "small_type": "2,研究科技", "max_num": 1, "id": 100230, "target": "31,4"}, {"huodong_id": "", "prize": "82010057,1;8000033,8;12100006,6;12100012,8;12100000,6;82001040,4", "target_type": 0, "prize_des": "活动开启后累计研究任意6个科技", "small_type": "2,研究科技", "max_num": 1, "id": 100240, "target": "31,6"}, {"huodong_id": "", "prize": "82010057,1;8000033,10;12100006,8;12100012,10;12100000,8;82001040,4", "target_type": 0, "prize_des": "累计研究院到7级", "small_type": "2,研究科技", "max_num": 1, "id": 100250, "target": "27,7"}, {"huodong_id": "", "prize": "82010057,1;8000033,12;12100006,10;12100012,12;12100000,10;82001040,4", "target_type": 0, "prize_des": "研究院到10级", "small_type": "2,研究科技", "max_num": 1, "id": 100260, "target": "27,10"}, {"huodong_id": "", "prize": "82010057,1;8000061,2;12100000,2;12100030,2;82001040,4", "target_type": 0, "prize_des": "获得40000点势力积分", "small_type": "3,势力贡献", "max_num": 1, "id": 100270, "target": "30,40000"}, {"huodong_id": "", "prize": "82010057,1;8000061,2;12100000,4;12100030,4;82001040,4", "target_type": 0, "prize_des": "获得80000点势力积分", "small_type": "3,势力贡献", "max_num": 1, "id": 100280, "target": "30,80000"}, {"huodong_id": "", "prize": "82010057,1;8000062,2;12100000,6;12100030,6;82001040,4", "target_type": 0, "prize_des": "获得120000点势力积分", "small_type": "3,势力贡献", "max_num": 1, "id": 100290, "target": "30,120000"}, {"huodong_id": "", "prize": "82010057,1;8000062,2;12100000,8;12100030,8;82001040,4", "target_type": 0, "prize_des": "获得160000点势力积分", "small_type": "3,势力贡献", "max_num": 1, "id": 100300, "target": "30,160000"}, {"huodong_id": "", "prize": "82010057,1;8000063,2;12100000,10;12100030,10;82001040,4", "target_type": 0, "prize_des": "获得200000点势力积分", "small_type": "3,势力贡献", "max_num": 1, "id": 100310, "target": "30,200000"}, {"huodong_id": "", "prize": "82010057,1;12100037,1;82001020,4", "target_type": 0, "prize_des": "开服庆典第3天", "small_type": "1,探索陵寝", "max_num": 1, "id": 100320, "target": "1,1"}, {"huodong_id": "", "prize": "82010057,1;82001020,4;1000026,50;82001028,1;12410008,1;82001040,4", "target_type": 0, "prize_des": "探索1次陵寝", "small_type": "1,探索陵寝", "max_num": 1, "id": 100330, "target": "18,1"}, {"huodong_id": "", "prize": "82010057,1;82001020,8;1000026,50;82001028,1;12410008,2;82001040,4", "target_type": 0, "prize_des": "探索3次陵寝", "small_type": "1,探索陵寝", "max_num": 1, "id": 100340, "target": "18,3"}, {"huodong_id": "", "prize": "82010057,1;82001020,12;1000026,100;82001028,1;12410008,3;82001040,4", "target_type": 0, "prize_des": "探索6次陵寝", "small_type": "1,探索陵寝", "max_num": 1, "id": 100350, "target": "18,6"}, {"huodong_id": "", "prize": "82010057,1;82001020,16;1000026,100;82001028,1;12410008,4;82001040,4", "target_type": 0, "prize_des": "探索9次陵寝", "small_type": "1,探索陵寝", "max_num": 1, "id": 100360, "target": "18,9"}, {"huodong_id": "", "prize": "82010057,1;82001020,20;1000026,200;82001028,1;12410008,5;82001040,4", "target_type": 0, "prize_des": "探索12次陵寝", "small_type": "1,探索陵寝", "max_num": 1, "id": 100370, "target": "18,12"}, {"huodong_id": "", "prize": "82010057,1;8000017,2;12100000,2;12100012,2;82001040,4", "target_type": 0, "prize_des": "势力里帮助他人达4次", "small_type": "2,势力帮助", "max_num": 1, "id": 100380, "target": "29,4"}, {"huodong_id": "", "prize": "82010057,1;8000017,4;12100000,4;12100012,4;82001040,4", "target_type": 0, "prize_des": "势力里帮助他人达8次", "small_type": "2,势力帮助", "max_num": 1, "id": 100390, "target": "29,8"}, {"huodong_id": "", "prize": "82010057,1;8000017,6;12100000,6;12100012,6;82001040,4", "target_type": 0, "prize_des": "势力里帮助他人达12次", "small_type": "2,势力帮助", "max_num": 1, "id": 100400, "target": "29,12"}, {"huodong_id": "", "prize": "82010057,1;8000017,8;12100000,8;12100012,8;82001040,4", "target_type": 0, "prize_des": "势力里帮助他人达16次", "small_type": "2,势力帮助", "max_num": 1, "id": 100410, "target": "29,16"}, {"huodong_id": "", "prize": "82010057,1;8000017,10;12100000,10;12100012,10;82001040,4", "target_type": 0, "prize_des": "势力里帮助他人达20次", "small_type": "2,势力帮助", "max_num": 1, "id": 100420, "target": "29,20"}, {"huodong_id": "", "prize": "12100006,10;12100000,10", "target_type": 0, "prize_des": "拥有5个武将", "small_type": "3,招募武将", "max_num": 1, "id": 100430, "target": "7,5"}, {"huodong_id": "", "prize": "12100006,10;12100000,10", "target_type": 0, "prize_des": "拥有10个武将", "small_type": "3,招募武将", "max_num": 1, "id": 100440, "target": "7,10"}, {"huodong_id": "", "prize": "12100006,10;12100000,10", "target_type": 0, "prize_des": "拥有15个武将", "small_type": "3,招募武将", "max_num": 1, "id": 100450, "target": "7,15"}, {"huodong_id": "", "prize": "12100006,10;12100000,10", "target_type": 0, "prize_des": "拥有20个武将", "small_type": "3,招募武将", "max_num": 1, "id": 100460, "target": "7,20"}, {"huodong_id": "", "prize": "12100006,10;12100000,10", "target_type": 0, "prize_des": "拥有25个武将", "small_type": "3,招募武将", "max_num": 1, "id": 100470, "target": "7,25"}, {"huodong_id": "", "prize": "82010057,1;12100037,1;82001020,4", "target_type": 0, "prize_des": "开服庆典第4天", "small_type": "1,拜访宝箱", "max_num": 1, "id": 100480, "target": "1,1"}, {"huodong_id": "", "prize": "82010057,1;12100000,2;12100030,2;12100006,2;8000058,1;82001040,4", "target_type": 0, "prize_des": "拜访世界中的宝箱达2次", "small_type": "1,拜访宝箱", "max_num": 1, "id": 100490, "target": "17,2"}, {"huodong_id": "", "prize": "82010057,1;12100000,4;12100030,4;12100006,4;8000058,1;82001040,4", "target_type": 0, "prize_des": "拜访世界中的宝箱达4次", "small_type": "1,拜访宝箱", "max_num": 1, "id": 100500, "target": "17,4"}, {"huodong_id": "", "prize": "82010057,1;12100000,6;12100030,6;12100006,6;8000058,1;82001040,4", "target_type": 0, "prize_des": "拜访世界中的宝箱达6次", "small_type": "1,拜访宝箱", "max_num": 1, "id": 100510, "target": "17,6"}, {"huodong_id": "", "prize": "82010057,1;12100000,8;12100030,8;12100006,8;8000058,1;82001040,4", "target_type": 0, "prize_des": "拜访世界中的宝箱达8次", "small_type": "1,拜访宝箱", "max_num": 1, "id": 100520, "target": "17,8"}, {"huodong_id": "", "prize": "82010057,1;12100000,10;12100030,10;12100006,10;8000058,1;82001040,4", "target_type": 0, "prize_des": "拜访世界中的宝箱达10次", "small_type": "1,拜访宝箱", "max_num": 1, "id": 100530, "target": "17,10"}, {"huodong_id": "", "prize": "82010057,1;82001028,2;12100006,2;12100012,2;12100000,4;82001040,4", "target_type": 1, "prize_des": "累计进阶任意2个武将", "small_type": "2,武将进阶", "max_num": 1, "id": 100540, "target": "36,2"}, {"huodong_id": "", "prize": "82010057,1;82001028,3;12100006,4;12100012,4;12100000,6;82001040,4", "target_type": 1, "prize_des": "累计进阶任意5个武将", "small_type": "2,武将进阶", "max_num": 1, "id": 100550, "target": "36,5"}, {"huodong_id": "", "prize": "82010057,1;82001028,4;12100006,6;12100012,6;12100000,8;82001040,4", "target_type": 1, "prize_des": "累计有6个五星及以上武将进阶至1", "small_type": "2,武将进阶", "max_num": 1, "id": 100560, "target": "33,6"}, {"huodong_id": "", "prize": "82010057,1;5100140,1;12100006,8;12100012,8;12100000,10;82001040,4", "target_type": 2, "prize_des": "累计有6个六星及以上武将进阶至2", "small_type": "2,武将进阶", "max_num": 1, "id": 100570, "target": "34,6"}, {"huodong_id": "", "prize": "82010057,1;5100160,1;12100006,10;12100012,10;12100000,12;82001040,4", "target_type": 2, "prize_des": "累计有5个七星及以上武将进阶至2", "small_type": "2,武将进阶", "max_num": 1, "id": 100580, "target": "35,5"}, {"huodong_id": "", "prize": "12100006,6;12100000,6", "target_type": 0, "prize_des": "主殿等级到7级", "small_type": "3,主殿升级", "max_num": 1, "id": 100590, "target": "1,7"}, {"huodong_id": "", "prize": "12100006,6;12100000,6", "target_type": 0, "prize_des": "主殿等级到8级", "small_type": "3,主殿升级", "max_num": 1, "id": 100600, "target": "1,8"}, {"huodong_id": "", "prize": "12100006,6;12100000,6", "target_type": 0, "prize_des": "主殿等级到9级", "small_type": "3,主殿升级", "max_num": 1, "id": 100610, "target": "1,9"}, {"huodong_id": "", "prize": "12100006,6;12100000,6", "target_type": 0, "prize_des": "主殿等级到10级", "small_type": "3,主殿升级", "max_num": 1, "id": 100620, "target": "1,10"}, {"huodong_id": "", "prize": "12100006,6;12100000,6", "target_type": 0, "prize_des": "主殿等级到12级", "small_type": "3,主殿升级", "max_num": 1, "id": 100630, "target": "1,12"}, {"huodong_id": "", "prize": "82010057,1;12100037,1;82001020,4", "target_type": 0, "prize_des": "开服庆典第5天", "small_type": "1,累计消耗", "max_num": 1, "id": 100640, "target": "1,1"}, {"huodong_id": "", "prize": "82010057,1;82000009,2;12100012,4;12100006,4;12100000,4;82001040,4", "target_type": 0, "prize_des": "累计消费100元宝", "small_type": "1,累计消耗", "max_num": 1, "id": 100650, "target": "4,100"}, {"huodong_id": "", "prize": "82010057,1;82000009,4;12100012,6;12100006,6;12100000,6;82001040,4", "target_type": 0, "prize_des": "累计消费200元宝", "small_type": "1,累计消耗", "max_num": 1, "id": 100660, "target": "4,200"}, {"huodong_id": "", "prize": "82010057,1;82000009,6;12100012,8;12100006,8;12100000,8;82001040,4", "target_type": 0, "prize_des": "累计消费300元宝", "small_type": "1,累计消耗", "max_num": 1, "id": 100670, "target": "4,300"}, {"huodong_id": "", "prize": "82010057,1;82000009,8;12100012,10;12100006,10;12100000,10;82001040,4", "target_type": 0, "prize_des": "累计消费400元宝", "small_type": "1,累计消耗", "max_num": 1, "id": 100680, "target": "4,400"}, {"huodong_id": "", "prize": "82010057,1;82000009,10;12100012,12;12100006,12;12100000,12;82001040,4", "target_type": 0, "prize_des": "累计消费500元宝", "small_type": "1,累计消耗", "max_num": 1, "id": 100690, "target": "4,500"}, {"huodong_id": "", "prize": "82010057,1;1000026,50;12100012,4;12100006,4;12100000,4;82001040,4", "target_type": 0, "prize_des": "市场内的神秘商人处购买5次道具", "small_type": "2,神秘商人", "max_num": 1, "id": 100700, "target": "24,5"}, {"huodong_id": "", "prize": "82010057,1;1000026,100;12100012,6;12100006,6;12100000,6;82001040,4", "target_type": 0, "prize_des": "市场内的神秘商人处购买10次道具", "small_type": "2,神秘商人", "max_num": 1, "id": 100710, "target": "24,10"}, {"huodong_id": "", "prize": "82010057,1;1000026,150;12100012,8;12100006,8;12100000,8;82001040,4", "target_type": 0, "prize_des": "市场内的神秘商人处购买20次道具", "small_type": "2,神秘商人", "max_num": 1, "id": 100720, "target": "24,20"}, {"huodong_id": "", "prize": "82010057,1;1000026,200;12100012,10;12100006,10;12100000,10;82001040,4", "target_type": 0, "prize_des": "市场内的神秘商人处购买30次道具", "small_type": "2,神秘商人", "max_num": 1, "id": 100730, "target": "24,30"}, {"huodong_id": "", "prize": "82010057,1;1000026,250;12100012,12;12100006,12;12100000,12;82001040,4", "target_type": 0, "prize_des": "市场内的神秘商人处购买50次道具", "small_type": "2,神秘商人", "max_num": 1, "id": 100740, "target": "24,50"}, {"huodong_id": "", "prize": "82010057,1;12100067,1;12100012,4;12100006,4;12100000,4;82001040,4", "target_type": 0, "prize_des": "主城内加速时间达60分钟", "small_type": "3,飞速发展", "max_num": 1, "id": 100750, "target": "3,60"}, {"huodong_id": "", "prize": "82010057,1;12100067,1;12100012,6;12100006,6;12100000,6;82001040,4", "target_type": 0, "prize_des": "主城内加速时间达120分钟", "small_type": "3,飞速发展", "max_num": 1, "id": 100760, "target": "3,120"}, {"huodong_id": "", "prize": "82010057,1;12100067,2;12100012,8;12100006,8;12100000,8;82001040,4", "target_type": 0, "prize_des": "主城内加速时间达240分钟", "small_type": "3,飞速发展", "max_num": 1, "id": 100770, "target": "3,240"}, {"huodong_id": "", "prize": "82010057,1;12100067,2;12100012,10;12100006,10;12100000,10;82001040,4", "target_type": 0, "prize_des": "主城内加速时间达420分钟", "small_type": "3,飞速发展", "max_num": 1, "id": 100780, "target": "3,420"}, {"huodong_id": "", "prize": "82010057,1;12100067,4;12100012,12;12100006,12;12100000,12;82001040,4", "target_type": 0, "prize_des": "主城内加速时间达600分钟", "small_type": "3,飞速发展", "max_num": 1, "id": 100790, "target": "3,600"}, {"huodong_id": "2017010901", "prize": "12100006,2;12100000,2;8000001,1", "target_type": 0, "prize_des": "预约成就第一阶段", "small_type": "", "max_num": 1, "id": 131, "target": "2,1"}, {"huodong_id": "2017010901", "prize": "12100012,2;12100030,2;8000017,2", "target_type": 0, "prize_des": "预约成就第二阶段", "small_type": "", "max_num": 1, "id": 132, "target": "2,1"}, {"huodong_id": "2017010901", "prize": "12100024,2;8000057,1;8000033,3", "target_type": 0, "prize_des": "预约成就第三阶段", "small_type": "", "max_num": 1, "id": 133, "target": "2,1"}, {"huodong_id": "2017010901", "prize": "8000065,1;8000061,1;8000009,4", "target_type": 0, "prize_des": "预约成就第四阶段", "small_type": "", "max_num": 1, "id": 134, "target": "2,1"}, {"huodong_id": "2017010901", "prize": "8000066,1;8000062,1;8000025,5", "target_type": 0, "prize_des": "预约成就第五阶段", "small_type": "", "max_num": 1, "id": 135, "target": "2,1"}, {"huodong_id": "2017010901", "prize": "82001027,1;8000063,1;8000041,6", "target_type": 0, "prize_des": "预约成就第六阶段", "small_type": "", "max_num": 1, "id": 136, "target": "2,1"}, {"huodong_id": "2017020201", "prize": "12100000,10;12100012,10;8000017,1;1000026,10", "target_type": 0, "prize_des": "0酒", "small_type": "", "max_num": 1, "id": 164, "target": "2000,0"}, {"huodong_id": "2017020201", "prize": "12100000,14;12100012,14;8000017,2;1000026,20", "target_type": 0, "prize_des": "1酒", "small_type": "", "max_num": 1, "id": 165, "target": "2000,1"}, {"huodong_id": "2017020201", "prize": "12100000,18;12100012,18;8000017,4;1000026,40", "target_type": 0, "prize_des": "2酒", "small_type": "", "max_num": 1, "id": 166, "target": "2000,2"}, {"huodong_id": "2017020201", "prize": "12100000,26;12100012,26;8000017,6;1000026,70", "target_type": 0, "prize_des": "3酒", "small_type": "", "max_num": 1, "id": 167, "target": "2000,3"}, {"huodong_id": "2017020201", "prize": "12100000,40;12100012,40;8000017,8;1000026,100", "target_type": 0, "prize_des": "4酒", "small_type": "", "max_num": 1, "id": 168, "target": "2000,4"}, {"huodong_id": "2017020201", "prize": "12100000,56;12100012,56;8000017,10;1000026,150", "target_type": 0, "prize_des": "5酒", "small_type": "", "max_num": 1, "id": 169, "target": "2000,5"}, {"huodong_id": "2017020201", "prize": "12100000,88;12100012,88;8000017,12;1000026,260", "target_type": 0, "prize_des": "6酒", "small_type": "", "max_num": 1, "id": 170, "target": "2000,6"}, {"huodong_id": "2017080701", "prize": "8000002,8;12100006,1200;12100000,1400;12100030,1200;12100024,1200", "target_type": 0, "prize_des": "主殿提升到24级", "small_type": "", "max_num": 1, "id": 36300, "target": "1,24"}, {"huodong_id": "2017080701", "prize": "8000002,10;12100006,1500;12100000,1700;12100030,1500;12100024,1500", "target_type": 0, "prize_des": "主殿提升到25级", "small_type": "", "max_num": 1, "id": 36400, "target": "1,25"}, {"huodong_id": "2017080701", "prize": "8000002,10;12100006,1700;12100000,1900;12100030,1700;12100024,1700", "target_type": 0, "prize_des": "主殿提升到26级", "small_type": "", "max_num": 1, "id": 36500, "target": "1,26"}, {"huodong_id": "2017080701", "prize": "8000002,10;12100006,2000;12100000,2200;12100030,2000;12100024,2000", "target_type": 0, "prize_des": "主殿提升到27级", "small_type": "", "max_num": 1, "id": 36600, "target": "1,27"}, {"huodong_id": "2017080701", "prize": "8000002,12;12100006,2400;12100000,2600;12100030,2400;12100024,2400", "target_type": 0, "prize_des": "主殿提升到28级", "small_type": "", "max_num": 1, "id": 36700, "target": "1,28"}, {"huodong_id": "2017080701", "prize": "8000002,14;12100006,3000;12100000,3300;12100030,3000;12100024,3000", "target_type": 0, "prize_des": "主殿提升到29级", "small_type": "", "max_num": 1, "id": 36800, "target": "1,29"}, {"huodong_id": "2017080701", "prize": "8000002,16;12100006,4000;12100000,4400;12100030,4000;12100024,4000", "target_type": 0, "prize_des": "主殿提升到30级", "small_type": "", "max_num": 1, "id": 36900, "target": "1,30"}, {"huodong_id": "2017110901", "prize": "12100037,1;82001020,4", "target_type": 0, "prize_des": "累计消费达1元宝", "small_type": "", "max_num": 1, "id": 41981, "target": "4,1"}, {"huodong_id": "2017110901", "prize": "12100067,2;82010109,2;12100006,20;12100000,20;12100012,20;12100024,20", "target_type": 0, "prize_des": "累计消费达300元宝", "small_type": "", "max_num": 1, "id": 41982, "target": "4,300"}, {"huodong_id": "2017110901", "prize": "12100067,4;82010109,4;12100006,80;12100000,80;12100012,80;12100024,80", "target_type": 0, "prize_des": "累计消费达750元宝", "small_type": "", "max_num": 1, "id": 41983, "target": "4,750"}, {"huodong_id": "2017110901", "prize": "12100067,6;82010109,6;12100006,120;12100000,120;12100012,120;12100024,120", "target_type": 0, "prize_des": "累计消费达1500元宝", "small_type": "", "max_num": 1, "id": 41984, "target": "4,1500"}, {"huodong_id": "2017110901", "prize": "12100067,8;82010109,8;12100006,160;12100000,160;12100012,160;12100024,160", "target_type": 0, "prize_des": "累计消费达3000元宝", "small_type": "", "max_num": 1, "id": 41985, "target": "4,3000"}, {"huodong_id": "2017110901", "prize": "12100067,12;82010109,10;12100006,240;12100000,240;12100012,240;12100024,240", "target_type": 0, "prize_des": "累计消费达5000元宝", "small_type": "", "max_num": 1, "id": 41986, "target": "4,5000"}, {"huodong_id": "2017110901", "prize": "8000002,20;8000034,20;8000010,20;8000018,20;8000026,20;12100024,320", "target_type": 0, "prize_des": "累计消费达10000元宝", "small_type": "", "max_num": 1, "id": 41987, "target": "4,10000"}, {"huodong_id": "", "prize": "82001020,4", "target_type": 0, "prize_des": "活动有礼", "small_type": "", "max_num": 1, "id": 80220, "target": "1,1"}, {"huodong_id": "", "prize": "12100012,4;12100006,4;12100000,4;8000033,6;82001040,4", "target_type": 0, "prize_des": "在世界中的集市购买达1次", "small_type": "", "max_num": 1, "id": 80230, "target": "16,1"}, {"huodong_id": "", "prize": "12100012,8;12100006,8;12100000,8;8000033,9;82001040,4", "target_type": 0, "prize_des": "在世界中的集市购买达2次", "small_type": "", "max_num": 1, "id": 80240, "target": "16,2"}, {"huodong_id": "", "prize": "12100012,12;12100006,12;12100000,12;8000033,12;82001040,4", "target_type": 0, "prize_des": "在世界中的集市购买达3次", "small_type": "", "max_num": 1, "id": 80250, "target": "16,3"}, {"huodong_id": "", "prize": "12100012,16;12100006,16;12100000,16;8000033,15;82001040,4", "target_type": 0, "prize_des": "在世界中的集市购买达5次", "small_type": "", "max_num": 1, "id": 80260, "target": "16,5"}, {"huodong_id": "", "prize": "12100012,20;12100006,20;12100000,20;8000033,18;82001040,4", "target_type": 0, "prize_des": "在世界中的集市购买达7次", "small_type": "", "max_num": 1, "id": 80270, "target": "16,7"}, {"huodong_id": "", "prize": "82001020,4", "target_type": 0, "prize_des": "活动有礼", "small_type": "", "max_num": 1, "id": 80280, "target": "1,1"}, {"huodong_id": "", "prize": "12100012,4;12100006,4;12100000,4;8000033,6;82001040,4", "target_type": 0, "prize_des": "在世界中的集市购买达1次", "small_type": "", "max_num": 1, "id": 80290, "target": "16,1"}, {"huodong_id": "", "prize": "12100012,8;12100006,8;12100000,8;8000033,9;82001040,4", "target_type": 0, "prize_des": "在世界中的集市购买达2次", "small_type": "", "max_num": 1, "id": 80300, "target": "16,2"}, {"huodong_id": "", "prize": "12100012,12;12100006,12;12100000,12;8000033,12;82001040,4", "target_type": 0, "prize_des": "在世界中的集市购买达3次", "small_type": "", "max_num": 1, "id": 80310, "target": "16,3"}, {"huodong_id": "", "prize": "12100012,16;12100006,16;12100000,16;8000033,15;82001040,4", "target_type": 0, "prize_des": "在世界中的集市购买达5次", "small_type": "", "max_num": 1, "id": 80320, "target": "16,5"}, {"huodong_id": "", "prize": "12100012,20;12100006,20;12100000,20;8000033,18;82001040,4", "target_type": 0, "prize_des": "在世界中的集市购买达7次", "small_type": "", "max_num": 1, "id": 80330, "target": "16,7"}, {"huodong_id": "", "prize": "82001020,4", "target_type": 0, "prize_des": "活动有礼", "small_type": "", "max_num": 1, "id": 80340, "target": "1,1"}, {"huodong_id": "", "prize": "12100012,4;12100006,4;12100000,4;8000033,6;82001040,4", "target_type": 0, "prize_des": "在世界中的集市购买达1次", "small_type": "", "max_num": 1, "id": 80350, "target": "16,1"}, {"huodong_id": "", "prize": "12100012,8;12100006,8;12100000,8;8000033,9;82001040,4", "target_type": 0, "prize_des": "在世界中的集市购买达2次", "small_type": "", "max_num": 1, "id": 80360, "target": "16,2"}, {"huodong_id": "", "prize": "12100012,12;12100006,12;12100000,12;8000033,12;82001040,4", "target_type": 0, "prize_des": "在世界中的集市购买达3次", "small_type": "", "max_num": 1, "id": 80370, "target": "16,3"}, {"huodong_id": "", "prize": "12100012,16;12100006,16;12100000,16;8000033,15;82001040,4", "target_type": 0, "prize_des": "在世界中的集市购买达5次", "small_type": "", "max_num": 1, "id": 80380, "target": "16,5"}, {"huodong_id": "", "prize": "12100012,20;12100006,20;12100000,20;8000033,18;82001040,4", "target_type": 0, "prize_des": "在世界中的集市购买达7次", "small_type": "", "max_num": 1, "id": 80390, "target": "16,7"}, {"huodong_id": "", "prize": "82001020,4", "target_type": 0, "prize_des": "活动有礼", "small_type": "", "max_num": 1, "id": 80400, "target": "1,1"}, {"huodong_id": "", "prize": "12100012,4;12100006,4;12100000,4;8000033,6;82001040,4", "target_type": 0, "prize_des": "在世界中的集市购买达1次", "small_type": "", "max_num": 1, "id": 80410, "target": "16,1"}, {"huodong_id": "", "prize": "12100012,8;12100006,8;12100000,8;8000033,9;82001040,4", "target_type": 0, "prize_des": "在世界中的集市购买达2次", "small_type": "", "max_num": 1, "id": 80420, "target": "16,2"}, {"huodong_id": "", "prize": "12100012,12;12100006,12;12100000,12;8000033,12;82001040,4", "target_type": 0, "prize_des": "在世界中的集市购买达3次", "small_type": "", "max_num": 1, "id": 80430, "target": "16,3"}, {"huodong_id": "", "prize": "12100012,16;12100006,16;12100000,16;8000033,15;82001040,4", "target_type": 0, "prize_des": "在世界中的集市购买达5次", "small_type": "", "max_num": 1, "id": 80440, "target": "16,5"}, {"huodong_id": "", "prize": "12100012,20;12100006,20;12100000,20;8000033,18;82001040,4", "target_type": 0, "prize_des": "在世界中的集市购买达7次", "small_type": "", "max_num": 1, "id": 80450, "target": "16,7"}, {"huodong_id": "", "prize": "82001020,4", "target_type": 0, "prize_des": "活动有礼", "small_type": "", "max_num": 1, "id": 80460, "target": "1,1"}, {"huodong_id": "", "prize": "12100012,4;12100006,4;12100000,4;8000033,6;82001040,4", "target_type": 0, "prize_des": "在世界中的集市购买达1次", "small_type": "", "max_num": 1, "id": 80470, "target": "16,1"}, {"huodong_id": "", "prize": "12100012,8;12100006,8;12100000,8;8000033,9;82001040,4", "target_type": 0, "prize_des": "在世界中的集市购买达2次", "small_type": "", "max_num": 1, "id": 80480, "target": "16,2"}, {"huodong_id": "", "prize": "12100012,12;12100006,12;12100000,12;8000033,12;82001040,4", "target_type": 0, "prize_des": "在世界中的集市购买达3次", "small_type": "", "max_num": 1, "id": 80490, "target": "16,3"}, {"huodong_id": "", "prize": "12100012,16;12100006,16;12100000,16;8000033,15;82001040,4", "target_type": 0, "prize_des": "在世界中的集市购买达5次", "small_type": "", "max_num": 1, "id": 80500, "target": "16,5"}, {"huodong_id": "", "prize": "12100012,20;12100006,20;12100000,20;8000033,18;82001040,4", "target_type": 0, "prize_des": "在世界中的集市购买达7次", "small_type": "", "max_num": 1, "id": 80510, "target": "16,7"}, {"huodong_id": "", "prize": "82001020,4", "target_type": 0, "prize_des": "活动有礼", "small_type": "", "max_num": 1, "id": 80520, "target": "1,1"}, {"huodong_id": "", "prize": "12100000,4;12100030,4;82001028,1;8000057,1;82001040,4", "target_type": 0, "prize_des": "拜访世界中的宝箱达2次", "small_type": "", "max_num": 1, "id": 80530, "target": "17,2"}, {"huodong_id": "", "prize": "12100000,8;12100030,8;82001028,1;8000057,1;82001040,4", "target_type": 0, "prize_des": "拜访世界中的宝箱达4次", "small_type": "", "max_num": 1, "id": 80540, "target": "17,4"}, {"huodong_id": "", "prize": "12100000,12;12100030,12;82001028,2;8000057,2;82001040,4", "target_type": 0, "prize_des": "拜访世界中的宝箱达6次", "small_type": "", "max_num": 1, "id": 80550, "target": "17,6"}, {"huodong_id": "", "prize": "12100000,16;12100030,16;82001028,3;8000057,3;82001040,4", "target_type": 0, "prize_des": "拜访世界中的宝箱达8次", "small_type": "", "max_num": 1, "id": 80560, "target": "17,8"}, {"huodong_id": "", "prize": "12100000,20;12100030,20;82001028,3;8000057,3;82001040,4", "target_type": 0, "prize_des": "拜访世界中的宝箱达10次", "small_type": "", "max_num": 1, "id": 80570, "target": "17,10"}, {"huodong_id": "", "prize": "82001020,4", "target_type": 0, "prize_des": "活动有礼", "small_type": "", "max_num": 1, "id": 80580, "target": "1,1"}, {"huodong_id": "", "prize": "12100000,4;12100030,4;82001028,1;8000057,1;82001040,4", "target_type": 0, "prize_des": "拜访世界中的宝箱达2次", "small_type": "", "max_num": 1, "id": 80590, "target": "17,2"}, {"huodong_id": "", "prize": "12100000,8;12100030,8;82001028,1;8000057,1;82001040,4", "target_type": 0, "prize_des": "拜访世界中的宝箱达4次", "small_type": "", "max_num": 1, "id": 80600, "target": "17,4"}, {"huodong_id": "", "prize": "12100000,12;12100030,12;82001028,2;8000057,2;82001040,4", "target_type": 0, "prize_des": "拜访世界中的宝箱达6次", "small_type": "", "max_num": 1, "id": 80610, "target": "17,6"}, {"huodong_id": "", "prize": "12100000,16;12100030,16;82001028,3;8000057,3;82001040,4", "target_type": 0, "prize_des": "拜访世界中的宝箱达8次", "small_type": "", "max_num": 1, "id": 80620, "target": "17,8"}, {"huodong_id": "", "prize": "12100000,20;12100030,20;82001028,3;8000057,3;82001040,4", "target_type": 0, "prize_des": "拜访世界中的宝箱达10次", "small_type": "", "max_num": 1, "id": 80630, "target": "17,10"}, {"huodong_id": "", "prize": "82001020,4", "target_type": 0, "prize_des": "活动有礼", "small_type": "", "max_num": 1, "id": 80640, "target": "1,1"}, {"huodong_id": "", "prize": "12100000,4;12100030,4;82001028,1;8000057,1;82001040,4", "target_type": 0, "prize_des": "拜访世界中的宝箱达2次", "small_type": "", "max_num": 1, "id": 80650, "target": "17,2"}, {"huodong_id": "", "prize": "12100000,8;12100030,8;82001028,1;8000057,1;82001040,4", "target_type": 0, "prize_des": "拜访世界中的宝箱达4次", "small_type": "", "max_num": 1, "id": 80660, "target": "17,4"}, {"huodong_id": "", "prize": "12100000,12;12100030,12;82001028,2;8000057,2;82001040,4", "target_type": 0, "prize_des": "拜访世界中的宝箱达6次", "small_type": "", "max_num": 1, "id": 80670, "target": "17,6"}, {"huodong_id": "", "prize": "12100000,16;12100030,16;82001028,3;8000057,3;82001040,4", "target_type": 0, "prize_des": "拜访世界中的宝箱达8次", "small_type": "", "max_num": 1, "id": 80680, "target": "17,8"}, {"huodong_id": "", "prize": "12100000,20;12100030,20;82001028,3;8000057,3;82001040,4", "target_type": 0, "prize_des": "拜访世界中的宝箱达10次", "small_type": "", "max_num": 1, "id": 80690, "target": "17,10"}, {"huodong_id": "", "prize": "82001020,4", "target_type": 0, "prize_des": "活动有礼", "small_type": "", "max_num": 1, "id": 80700, "target": "1,1"}, {"huodong_id": "", "prize": "12100000,4;12100030,4;82001028,1;8000057,1;82001040,4", "target_type": 0, "prize_des": "拜访世界中的宝箱达2次", "small_type": "", "max_num": 1, "id": 80710, "target": "17,2"}, {"huodong_id": "", "prize": "12100000,8;12100030,8;82001028,1;8000057,1;82001040,4", "target_type": 0, "prize_des": "拜访世界中的宝箱达4次", "small_type": "", "max_num": 1, "id": 80720, "target": "17,4"}, {"huodong_id": "", "prize": "12100000,12;12100030,12;82001028,2;8000057,2;82001040,4", "target_type": 0, "prize_des": "拜访世界中的宝箱达6次", "small_type": "", "max_num": 1, "id": 80730, "target": "17,6"}, {"huodong_id": "", "prize": "12100000,16;12100030,16;82001028,3;8000057,3;82001040,4", "target_type": 0, "prize_des": "拜访世界中的宝箱达8次", "small_type": "", "max_num": 1, "id": 80740, "target": "17,8"}, {"huodong_id": "", "prize": "12100000,20;12100030,20;82001028,3;8000057,3;82001040,4", "target_type": 0, "prize_des": "拜访世界中的宝箱达10次", "small_type": "", "max_num": 1, "id": 80750, "target": "17,10"}, {"huodong_id": "", "prize": "82001020,4", "target_type": 0, "prize_des": "活动有礼", "small_type": "", "max_num": 1, "id": 80760, "target": "1,1"}, {"huodong_id": "", "prize": "12100000,4;12100030,4;82001028,1;8000057,1;82001040,4", "target_type": 0, "prize_des": "拜访世界中的宝箱达2次", "small_type": "", "max_num": 1, "id": 80770, "target": "17,2"}, {"huodong_id": "", "prize": "12100000,8;12100030,8;82001028,1;8000057,1;82001040,4", "target_type": 0, "prize_des": "拜访世界中的宝箱达4次", "small_type": "", "max_num": 1, "id": 80780, "target": "17,4"}, {"huodong_id": "", "prize": "12100000,12;12100030,12;82001028,2;8000057,2;82001040,4", "target_type": 0, "prize_des": "拜访世界中的宝箱达6次", "small_type": "", "max_num": 1, "id": 80790, "target": "17,6"}, {"huodong_id": "", "prize": "12100000,16;12100030,16;82001028,3;8000057,3;82001040,4", "target_type": 0, "prize_des": "拜访世界中的宝箱达8次", "small_type": "", "max_num": 1, "id": 80800, "target": "17,8"}, {"huodong_id": "", "prize": "12100000,20;12100030,20;82001028,3;8000057,3;82001040,4", "target_type": 0, "prize_des": "拜访世界中的宝箱达10次", "small_type": "", "max_num": 1, "id": 80810, "target": "17,10"}, {"huodong_id": "", "prize": "82001020,4", "target_type": 0, "prize_des": "活动有礼", "small_type": "", "max_num": 1, "id": 80820, "target": "1,1"}, {"huodong_id": "", "prize": "12100006,2;12100000,2;12100030,2;82001040,4", "target_type": 0, "prize_des": "采集资源负重达36000", "small_type": "", "max_num": 1, "id": 80830, "target": "15,36000"}, {"huodong_id": "", "prize": "12100006,6;12100000,6;12100030,6;82001040,4", "target_type": 0, "prize_des": "采集资源负重达360000", "small_type": "", "max_num": 1, "id": 80840, "target": "15,360000"}, {"huodong_id": "", "prize": "12100006,12;12100000,12;12100030,12;82001040,4", "target_type": 0, "prize_des": "采集资源负重达1080000", "small_type": "", "max_num": 1, "id": 80850, "target": "15,1080000"}, {"huodong_id": "", "prize": "8000018,1;12100006,20;12100000,20;12100024,20;82001040,4", "target_type": 0, "prize_des": "采集资源负重达2160000", "small_type": "", "max_num": 1, "id": 80860, "target": "15,2160000"}, {"huodong_id": "", "prize": "8000011,1;12100006,40;12100000,40;12100024,40;82001040,4", "target_type": 0, "prize_des": "采集资源负重达3600000", "small_type": "", "max_num": 1, "id": 80870, "target": "15,3600000"}, {"huodong_id": "", "prize": "82001020,4", "target_type": 0, "prize_des": "活动有礼", "small_type": "", "max_num": 1, "id": 80880, "target": "1,1"}, {"huodong_id": "", "prize": "12100006,2;12100000,2;12100030,2;82001040,4", "target_type": 0, "prize_des": "采集资源负重达36000", "small_type": "", "max_num": 1, "id": 80890, "target": "15,36000"}, {"huodong_id": "", "prize": "12100006,6;12100000,6;12100030,6;82001040,4", "target_type": 0, "prize_des": "采集资源负重达360000", "small_type": "", "max_num": 1, "id": 80900, "target": "15,360000"}, {"huodong_id": "", "prize": "12100006,12;12100000,12;12100030,12;82001040,4", "target_type": 0, "prize_des": "采集资源负重达1080000", "small_type": "", "max_num": 1, "id": 80910, "target": "15,1080000"}, {"huodong_id": "", "prize": "8000018,1;12100006,20;12100000,20;12100024,20;82001040,4", "target_type": 0, "prize_des": "采集资源负重达2160000", "small_type": "", "max_num": 1, "id": 80920, "target": "15,2160000"}, {"huodong_id": "", "prize": "8000011,1;12100006,40;12100000,40;12100024,40;82001040,4", "target_type": 0, "prize_des": "采集资源负重达3600000", "small_type": "", "max_num": 1, "id": 80930, "target": "15,3600000"}, {"huodong_id": "", "prize": "82001020,4", "target_type": 0, "prize_des": "活动有礼", "small_type": "", "max_num": 1, "id": 80940, "target": "1,1"}, {"huodong_id": "", "prize": "12100006,2;12100000,2;12100030,2;82001040,4", "target_type": 0, "prize_des": "采集资源负重达36000", "small_type": "", "max_num": 1, "id": 80950, "target": "15,36000"}, {"huodong_id": "", "prize": "12100006,6;12100000,6;12100030,6;82001040,4", "target_type": 0, "prize_des": "采集资源负重达360000", "small_type": "", "max_num": 1, "id": 80960, "target": "15,360000"}, {"huodong_id": "", "prize": "12100006,12;12100000,12;12100030,12;82001040,4", "target_type": 0, "prize_des": "采集资源负重达1080000", "small_type": "", "max_num": 1, "id": 80970, "target": "15,1080000"}, {"huodong_id": "", "prize": "8000018,1;12100006,20;12100000,20;12100024,20;82001040,4", "target_type": 0, "prize_des": "采集资源负重达2160000", "small_type": "", "max_num": 1, "id": 80980, "target": "15,2160000"}, {"huodong_id": "", "prize": "8000011,1;12100006,40;12100000,40;12100024,40;82001040,4", "target_type": 0, "prize_des": "采集资源负重达3600000", "small_type": "", "max_num": 1, "id": 80990, "target": "15,3600000"}, {"huodong_id": "", "prize": "82001020,4", "target_type": 0, "prize_des": "活动有礼", "small_type": "", "max_num": 1, "id": 81000, "target": "1,1"}, {"huodong_id": "", "prize": "12100006,2;12100000,2;12100030,2;82001040,4", "target_type": 0, "prize_des": "采集资源负重达36000", "small_type": "", "max_num": 1, "id": 81010, "target": "15,36000"}, {"huodong_id": "", "prize": "12100006,6;12100000,6;12100030,6;82001040,4", "target_type": 0, "prize_des": "采集资源负重达360000", "small_type": "", "max_num": 1, "id": 81020, "target": "15,360000"}, {"huodong_id": "", "prize": "12100006,12;12100000,12;12100030,12;82001040,4", "target_type": 0, "prize_des": "采集资源负重达1080000", "small_type": "", "max_num": 1, "id": 81030, "target": "15,1080000"}, {"huodong_id": "", "prize": "8000018,1;12100006,20;12100000,20;12100024,20;82001040,4", "target_type": 0, "prize_des": "采集资源负重达2160000", "small_type": "", "max_num": 1, "id": 81040, "target": "15,2160000"}, {"huodong_id": "", "prize": "8000011,1;12100006,40;12100000,40;12100024,40;82001040,4", "target_type": 0, "prize_des": "采集资源负重达3600000", "small_type": "", "max_num": 1, "id": 81050, "target": "15,3600000"}, {"huodong_id": "", "prize": "82001020,4", "target_type": 0, "prize_des": "活动有礼", "small_type": "", "max_num": 1, "id": 81060, "target": "1,1"}, {"huodong_id": "", "prize": "12100006,2;12100000,2;12100030,2;82001040,4", "target_type": 0, "prize_des": "采集资源负重达36000", "small_type": "", "max_num": 1, "id": 81070, "target": "15,36000"}, {"huodong_id": "", "prize": "12100006,6;12100000,6;12100030,6;82001040,4", "target_type": 0, "prize_des": "采集资源负重达360000", "small_type": "", "max_num": 1, "id": 81080, "target": "15,360000"}, {"huodong_id": "", "prize": "12100006,12;12100000,12;12100030,12;82001040,4", "target_type": 0, "prize_des": "采集资源负重达1080000", "small_type": "", "max_num": 1, "id": 81090, "target": "15,1080000"}, {"huodong_id": "", "prize": "8000018,1;12100006,20;12100000,20;12100024,20;82001040,4", "target_type": 0, "prize_des": "采集资源负重达2160000", "small_type": "", "max_num": 1, "id": 81100, "target": "15,2160000"}, {"huodong_id": "", "prize": "8000011,1;12100006,40;12100000,40;12100024,40;82001040,4", "target_type": 0, "prize_des": "采集资源负重达3600000", "small_type": "", "max_num": 1, "id": 81110, "target": "15,3600000"}, {"huodong_id": "", "prize": "82001020,4", "target_type": 0, "prize_des": "活动有礼", "small_type": "", "max_num": 1, "id": 81120, "target": "1,1"}, {"huodong_id": "", "prize": "82001020,4;1000026,25;82001028,1;12410008,1", "target_type": 0, "prize_des": "探索1次陵寝", "small_type": "", "max_num": 1, "id": 81130, "target": "18,1"}, {"huodong_id": "", "prize": "82001020,8;1000026,50;82001028,1;12410008,2", "target_type": 0, "prize_des": "探索3次陵寝", "small_type": "", "max_num": 1, "id": 81140, "target": "18,3"}, {"huodong_id": "", "prize": "82001020,12;1000026,75;82001028,1;12410008,3", "target_type": 0, "prize_des": "探索6次陵寝", "small_type": "", "max_num": 1, "id": 81150, "target": "18,6"}, {"huodong_id": "", "prize": "82001020,16;1000026,125;82001028,1;12410008,4", "target_type": 0, "prize_des": "探索9次陵寝", "small_type": "", "max_num": 1, "id": 81160, "target": "18,9"}, {"huodong_id": "", "prize": "82001020,20;1000026,225;82001028,1;12410008,5", "target_type": 0, "prize_des": "探索12次陵寝", "small_type": "", "max_num": 1, "id": 81170, "target": "18,12"}, {"huodong_id": "", "prize": "82001020,4", "target_type": 0, "prize_des": "活动有礼", "small_type": "", "max_num": 1, "id": 81180, "target": "1,1"}, {"huodong_id": "", "prize": "82001020,4;1000026,25;82001028,1;12410008,1", "target_type": 0, "prize_des": "探索1次陵寝", "small_type": "", "max_num": 1, "id": 81190, "target": "18,1"}, {"huodong_id": "", "prize": "82001020,8;1000026,50;82001028,1;12410008,2", "target_type": 0, "prize_des": "探索3次陵寝", "small_type": "", "max_num": 1, "id": 81200, "target": "18,3"}, {"huodong_id": "", "prize": "82001020,12;1000026,75;82001028,1;12410008,3", "target_type": 0, "prize_des": "探索6次陵寝", "small_type": "", "max_num": 1, "id": 81210, "target": "18,6"}, {"huodong_id": "", "prize": "82001020,16;1000026,125;82001028,1;12410008,4", "target_type": 0, "prize_des": "探索9次陵寝", "small_type": "", "max_num": 1, "id": 81220, "target": "18,9"}, {"huodong_id": "", "prize": "82001020,20;1000026,225;82001028,1;12410008,5", "target_type": 0, "prize_des": "探索12次陵寝", "small_type": "", "max_num": 1, "id": 81230, "target": "18,12"}, {"huodong_id": "", "prize": "82001020,4", "target_type": 0, "prize_des": "活动有礼", "small_type": "", "max_num": 1, "id": 81240, "target": "1,1"}, {"huodong_id": "", "prize": "82001020,4;1000026,25;82001028,1;12410008,1", "target_type": 0, "prize_des": "探索1次陵寝", "small_type": "", "max_num": 1, "id": 81250, "target": "18,1"}, {"huodong_id": "", "prize": "82001020,8;1000026,50;82001028,1;12410008,2", "target_type": 0, "prize_des": "探索3次陵寝", "small_type": "", "max_num": 1, "id": 81260, "target": "18,3"}, {"huodong_id": "", "prize": "82001020,12;1000026,75;82001028,1;12410008,3", "target_type": 0, "prize_des": "探索6次陵寝", "small_type": "", "max_num": 1, "id": 81270, "target": "18,6"}, {"huodong_id": "", "prize": "82001020,16;1000026,125;82001028,1;12410008,4", "target_type": 0, "prize_des": "探索9次陵寝", "small_type": "", "max_num": 1, "id": 81280, "target": "18,9"}, {"huodong_id": "", "prize": "82001020,20;1000026,225;82001028,1;12410008,5", "target_type": 0, "prize_des": "探索12次陵寝", "small_type": "", "max_num": 1, "id": 81290, "target": "18,12"}, {"huodong_id": "", "prize": "82001020,4", "target_type": 0, "prize_des": "活动有礼", "small_type": "", "max_num": 1, "id": 81300, "target": "1,1"}, {"huodong_id": "", "prize": "82001020,4;1000026,25;82001028,1;12410008,1", "target_type": 0, "prize_des": "探索1次陵寝", "small_type": "", "max_num": 1, "id": 81310, "target": "18,1"}, {"huodong_id": "", "prize": "82001020,8;1000026,50;82001028,1;12410008,2", "target_type": 0, "prize_des": "探索3次陵寝", "small_type": "", "max_num": 1, "id": 81320, "target": "18,3"}, {"huodong_id": "", "prize": "82001020,12;1000026,75;82001028,1;12410008,3", "target_type": 0, "prize_des": "探索6次陵寝", "small_type": "", "max_num": 1, "id": 81330, "target": "18,6"}, {"huodong_id": "", "prize": "82001020,16;1000026,125;82001028,1;12410008,4", "target_type": 0, "prize_des": "探索9次陵寝", "small_type": "", "max_num": 1, "id": 81340, "target": "18,9"}, {"huodong_id": "", "prize": "82001020,20;1000026,225;82001028,1;12410008,5", "target_type": 0, "prize_des": "探索12次陵寝", "small_type": "", "max_num": 1, "id": 81350, "target": "18,12"}, {"huodong_id": "", "prize": "82001020,4", "target_type": 0, "prize_des": "活动有礼", "small_type": "", "max_num": 1, "id": 81360, "target": "1,1"}, {"huodong_id": "", "prize": "82001020,4;12100000,4;12100006,4;82001040,2", "target_type": 0, "prize_des": "击败5个系统部队", "small_type": "", "max_num": 1, "id": 81370, "target": "14,5"}, {"huodong_id": "", "prize": "82001020,8;12100000,8;12100006,8;82001040,2", "target_type": 0, "prize_des": "击败10个系统部队", "small_type": "", "max_num": 1, "id": 81380, "target": "14,10"}, {"huodong_id": "", "prize": "82001020,10;12100000,10;12100006,10;82001040,2", "target_type": 0, "prize_des": "击败20个系统部队", "small_type": "", "max_num": 1, "id": 81390, "target": "14,20"}, {"huodong_id": "", "prize": "82001020,16;12100000,16;12100006,16;82001040,2", "target_type": 0, "prize_des": "击败30个系统部队", "small_type": "", "max_num": 1, "id": 81400, "target": "14,30"}, {"huodong_id": "", "prize": "82001027,1;82001020,20;12100000,20;82001040,2", "target_type": 0, "prize_des": "击败40个系统部队", "small_type": "", "max_num": 1, "id": 81410, "target": "14,40"}, {"huodong_id": "", "prize": "82001020,4", "target_type": 0, "prize_des": "活动有礼", "small_type": "", "max_num": 1, "id": 81420, "target": "1,1"}, {"huodong_id": "", "prize": "82001020,4;12100000,4;12100006,4;82001040,2", "target_type": 0, "prize_des": "击败5个系统部队", "small_type": "", "max_num": 1, "id": 81430, "target": "14,5"}, {"huodong_id": "", "prize": "82001020,8;12100000,8;12100006,8;82001040,2", "target_type": 0, "prize_des": "击败10个系统部队", "small_type": "", "max_num": 1, "id": 81440, "target": "14,10"}, {"huodong_id": "", "prize": "82001020,10;12100000,10;12100006,10;82001040,2", "target_type": 0, "prize_des": "击败20个系统部队", "small_type": "", "max_num": 1, "id": 81450, "target": "14,20"}, {"huodong_id": "", "prize": "82001020,16;12100000,16;12100006,16;82001040,2", "target_type": 0, "prize_des": "击败30个系统部队", "small_type": "", "max_num": 1, "id": 81460, "target": "14,30"}, {"huodong_id": "", "prize": "82001027,1;82001020,20;12100000,20;82001040,2", "target_type": 0, "prize_des": "击败40个系统部队", "small_type": "", "max_num": 1, "id": 81470, "target": "14,40"}, {"huodong_id": "", "prize": "82001020,4", "target_type": 0, "prize_des": "活动有礼", "small_type": "", "max_num": 1, "id": 81480, "target": "1,1"}, {"huodong_id": "", "prize": "82001020,4;12100000,4;12100006,4;82001040,2", "target_type": 0, "prize_des": "击败5个系统部队", "small_type": "", "max_num": 1, "id": 81490, "target": "14,5"}, {"huodong_id": "", "prize": "82001020,8;12100000,8;12100006,8;82001040,2", "target_type": 0, "prize_des": "击败10个系统部队", "small_type": "", "max_num": 1, "id": 81500, "target": "14,10"}, {"huodong_id": "", "prize": "82001020,10;12100000,10;12100006,10;82001040,2", "target_type": 0, "prize_des": "击败20个系统部队", "small_type": "", "max_num": 1, "id": 81510, "target": "14,20"}, {"huodong_id": "", "prize": "82001020,16;12100000,16;12100006,16;82001040,2", "target_type": 0, "prize_des": "击败30个系统部队", "small_type": "", "max_num": 1, "id": 81520, "target": "14,30"}, {"huodong_id": "", "prize": "82001027,1;82001020,20;12100000,20;82001040,2", "target_type": 0, "prize_des": "击败40个系统部队", "small_type": "", "max_num": 1, "id": 81530, "target": "14,40"}, {"huodong_id": "", "prize": "82001020,4", "target_type": 0, "prize_des": "活动有礼", "small_type": "", "max_num": 1, "id": 81540, "target": "1,1"}, {"huodong_id": "", "prize": "82001020,4;12100000,4;12100006,4;82001040,2", "target_type": 0, "prize_des": "击败5个系统部队", "small_type": "", "max_num": 1, "id": 81550, "target": "14,5"}, {"huodong_id": "", "prize": "82001020,8;12100000,8;12100006,8;82001040,2", "target_type": 0, "prize_des": "击败10个系统部队", "small_type": "", "max_num": 1, "id": 81560, "target": "14,10"}, {"huodong_id": "", "prize": "82001020,10;12100000,10;12100006,10;82001040,2", "target_type": 0, "prize_des": "击败20个系统部队", "small_type": "", "max_num": 1, "id": 81570, "target": "14,20"}, {"huodong_id": "", "prize": "82001020,16;12100000,16;12100006,16;82001040,2", "target_type": 0, "prize_des": "击败30个系统部队", "small_type": "", "max_num": 1, "id": 81580, "target": "14,30"}, {"huodong_id": "", "prize": "82001027,1;82001020,20;12100000,20;82001040,2", "target_type": 0, "prize_des": "击败40个系统部队", "small_type": "", "max_num": 1, "id": 81590, "target": "14,40"}, {"huodong_id": "", "prize": "82001020,4", "target_type": 0, "prize_des": "活动有礼", "small_type": "", "max_num": 1, "id": 81600, "target": "1,1"}, {"huodong_id": "", "prize": "82001020,4;12100000,4;12100006,4;82001040,2", "target_type": 0, "prize_des": "击败5个系统部队", "small_type": "", "max_num": 1, "id": 81610, "target": "14,5"}, {"huodong_id": "", "prize": "82001020,8;12100000,8;12100006,8;82001040,2", "target_type": 0, "prize_des": "击败10个系统部队", "small_type": "", "max_num": 1, "id": 81620, "target": "14,10"}, {"huodong_id": "", "prize": "82001020,10;12100000,10;12100006,10;82001040,2", "target_type": 0, "prize_des": "击败20个系统部队", "small_type": "", "max_num": 1, "id": 81630, "target": "14,20"}, {"huodong_id": "", "prize": "82001020,16;12100000,16;12100006,16;82001040,2", "target_type": 0, "prize_des": "击败30个系统部队", "small_type": "", "max_num": 1, "id": 81640, "target": "14,30"}, {"huodong_id": "", "prize": "82001027,1;82001020,20;12100000,20;82001040,2", "target_type": 0, "prize_des": "击败40个系统部队", "small_type": "", "max_num": 1, "id": 81650, "target": "14,40"}, {"huodong_id": "", "prize": "82001020,4", "target_type": 0, "prize_des": "活动有礼", "small_type": "", "max_num": 1, "id": 81660, "target": "1,1"}, {"huodong_id": "", "prize": "12100000,4;12100030,4;82000009,2", "target_type": 0, "prize_des": "在市场内的神秘商人处进行2次购买", "small_type": "", "max_num": 1, "id": 81670, "target": "24,2"}, {"huodong_id": "", "prize": "12100000,8;12100030,8;82000009,3", "target_type": 0, "prize_des": "在市场内的神秘商人处进行5次购买", "small_type": "", "max_num": 1, "id": 81680, "target": "24,5"}, {"huodong_id": "", "prize": "12100000,10;12100030,10;82000009,4", "target_type": 0, "prize_des": "在市场内的神秘商人处进行10次购买", "small_type": "", "max_num": 1, "id": 81690, "target": "24,10"}, {"huodong_id": "", "prize": "12100000,12;12100030,12;82010109,2", "target_type": 0, "prize_des": "在市场内的神秘商人处进行20次购买", "small_type": "", "max_num": 1, "id": 81700, "target": "24,20"}, {"huodong_id": "", "prize": "12100000,20;12100030,20;82010109,3", "target_type": 0, "prize_des": "在市场内的神秘商人处进行30次购买", "small_type": "", "max_num": 1, "id": 81710, "target": "24,30"}, {"huodong_id": "", "prize": "82001020,4", "target_type": 0, "prize_des": "活动有礼", "small_type": "", "max_num": 1, "id": 81720, "target": "1,1"}, {"huodong_id": "", "prize": "12100000,4;12100030,4;82000009,2", "target_type": 0, "prize_des": "在市场内的神秘商人处进行2次购买", "small_type": "", "max_num": 1, "id": 81730, "target": "24,2"}, {"huodong_id": "", "prize": "12100000,8;12100030,8;82000009,3", "target_type": 0, "prize_des": "在市场内的神秘商人处进行5次购买", "small_type": "", "max_num": 1, "id": 81740, "target": "24,5"}, {"huodong_id": "", "prize": "12100000,10;12100030,10;82000009,4", "target_type": 0, "prize_des": "在市场内的神秘商人处进行10次购买", "small_type": "", "max_num": 1, "id": 81750, "target": "24,10"}, {"huodong_id": "", "prize": "12100000,12;12100030,12;82010109,2", "target_type": 0, "prize_des": "在市场内的神秘商人处进行20次购买", "small_type": "", "max_num": 1, "id": 81760, "target": "24,20"}, {"huodong_id": "", "prize": "12100000,20;12100030,20;82010109,3", "target_type": 0, "prize_des": "在市场内的神秘商人处进行30次购买", "small_type": "", "max_num": 1, "id": 81770, "target": "24,30"}, {"huodong_id": "", "prize": "82001020,4", "target_type": 0, "prize_des": "活动有礼", "small_type": "", "max_num": 1, "id": 81780, "target": "1,1"}, {"huodong_id": "", "prize": "12100000,4;12100030,4;82000009,2", "target_type": 0, "prize_des": "在市场内的神秘商人处进行2次购买", "small_type": "", "max_num": 1, "id": 81790, "target": "24,2"}, {"huodong_id": "", "prize": "12100000,8;12100030,8;82000009,3", "target_type": 0, "prize_des": "在市场内的神秘商人处进行5次购买", "small_type": "", "max_num": 1, "id": 81800, "target": "24,5"}, {"huodong_id": "", "prize": "12100000,10;12100030,10;82000009,4", "target_type": 0, "prize_des": "在市场内的神秘商人处进行10次购买", "small_type": "", "max_num": 1, "id": 81810, "target": "24,10"}, {"huodong_id": "", "prize": "12100000,12;12100030,12;82010109,2", "target_type": 0, "prize_des": "在市场内的神秘商人处进行20次购买", "small_type": "", "max_num": 1, "id": 81820, "target": "24,20"}, {"huodong_id": "", "prize": "12100000,20;12100030,20;82010109,3", "target_type": 0, "prize_des": "在市场内的神秘商人处进行30次购买", "small_type": "", "max_num": 1, "id": 81830, "target": "24,30"}, {"huodong_id": "", "prize": "82001020,4", "target_type": 0, "prize_des": "活动有礼", "small_type": "", "max_num": 1, "id": 81840, "target": "1,1"}, {"huodong_id": "", "prize": "12100006,4;12100000,4;12100012,4", "target_type": 0, "prize_des": "训练3000个步兵", "small_type": "", "max_num": 1, "id": 81850, "target": "20,3000"}, {"huodong_id": "", "prize": "12100006,8;12100000,8;12100012,8", "target_type": 0, "prize_des": "训练3000个骑兵", "small_type": "", "max_num": 1, "id": 81860, "target": "21,3000"}, {"huodong_id": "", "prize": "12100006,12;12100000,12;12100012,12", "target_type": 0, "prize_des": "训练3000个弓兵", "small_type": "", "max_num": 1, "id": 81870, "target": "22,3000"}, {"huodong_id": "", "prize": "12100067,2;12100006,16;12100000,16;12100012,16", "target_type": 0, "prize_des": "训练3000个士兵", "small_type": "", "max_num": 1, "id": 81880, "target": "23,3000"}, {"huodong_id": "", "prize": "12100067,2;12100006,20;12100000,20;12100012,20", "target_type": 0, "prize_des": "治疗任意3000个伤兵", "small_type": "", "max_num": 1, "id": 81890, "target": "19,3000"}, {"huodong_id": "", "prize": "82001020,4", "target_type": 0, "prize_des": "活动有礼", "small_type": "", "max_num": 1, "id": 81900, "target": "1,1"}, {"huodong_id": "", "prize": "12100006,4;12100000,4;12100012,4", "target_type": 0, "prize_des": "训练3000个步兵", "small_type": "", "max_num": 1, "id": 81910, "target": "20,3000"}, {"huodong_id": "", "prize": "12100006,8;12100000,8;12100012,8", "target_type": 0, "prize_des": "训练3000个骑兵", "small_type": "", "max_num": 1, "id": 81920, "target": "21,3000"}, {"huodong_id": "", "prize": "12100006,12;12100000,12;12100012,12", "target_type": 0, "prize_des": "训练3000个弓兵", "small_type": "", "max_num": 1, "id": 81930, "target": "22,3000"}, {"huodong_id": "", "prize": "12100067,2;12100006,16;12100000,16;12100012,16", "target_type": 0, "prize_des": "训练3000个士兵", "small_type": "", "max_num": 1, "id": 81940, "target": "23,3000"}, {"huodong_id": "", "prize": "12100067,2;12100006,20;12100000,20;12100012,20", "target_type": 0, "prize_des": "治疗任意3000个伤兵", "small_type": "", "max_num": 1, "id": 81950, "target": "19,3000"}, {"huodong_id": "", "prize": "82001020,4", "target_type": 0, "prize_des": "活动有礼", "small_type": "", "max_num": 1, "id": 81960, "target": "1,1"}, {"huodong_id": "", "prize": "12100006,4;12100000,4;12100012,4", "target_type": 0, "prize_des": "训练3000个步兵", "small_type": "", "max_num": 1, "id": 81970, "target": "20,3000"}, {"huodong_id": "", "prize": "12100006,8;12100000,8;12100012,8", "target_type": 0, "prize_des": "训练3000个骑兵", "small_type": "", "max_num": 1, "id": 81980, "target": "21,3000"}, {"huodong_id": "", "prize": "12100006,12;12100000,12;12100012,12", "target_type": 0, "prize_des": "训练3000个弓兵", "small_type": "", "max_num": 1, "id": 81990, "target": "22,3000"}, {"huodong_id": "", "prize": "12100067,2;12100006,16;12100000,16;12100012,16", "target_type": 0, "prize_des": "训练3000个士兵", "small_type": "", "max_num": 1, "id": 82000, "target": "23,3000"}, {"huodong_id": "", "prize": "12100067,2;12100006,20;12100000,20;12100012,20", "target_type": 0, "prize_des": "治疗任意3000个伤兵", "small_type": "", "max_num": 1, "id": 82010, "target": "19,3000"}, {"huodong_id": "", "prize": "82001020,4", "target_type": 0, "prize_des": "活动有礼", "small_type": "", "max_num": 1, "id": 82020, "target": "1,1"}, {"huodong_id": "", "prize": "12100006,4;12100000,4;12100030,4", "target_type": 0, "prize_des": "累计使用加速令或元宝减少时间达60分钟", "small_type": "", "max_num": 1, "id": 82030, "target": "3,60"}, {"huodong_id": "", "prize": "12100006,8;12100000,8;12100030,8", "target_type": 0, "prize_des": "累计使用加速令或元宝减少时间达120分钟", "small_type": "", "max_num": 1, "id": 82040, "target": "3,120"}, {"huodong_id": "", "prize": "12100006,12;12100000,12;12100030,12;12100024,12", "target_type": 0, "prize_des": "累计使用加速令或元宝减少时间达240分钟", "small_type": "", "max_num": 1, "id": 82050, "target": "3,240"}, {"huodong_id": "", "prize": "12100006,16;12100000,16;12100030,16;12100024,16", "target_type": 0, "prize_des": "累计使用加速令或元宝减少时间达420分钟", "small_type": "", "max_num": 1, "id": 82060, "target": "3,420"}, {"huodong_id": "", "prize": "12100006,20;12100000,20;12100030,20;12100024,20;12102003,2", "target_type": 0, "prize_des": "累计使用加速令或元宝减少时间达600分钟", "small_type": "", "max_num": 1, "id": 82070, "target": "3,600"}, {"huodong_id": "", "prize": "82001020,4", "target_type": 0, "prize_des": "活动有礼", "small_type": "", "max_num": 1, "id": 82080, "target": "1,1"}, {"huodong_id": "", "prize": "12100006,4;12100000,4;12100030,4", "target_type": 0, "prize_des": "累计使用加速令或元宝减少时间达60分钟", "small_type": "", "max_num": 1, "id": 82090, "target": "3,60"}, {"huodong_id": "", "prize": "12100006,8;12100000,8;12100030,8", "target_type": 0, "prize_des": "累计使用加速令或元宝减少时间达120分钟", "small_type": "", "max_num": 1, "id": 82100, "target": "3,120"}, {"huodong_id": "", "prize": "12100006,12;12100000,12;12100030,12;12100024,12", "target_type": 0, "prize_des": "累计使用加速令或元宝减少时间达240分钟", "small_type": "", "max_num": 1, "id": 82110, "target": "3,240"}, {"huodong_id": "", "prize": "12100006,16;12100000,16;12100030,16;12100024,16", "target_type": 0, "prize_des": "累计使用加速令或元宝减少时间达420分钟", "small_type": "", "max_num": 1, "id": 82120, "target": "3,420"}, {"huodong_id": "", "prize": "12100006,20;12100000,20;12100030,20;12100024,20;12102003,2", "target_type": 0, "prize_des": "累计使用加速令或元宝减少时间达600分钟", "small_type": "", "max_num": 1, "id": 82130, "target": "3,600"}, {"huodong_id": "", "prize": "82001020,4", "target_type": 0, "prize_des": "活动有礼", "small_type": "", "max_num": 1, "id": 82140, "target": "1,1"}, {"huodong_id": "", "prize": "12100006,4;12100000,4;12100030,4", "target_type": 0, "prize_des": "累计使用加速令或元宝减少时间达60分钟", "small_type": "", "max_num": 1, "id": 82150, "target": "3,60"}, {"huodong_id": "", "prize": "12100006,8;12100000,8;12100030,8", "target_type": 0, "prize_des": "累计使用加速令或元宝减少时间达120分钟", "small_type": "", "max_num": 1, "id": 82160, "target": "3,120"}, {"huodong_id": "", "prize": "12100006,12;12100000,12;12100030,12;12100024,12", "target_type": 0, "prize_des": "累计使用加速令或元宝减少时间达240分钟", "small_type": "", "max_num": 1, "id": 82170, "target": "3,240"}, {"huodong_id": "", "prize": "12100006,16;12100000,16;12100030,16;12100024,16", "target_type": 0, "prize_des": "累计使用加速令或元宝减少时间达420分钟", "small_type": "", "max_num": 1, "id": 82180, "target": "3,420"}, {"huodong_id": "", "prize": "12100006,20;12100000,20;12100030,20;12100024,20;12102003,2", "target_type": 0, "prize_des": "累计使用加速令或元宝减少时间达600分钟", "small_type": "", "max_num": 1, "id": 82190, "target": "3,600"}, {"huodong_id": "", "prize": "82001020,4", "target_type": 0, "prize_des": "活动有礼", "small_type": "", "max_num": 1, "id": 82200, "target": "1,1"}, {"huodong_id": "", "prize": "8000009,4;12100012,4;12100006,4", "target_type": 0, "prize_des": "进行5次跑商", "small_type": "", "max_num": 1, "id": 82210, "target": "25,5"}, {"huodong_id": "", "prize": "8000009,6;12100012,8;12100006,8", "target_type": 0, "prize_des": "进行10次跑商", "small_type": "", "max_num": 1, "id": 82220, "target": "25,10"}, {"huodong_id": "", "prize": "8000009,8;12100012,10;12100006,10", "target_type": 0, "prize_des": "进行15次跑商", "small_type": "", "max_num": 1, "id": 82230, "target": "25,15"}, {"huodong_id": "", "prize": "8000009,10;12100012,12;12100006,12", "target_type": 0, "prize_des": "盈利达400000铜币", "small_type": "", "max_num": 1, "id": 82240, "target": "26,400000"}, {"huodong_id": "", "prize": "8000009,12;12100012,16;12100006,16", "target_type": 0, "prize_des": "盈利达800000铜币", "small_type": "", "max_num": 1, "id": 82250, "target": "26,800000"}, {"huodong_id": "", "prize": "8000009,14;12100012,20;12100006,20", "target_type": 0, "prize_des": "盈利达1200000铜币", "small_type": "", "max_num": 1, "id": 82260, "target": "26,1200000"}, {"huodong_id": "", "prize": "82001020,4", "target_type": 0, "prize_des": "活动有礼", "small_type": "", "max_num": 1, "id": 82270, "target": "1,1"}, {"huodong_id": "", "prize": "8000009,4;12100012,4;12100006,4", "target_type": 0, "prize_des": "进行5次跑商", "small_type": "", "max_num": 1, "id": 82280, "target": "25,5"}, {"huodong_id": "", "prize": "8000009,6;12100012,8;12100006,8", "target_type": 0, "prize_des": "进行10次跑商", "small_type": "", "max_num": 1, "id": 82290, "target": "25,10"}, {"huodong_id": "", "prize": "8000009,8;12100012,10;12100006,10", "target_type": 0, "prize_des": "进行15次跑商", "small_type": "", "max_num": 1, "id": 82300, "target": "25,15"}, {"huodong_id": "", "prize": "8000009,10;12100012,12;12100006,12", "target_type": 0, "prize_des": "盈利达400000铜币", "small_type": "", "max_num": 1, "id": 82310, "target": "26,400000"}, {"huodong_id": "", "prize": "8000009,12;12100012,16;12100006,16", "target_type": 0, "prize_des": "盈利达800000铜币", "small_type": "", "max_num": 1, "id": 82320, "target": "26,800000"}, {"huodong_id": "", "prize": "8000009,14;12100012,20;12100006,20", "target_type": 0, "prize_des": "盈利达1200000铜币", "small_type": "", "max_num": 1, "id": 82330, "target": "26,1200000"}, {"huodong_id": "", "prize": "82001020,4", "target_type": 0, "prize_des": "活动有礼", "small_type": "", "max_num": 1, "id": 82340, "target": "1,1"}, {"huodong_id": "", "prize": "8000009,4;12100012,4;12100006,4", "target_type": 0, "prize_des": "进行5次跑商", "small_type": "", "max_num": 1, "id": 82350, "target": "25,5"}, {"huodong_id": "", "prize": "8000009,6;12100012,8;12100006,8", "target_type": 0, "prize_des": "进行10次跑商", "small_type": "", "max_num": 1, "id": 82360, "target": "25,10"}, {"huodong_id": "", "prize": "8000009,8;12100012,10;12100006,10", "target_type": 0, "prize_des": "进行15次跑商", "small_type": "", "max_num": 1, "id": 82370, "target": "25,15"}, {"huodong_id": "", "prize": "8000009,10;12100012,12;12100006,12", "target_type": 0, "prize_des": "盈利达400000铜币", "small_type": "", "max_num": 1, "id": 82380, "target": "26,400000"}, {"huodong_id": "", "prize": "8000009,12;12100012,16;12100006,16", "target_type": 0, "prize_des": "盈利达800000铜币", "small_type": "", "max_num": 1, "id": 82390, "target": "26,800000"}, {"huodong_id": "", "prize": "8000009,14;12100012,20;12100006,20", "target_type": 0, "prize_des": "盈利达1200000铜币", "small_type": "", "max_num": 1, "id": 82400, "target": "26,1200000"}, {"huodong_id": "", "prize": "12100000,10;12100012,10;8000001,1;1000042,20", "target_type": 0, "prize_des": "0酒", "small_type": "", "max_num": 1, "id": 82410, "target": "2000,0"}, {"huodong_id": "", "prize": "12100000,14;12100012,14;8000001,2;1000042,40", "target_type": 0, "prize_des": "1酒", "small_type": "", "max_num": 1, "id": 82420, "target": "2000,1"}, {"huodong_id": "", "prize": "12100000,18;12100012,18;8000001,3;1000042,60", "target_type": 0, "prize_des": "2酒", "small_type": "", "max_num": 1, "id": 82430, "target": "2000,2"}, {"huodong_id": "", "prize": "12100000,26;12100012,26;8000001,4;1000042,80", "target_type": 0, "prize_des": "3酒", "small_type": "", "max_num": 1, "id": 82440, "target": "2000,3"}, {"huodong_id": "", "prize": "12100000,40;12100012,40;8000001,6;1000042,100", "target_type": 0, "prize_des": "4酒", "small_type": "", "max_num": 1, "id": 82450, "target": "2000,4"}, {"huodong_id": "", "prize": "12100000,56;12100012,56;8000001,8;82010109,1", "target_type": 0, "prize_des": "5酒", "small_type": "", "max_num": 1, "id": 82460, "target": "2000,5"}, {"huodong_id": "", "prize": "12100000,88;12100012,88;8000001,12;82010109,2", "target_type": 0, "prize_des": "6酒", "small_type": "", "max_num": 1, "id": 82470, "target": "2000,6"}, {"huodong_id": "", "prize": "1000006,20;12100006,10;12100000,10;12100012,10", "target_type": 0, "prize_des": "登录游戏", "small_type": "", "max_num": 1, "id": 82480, "target": "1,1"}, {"huodong_id": "", "prize": "1000026,200;12100006,10;12100000,10;12100012,10", "target_type": 0, "prize_des": "在世界上找到并开启5次宝箱", "small_type": "", "max_num": 1, "id": 82490, "target": "17,5"}, {"huodong_id": "", "prize": "8000061,2;8000062,2;8000063,2;12100012,10", "target_type": 0, "prize_des": "采集资源负重达36000", "small_type": "", "max_num": 1, "id": 82500, "target": "15,36000"}, {"huodong_id": "", "prize": "8000061,2;8000062,2;8000063,2;12100012,10", "target_type": 0, "prize_des": "完成1次陵寝探索", "small_type": "", "max_num": 1, "id": 82510, "target": "18,1"}, {"huodong_id": "", "prize": "8000061,2;8000062,2;8000063,2;12100012,10", "target_type": 0, "prize_des": "训练任意1000个士兵", "small_type": "", "max_num": 1, "id": 82520, "target": "23,1000"}, {"huodong_id": "", "prize": "1000006,60;8000002,2;12100000,10;12100012,10", "target_type": 0, "prize_des": "今日充值达6元", "small_type": "", "max_num": 1, "id": 82530, "target": "5,6"}, {"huodong_id": "", "prize": "1000006,250;82001028,6;8000065,1;8000066,1", "target_type": 0, "prize_des": "今日充值达25元", "small_type": "", "max_num": 1, "id": 82540, "target": "5,25"}, {"huodong_id": "", "prize": "1000006,370;82001029,1;8000065,1;8000066,1", "target_type": 0, "prize_des": "今日充值达68元", "small_type": "", "max_num": 1, "id": 82550, "target": "5,68"}, {"huodong_id": "", "prize": "1000006,20;12100006,10;12100000,10;12100012,10", "target_type": 0, "prize_des": "登录游戏", "small_type": "", "max_num": 1, "id": 82560, "target": "1,1"}, {"huodong_id": "", "prize": "1000026,200;12100006,10;12100000,10;12100012,10", "target_type": 0, "prize_des": "在世界上找到并开启5次宝箱", "small_type": "", "max_num": 1, "id": 82570, "target": "17,5"}, {"huodong_id": "", "prize": "8000061,2;8000062,2;8000063,2;12100012,10", "target_type": 0, "prize_des": "采集资源负重达36000", "small_type": "", "max_num": 1, "id": 82580, "target": "15,36000"}, {"huodong_id": "", "prize": "8000061,2;8000062,2;8000063,2;12100012,10", "target_type": 0, "prize_des": "完成1次陵寝探索", "small_type": "", "max_num": 1, "id": 82590, "target": "18,1"}, {"huodong_id": "", "prize": "8000061,2;8000062,2;8000063,2;12100012,10", "target_type": 0, "prize_des": "训练任意1000个士兵", "small_type": "", "max_num": 1, "id": 82600, "target": "23,1000"}, {"huodong_id": "", "prize": "1000006,60;8000002,2;12100000,10;12100012,10", "target_type": 0, "prize_des": "今日充值达6元", "small_type": "", "max_num": 1, "id": 82610, "target": "5,6"}, {"huodong_id": "", "prize": "1000006,250;82001028,6;8000065,1;8000066,1", "target_type": 0, "prize_des": "今日充值达25元", "small_type": "", "max_num": 1, "id": 82620, "target": "5,25"}, {"huodong_id": "", "prize": "1000006,370;82001029,1;8000065,1;8000066,1", "target_type": 0, "prize_des": "今日充值达68元", "small_type": "", "max_num": 1, "id": 82630, "target": "5,68"}, {"huodong_id": "", "prize": "1000006,20;12100006,10;12100000,10;12100012,10", "target_type": 0, "prize_des": "登录游戏", "small_type": "", "max_num": 1, "id": 82640, "target": "1,1"}, {"huodong_id": "", "prize": "1000026,200;12100006,10;12100000,10;12100012,10", "target_type": 0, "prize_des": "在世界上找到并开启5次宝箱", "small_type": "", "max_num": 1, "id": 82650, "target": "17,5"}, {"huodong_id": "", "prize": "8000061,2;8000062,2;8000063,2;12100012,10", "target_type": 0, "prize_des": "采集资源负重达36000", "small_type": "", "max_num": 1, "id": 82660, "target": "15,36000"}, {"huodong_id": "", "prize": "8000061,2;8000062,2;8000063,2;12100012,10", "target_type": 0, "prize_des": "完成1次陵寝探索", "small_type": "", "max_num": 1, "id": 82670, "target": "18,1"}, {"huodong_id": "", "prize": "8000061,2;8000062,2;8000063,2;12100012,10", "target_type": 0, "prize_des": "训练任意1000个士兵", "small_type": "", "max_num": 1, "id": 82680, "target": "23,1000"}, {"huodong_id": "", "prize": "1000006,60;8000002,2;12100000,10;12100012,10", "target_type": 0, "prize_des": "今日充值达6元", "small_type": "", "max_num": 1, "id": 82690, "target": "5,6"}, {"huodong_id": "", "prize": "1000006,250;82001028,6;8000065,1;8000066,1", "target_type": 0, "prize_des": "今日充值达25元", "small_type": "", "max_num": 1, "id": 82700, "target": "5,25"}, {"huodong_id": "", "prize": "1000006,370;82001029,1;8000065,1;8000066,1", "target_type": 0, "prize_des": "今日充值达68元", "small_type": "", "max_num": 1, "id": 82710, "target": "5,68"}, {"huodong_id": "", "prize": "6103110,1", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 82720, "target": "1000,1"}, {"huodong_id": "", "prize": "12100006,15", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 82730, "target": "1000,2"}, {"huodong_id": "", "prize": "12100000,15", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 82740, "target": "1000,3"}, {"huodong_id": "", "prize": "12100030,15", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 82750, "target": "1000,4"}, {"huodong_id": "", "prize": "12100024,15", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 82760, "target": "1000,5"}, {"huodong_id": "", "prize": "6103108,1", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 82770, "target": "1000,6"}, {"huodong_id": "", "prize": "8000018,1", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 82780, "target": "1000,7"}, {"huodong_id": "", "prize": "8000002,1", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 82790, "target": "1000,8"}, {"huodong_id": "", "prize": "6103106,1", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 82800, "target": "1000,9"}, {"huodong_id": "", "prize": "12100006,15", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 82810, "target": "1000,10"}, {"huodong_id": "", "prize": "12100000,15", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 82820, "target": "1000,11"}, {"huodong_id": "", "prize": "12100030,15", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 82830, "target": "1000,12"}, {"huodong_id": "", "prize": "12100024,15", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 82840, "target": "1000,13"}, {"huodong_id": "", "prize": "5100170,1", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 82850, "target": "1000,14"}, {"huodong_id": "", "prize": "82000009,2", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 82860, "target": "1000,15"}, {"huodong_id": "", "prize": "12100067,1", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 82870, "target": "1000,16"}, {"huodong_id": "", "prize": "6102040,10", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 82880, "target": "1000,17"}, {"huodong_id": "", "prize": "6102040,15", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 82890, "target": "1000,18"}, {"huodong_id": "", "prize": "6102040,20", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 82900, "target": "1000,19"}, {"huodong_id": "", "prize": "6102040,25", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 82910, "target": "1000,20"}, {"huodong_id": "", "prize": "82003000,1", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 82920, "target": "1000,21"}, {"huodong_id": "", "prize": "82003000,9", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 82930, "target": "1000,22"}, {"huodong_id": "", "prize": "1,30;2,430;3,430;4,430;5,430;6,30;7,431;8,431;9,30;10,431;11,431;12,431;13,431;14,5;15,300;16,299", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 82940, "target": "1000,23"}, {"huodong_id": "", "prize": "6103104,1", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83010, "target": "1000,1"}, {"huodong_id": "", "prize": "12100006,15", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83020, "target": "1000,2"}, {"huodong_id": "", "prize": "12100000,15", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83030, "target": "1000,3"}, {"huodong_id": "", "prize": "12100030,15", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83040, "target": "1000,4"}, {"huodong_id": "", "prize": "12100024,15", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83050, "target": "1000,5"}, {"huodong_id": "", "prize": "6103102,1", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83060, "target": "1000,6"}, {"huodong_id": "", "prize": "8000018,1", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83070, "target": "1000,7"}, {"huodong_id": "", "prize": "8000002,1", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83080, "target": "1000,8"}, {"huodong_id": "", "prize": "6103100,1", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83090, "target": "1000,9"}, {"huodong_id": "", "prize": "12100006,15", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83100, "target": "1000,10"}, {"huodong_id": "", "prize": "12100000,15", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83110, "target": "1000,11"}, {"huodong_id": "", "prize": "12100030,15", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83120, "target": "1000,12"}, {"huodong_id": "", "prize": "12100024,15", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83130, "target": "1000,13"}, {"huodong_id": "", "prize": "5100170,1", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83140, "target": "1000,14"}, {"huodong_id": "", "prize": "82000009,2", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83150, "target": "1000,15"}, {"huodong_id": "", "prize": "12100067,1", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83160, "target": "1000,16"}, {"huodong_id": "", "prize": "6102040,10", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83170, "target": "1000,17"}, {"huodong_id": "", "prize": "6102040,15", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83180, "target": "1000,18"}, {"huodong_id": "", "prize": "6102040,20", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83190, "target": "1000,19"}, {"huodong_id": "", "prize": "6102040,25", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83200, "target": "1000,20"}, {"huodong_id": "", "prize": "82003000,1", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83210, "target": "1000,21"}, {"huodong_id": "", "prize": "82003000,9", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83220, "target": "1000,22"}, {"huodong_id": "", "prize": "1,30;2,430;3,430;4,430;5,430;6,30;7,431;8,431;9,30;10,431;11,431;12,431;13,431;14,5;15,300;16,299", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83230, "target": "1000,23"}, {"huodong_id": "", "prize": "1000006,20;12100006,10;12100000,10;12100012,10", "target_type": 0, "prize_des": "登录游戏", "small_type": "", "max_num": 1, "id": 83240, "target": "1,1"}, {"huodong_id": "", "prize": "1000026,200;12100006,10;12100000,10;12100012,10", "target_type": 0, "prize_des": "在世界上找到并开启10次宝箱", "small_type": "", "max_num": 1, "id": 83250, "target": "17,10"}, {"huodong_id": "", "prize": "8000061,2;8000062,2;8000063,2;12100012,10", "target_type": 0, "prize_des": "采集资源负重达180000", "small_type": "", "max_num": 1, "id": 83260, "target": "15,180000"}, {"huodong_id": "", "prize": "8000061,2;8000062,2;8000063,2;12100012,10", "target_type": 0, "prize_des": "击败20个系统部队", "small_type": "", "max_num": 1, "id": 83270, "target": "14,20"}, {"huodong_id": "", "prize": "8000061,2;8000062,2;8000063,2;12100012,10", "target_type": 0, "prize_des": "训练3000个士兵", "small_type": "", "max_num": 1, "id": 83280, "target": "23,3000"}, {"huodong_id": "", "prize": "1000006,300;8000002,2;12100000,10;12100012,10", "target_type": 0, "prize_des": "今日充值达30元", "small_type": "", "max_num": 1, "id": 83290, "target": "5,30"}, {"huodong_id": "2017110901", "prize": "12100037,1;82001020,4", "target_type": 0, "prize_des": "累计消费达1元宝", "small_type": "", "max_num": 1, "id": 83300, "target": "4,1"}, {"huodong_id": "2017110901", "prize": "12100067,2;82010109,2;12100006,20;12100000,20;12100012,20;12100024,20", "target_type": 0, "prize_des": "累计消费达300元宝", "small_type": "", "max_num": 1, "id": 83310, "target": "4,300"}, {"huodong_id": "2017110901", "prize": "12100067,4;82010109,4;12100006,80;12100000,80;12100012,80;12100024,80", "target_type": 0, "prize_des": "累计消费达750元宝", "small_type": "", "max_num": 1, "id": 83320, "target": "4,750"}, {"huodong_id": "2017110901", "prize": "12100067,6;82010109,6;12100006,120;12100000,120;12100012,120;12100024,120", "target_type": 0, "prize_des": "累计消费达1500元宝", "small_type": "", "max_num": 1, "id": 83330, "target": "4,1500"}, {"huodong_id": "2017110901", "prize": "12100067,8;82010109,8;12100006,160;12100000,160;12100012,160;12100024,160", "target_type": 0, "prize_des": "累计消费达3000元宝", "small_type": "", "max_num": 1, "id": 83340, "target": "4,3000"}, {"huodong_id": "2017110901", "prize": "12100067,10;82010109,10;12100006,200;12100000,200;12100012,200;12100024,200", "target_type": 0, "prize_des": "累计消费达5000元宝", "small_type": "", "max_num": 1, "id": 83350, "target": "4,5000"}, {"huodong_id": "2017110901", "prize": "8000002,20;8000034,20;8000010,20;8000018,20;8000026,20;12100024,320", "target_type": 0, "prize_des": "累计消费达10000元宝", "small_type": "", "max_num": 1, "id": 83360, "target": "4,10000"}, {"huodong_id": "", "prize": "82005000,3", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83370, "target": "1000,1"}, {"huodong_id": "", "prize": "12100012,20", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83380, "target": "1000,2"}, {"huodong_id": "", "prize": "12100000,20", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83390, "target": "1000,3"}, {"huodong_id": "", "prize": "12102003,1", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83400, "target": "1000,4"}, {"huodong_id": "", "prize": "12100024,20", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83410, "target": "1000,5"}, {"huodong_id": "", "prize": "82010109,4", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83420, "target": "1000,6"}, {"huodong_id": "", "prize": "8000018,1", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83430, "target": "1000,7"}, {"huodong_id": "", "prize": "8000002,1", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83440, "target": "1000,8"}, {"huodong_id": "", "prize": "1000042,500", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83450, "target": "1000,9"}, {"huodong_id": "", "prize": "12100012,20", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83460, "target": "1000,10"}, {"huodong_id": "", "prize": "12100000,20", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83470, "target": "1000,11"}, {"huodong_id": "", "prize": "12102003,1", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83480, "target": "1000,12"}, {"huodong_id": "", "prize": "12100024,20", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83490, "target": "1000,13"}, {"huodong_id": "", "prize": "5100170,1", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83500, "target": "1000,14"}, {"huodong_id": "", "prize": "82000009,2", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83510, "target": "1000,15"}, {"huodong_id": "", "prize": "12100067,1", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83520, "target": "1000,16"}, {"huodong_id": "", "prize": "82001028,1", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83530, "target": "1000,17"}, {"huodong_id": "", "prize": "82001028,2", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83540, "target": "1000,18"}, {"huodong_id": "", "prize": "82001028,3", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83550, "target": "1000,19"}, {"huodong_id": "", "prize": "82001028,4", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83560, "target": "1000,20"}, {"huodong_id": "", "prize": "82003000,1", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83570, "target": "1000,21"}, {"huodong_id": "", "prize": "82003000,9", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83580, "target": "1000,22"}, {"huodong_id": "", "prize": "1,40;2,540;3,540;4,100;5,540;6,40;7,420;8,420;9,40;10,540;11,540;12,100;13,540;14,5;15,300;16,295", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83590, "target": "1000,23"}, {"huodong_id": "", "prize": "6103105,1", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83600, "target": "1000,1"}, {"huodong_id": "", "prize": "12100006,15", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83610, "target": "1000,2"}, {"huodong_id": "", "prize": "12100000,15", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83620, "target": "1000,3"}, {"huodong_id": "", "prize": "12100030,15", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83630, "target": "1000,4"}, {"huodong_id": "", "prize": "12100024,15", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83640, "target": "1000,5"}, {"huodong_id": "", "prize": "6103103,1", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83650, "target": "1000,6"}, {"huodong_id": "", "prize": "8000018,1", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83660, "target": "1000,7"}, {"huodong_id": "", "prize": "8000002,1", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83670, "target": "1000,8"}, {"huodong_id": "", "prize": "6103101,1", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83680, "target": "1000,9"}, {"huodong_id": "", "prize": "12100006,15", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83690, "target": "1000,10"}, {"huodong_id": "", "prize": "12100000,15", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83700, "target": "1000,11"}, {"huodong_id": "", "prize": "12100030,15", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83710, "target": "1000,12"}, {"huodong_id": "", "prize": "12100024,15", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83720, "target": "1000,13"}, {"huodong_id": "", "prize": "5100170,1", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83730, "target": "1000,14"}, {"huodong_id": "", "prize": "82000009,2", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83740, "target": "1000,15"}, {"huodong_id": "", "prize": "12100067,1", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83750, "target": "1000,16"}, {"huodong_id": "", "prize": "6102040,10", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83760, "target": "1000,17"}, {"huodong_id": "", "prize": "6102040,15", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83770, "target": "1000,18"}, {"huodong_id": "", "prize": "6102040,20", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83780, "target": "1000,19"}, {"huodong_id": "", "prize": "6102040,25", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83790, "target": "1000,20"}, {"huodong_id": "", "prize": "82003000,1", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83800, "target": "1000,21"}, {"huodong_id": "", "prize": "82003000,9", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83810, "target": "1000,22"}, {"huodong_id": "", "prize": "1,30;2,430;3,430;4,430;5,430;6,30;7,431;8,431;9,30;10,431;11,431;12,431;13,431;14,5;15,300;16,299", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 83820, "target": "1000,23"}, {"huodong_id": "201000", "prize": "8000075,1;8000061,8;8000062,8;8000063,8;8000058,10", "target_type": 0, "prize_des": "第1天", "small_type": "", "max_num": 1, "id": 200001, "target": "1,1"}, {"huodong_id": "201000", "prize": "8000075,1;8000061,8;8000062,8;8000063,8;8000058,10", "target_type": 0, "prize_des": "第2天", "small_type": "", "max_num": 1, "id": 200002, "target": "2,2"}, {"huodong_id": "201000", "prize": "8000075,1;8000061,8;8000062,8;8000063,8;8000058,10", "target_type": 0, "prize_des": "第3天", "small_type": "", "max_num": 1, "id": 200003, "target": "2,3"}, {"huodong_id": "201000", "prize": "8000075,1;8000061,8;8000062,8;8000063,8;8000058,10", "target_type": 0, "prize_des": "第4天", "small_type": "", "max_num": 1, "id": 200004, "target": "2,4"}, {"huodong_id": "201000", "prize": "8000075,1;8000061,8;8000062,8;8000063,8;8000058,10", "target_type": 0, "prize_des": "第5天", "small_type": "", "max_num": 1, "id": 200005, "target": "2,5"}, {"huodong_id": "201000", "prize": "8000075,1;8000061,8;8000062,8;8000063,8;8000058,10", "target_type": 0, "prize_des": "第6天", "small_type": "", "max_num": 1, "id": 200006, "target": "2,6"}, {"huodong_id": "201000", "prize": "8000075,1;8000061,8;8000062,8;8000063,8;8000058,10", "target_type": 0, "prize_des": "第7天", "small_type": "", "max_num": 1, "id": 200007, "target": "2,7"}, {"huodong_id": "201100", "prize": "1,12100038,1,1000006,50;2,8000034,10,1000006,20", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 200010, "target": "9,1"}, {"huodong_id": "201100", "prize": "3,82001026,10,1000006,10;4,82001027,10,1000006,100", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 200020, "target": "9,1"}, {"huodong_id": "201100", "prize": "5,8000018,6,1000006,20;6,82001029,5,1000006,200", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 200030, "target": "9,1"}, {"huodong_id": "201100", "prize": "7,82000009,20,1000006,10;8,8000010,40,1000006,20", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 200040, "target": "9,1"}, {"huodong_id": "201100", "prize": "9,82001027,2,1000006,100;10,82001027,20,1000006,100", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 200050, "target": "9,1"}, {"huodong_id": "201100", "prize": "11,12100000,400,1000006,1;12,12100067,100,1000006,30", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 200060, "target": "9,1"}, {"huodong_id": "201100", "prize": "13,8000002,10,1000006,20;14,82010109,50,1000006,60", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 200070, "target": "9,1"}, {"huodong_id": "201110", "prize": "9000007,1", "target_type": 0, "prize_des": "累计充值达1元", "small_type": "", "max_num": 1, "id": 200080, "target": "5,1"}, {"huodong_id": "201110", "prize": "8000002,6;1000026,500;6102040,20;12100000,40", "target_type": 0, "prize_des": "累计充值达88元", "small_type": "", "max_num": 1, "id": 200090, "target": "5,88"}, {"huodong_id": "201110", "prize": "8000002,12;1000026,1000;6102040,40;12100000,80", "target_type": 0, "prize_des": "累计充值达188元", "small_type": "", "max_num": 1, "id": 200100, "target": "5,188"}, {"huodong_id": "201110", "prize": "8000002,24;1000026,1500;6102040,80;12100000,120", "target_type": 0, "prize_des": "累计充值达388元", "small_type": "", "max_num": 1, "id": 200110, "target": "5,388"}, {"huodong_id": "201110", "prize": "5440106,1;8000002,48;6102040,100;12100000,200", "target_type": 0, "prize_des": "累计充值达888元", "small_type": "", "max_num": 1, "id": 200120, "target": "5,888"}, {"huodong_id": "201110", "prize": "5300102,1;8000002,64;12102003,40;6102040,150;12100000,400", "target_type": 0, "prize_des": "累计充值达1888元", "small_type": "", "max_num": 1, "id": 200130, "target": "5,1888"}, {"huodong_id": "201110", "prize": "5100310,1;5300106,1;8000002,80;12102003,60", "target_type": 0, "prize_des": "累计充值达3888元", "small_type": "", "max_num": 1, "id": 200140, "target": "5,3888"}, {"huodong_id": "201110", "prize": "9000012,1;5300104,1;5300100,1;8000002,120;12102003,80", "target_type": 0, "prize_des": "累计充值达8888元", "small_type": "", "max_num": 1, "id": 200150, "target": "5,8888"}, {"huodong_id": "201190", "prize": "1000006,20;82001020,4;82001040,4", "target_type": 0, "prize_des": "新服庆典第1天", "small_type": "1,采集资源", "max_num": 1, "id": 200160, "target": "1,1"}, {"huodong_id": "201190", "prize": "12100006,4;12100000,4;12100030,4;82001040,4", "target_type": 0, "prize_des": "采集资源负重达72000", "small_type": "1,采集资源", "max_num": 1, "id": 200170, "target": "15,72000"}, {"huodong_id": "201190", "prize": "12100006,8;12100000,8;12100030,8;82001040,4", "target_type": 0, "prize_des": "采集资源负重达144000", "small_type": "1,采集资源", "max_num": 1, "id": 200180, "target": "15,144000"}, {"huodong_id": "201190", "prize": "12100006,12;12100000,12;12100030,12;82001040,4", "target_type": 0, "prize_des": "采集资源负重达288000", "small_type": "1,采集资源", "max_num": 1, "id": 200190, "target": "15,288000"}, {"huodong_id": "201190", "prize": "8000018,1;12100006,16;12100000,16;12100030,8;82001040,4", "target_type": 0, "prize_des": "采集资源负重达1080000", "small_type": "1,采集资源", "max_num": 1, "id": 200200, "target": "15,1080000"}, {"huodong_id": "201190", "prize": "8000060,1;12100006,20;12100000,20;12100030,10;82001040,4", "target_type": 0, "prize_des": "采集资源负重达2160000", "small_type": "1,采集资源", "max_num": 1, "id": 200210, "target": "15,2160000"}, {"huodong_id": "201190", "prize": "82001020,4;12100000,4;12100006,4;82001040,4", "target_type": 0, "prize_des": "击败5个系统部队", "small_type": "2,征战天下", "max_num": 1, "id": 200220, "target": "14,5"}, {"huodong_id": "201190", "prize": "82001020,6;12100000,8;12100006,8;82001040,4", "target_type": 0, "prize_des": "击败10个系统部队", "small_type": "2,征战天下", "max_num": 1, "id": 200230, "target": "14,10"}, {"huodong_id": "201190", "prize": "82001020,8;12100000,12;12100006,12;82001040,4", "target_type": 0, "prize_des": "击败15个系统部队", "small_type": "2,征战天下", "max_num": 1, "id": 200240, "target": "14,15"}, {"huodong_id": "201190", "prize": "82001020,10;12100000,16;12100006,16;82001040,4", "target_type": 0, "prize_des": "击败20个系统部队", "small_type": "2,征战天下", "max_num": 1, "id": 200250, "target": "14,20"}, {"huodong_id": "201190", "prize": "82001027,1;82001020,15;12100000,20;82001040,4", "target_type": 0, "prize_des": "击败30个系统部队", "small_type": "2,征战天下", "max_num": 1, "id": 200260, "target": "14,30"}, {"huodong_id": "201200", "prize": "1000006,30;82001020,4;82001040,4", "target_type": 0, "prize_des": "新服庆典第2天", "small_type": "1,摸金校尉", "max_num": 1, "id": 200270, "target": "1,1"}, {"huodong_id": "201200", "prize": "82001020,4;1000026,50;82001028,1;12410008,1;82001040,4", "target_type": 0, "prize_des": "探索1次陵寝", "small_type": "1,摸金校尉", "max_num": 1, "id": 200280, "target": "18,1"}, {"huodong_id": "201200", "prize": "82001020,8;1000026,100;82001028,1;12410008,2;82001040,4", "target_type": 0, "prize_des": "探索3次陵寝", "small_type": "1,摸金校尉", "max_num": 1, "id": 200290, "target": "18,3"}, {"huodong_id": "201200", "prize": "82001020,12;1000026,150;82001028,1;12410008,3;82001040,4", "target_type": 0, "prize_des": "探索6次陵寝", "small_type": "1,摸金校尉", "max_num": 1, "id": 200300, "target": "18,6"}, {"huodong_id": "201200", "prize": "82001020,16;1000026,200;82001028,1;12410008,4;82001040,4", "target_type": 0, "prize_des": "探索9次陵寝", "small_type": "1,摸金校尉", "max_num": 1, "id": 200310, "target": "18,9"}, {"huodong_id": "201200", "prize": "82001020,20;1000026,250;82001028,1;12410008,5;82001040,4", "target_type": 0, "prize_des": "探索12次陵寝", "small_type": "1,摸金校尉", "max_num": 1, "id": 200320, "target": "18,12"}, {"huodong_id": "201200", "prize": "8000017,4;12100000,4;12100030,4;82001040,4", "target_type": 0, "prize_des": "征战1个任意据点(己方据点满时，可攻击其他势力的据点)", "small_type": "2,征战据点", "max_num": 1, "id": 200330, "target": "38,1"}, {"huodong_id": "201200", "prize": "8000017,6;12100000,8;12100030,8;82001040,4", "target_type": 0, "prize_des": "征战5个任意据点并获得胜利", "small_type": "2,征战据点", "max_num": 1, "id": 200340, "target": "38,5"}, {"huodong_id": "201200", "prize": "8000017,8;12100000,12;12100030,12;82001040,4", "target_type": 0, "prize_des": "征战10个任意据点并获得胜利", "small_type": "2,征战据点", "max_num": 1, "id": 200350, "target": "38,10"}, {"huodong_id": "201200", "prize": "8000017,10;12100000,16;12100030,16;82001040,4", "target_type": 0, "prize_des": "征战20个任意据点并获得胜利", "small_type": "2,征战据点", "max_num": 1, "id": 200360, "target": "38,20"}, {"huodong_id": "201200", "prize": "8000017,12;12100000,20;12100030,20;82001040,4", "target_type": 0, "prize_des": "征战30个任意据点并获得胜利", "small_type": "2,征战据点", "max_num": 1, "id": 200370, "target": "38,30"}, {"huodong_id": "201200", "prize": "8000009,4;12100012,2;12100006,2;82001040,4", "target_type": 0, "prize_des": "进行5次跑商", "small_type": "3,跑商贸易", "max_num": 1, "id": 200380, "target": "25,5"}, {"huodong_id": "201200", "prize": "8000009,6;12100012,4;12100006,4;82001040,4", "target_type": 0, "prize_des": "进行10次跑商", "small_type": "3,跑商贸易", "max_num": 1, "id": 200390, "target": "25,10"}, {"huodong_id": "201200", "prize": "8000009,8;12100012,8;12100006,8;82001040,4", "target_type": 0, "prize_des": "进行15次跑商", "small_type": "3,跑商贸易", "max_num": 1, "id": 200400, "target": "25,15"}, {"huodong_id": "201200", "prize": "8000009,10;12100012,12;12100006,12;82001040,4", "target_type": 0, "prize_des": "盈利达400000铜币", "small_type": "3,跑商贸易", "max_num": 1, "id": 200410, "target": "26,400000"}, {"huodong_id": "201200", "prize": "8000009,12;12100012,16;12100006,16;82001040,4", "target_type": 0, "prize_des": "盈利达800000铜币", "small_type": "3,跑商贸易", "max_num": 1, "id": 200420, "target": "26,800000"}, {"huodong_id": "201200", "prize": "8000009,14;12100012,20;12100006,20;82001040,4", "target_type": 0, "prize_des": "盈利达1200000铜币", "small_type": "3,跑商贸易", "max_num": 1, "id": 200430, "target": "26,1200000"}, {"huodong_id": "201210", "prize": "1000006,40;82001020,4;82001040,4", "target_type": 0, "prize_des": "新服庆典第3天", "small_type": "1,集市购买", "max_num": 1, "id": 200440, "target": "1,1"}, {"huodong_id": "201210", "prize": "12100012,4;12100000,4;82000005,2;8000033,8;82001040,4", "target_type": 0, "prize_des": "在世界中的集市购买达2次", "small_type": "1,集市购买", "max_num": 1, "id": 200450, "target": "16,2"}, {"huodong_id": "201210", "prize": "12100012,8;12100000,8;82000005,3;8000033,12;82001040,4", "target_type": 0, "prize_des": "在世界中的集市购买达4次", "small_type": "1,集市购买", "max_num": 1, "id": 200460, "target": "16,4"}, {"huodong_id": "201210", "prize": "12100012,12;12100000,12;82000005,4;8000033,16;82001040,4", "target_type": 0, "prize_des": "在世界中的集市购买达8次", "small_type": "1,集市购买", "max_num": 1, "id": 200470, "target": "16,8"}, {"huodong_id": "201210", "prize": "12100012,16;12100000,16;82000005,5;8000033,20;82001040,4", "target_type": 0, "prize_des": "在世界中的集市购买达12次", "small_type": "1,集市购买", "max_num": 1, "id": 200480, "target": "16,12"}, {"huodong_id": "201210", "prize": "12100012,20;12100000,20;82000005,6;8000033,24;82001040,4", "target_type": 0, "prize_des": "在世界中的集市购买达15次", "small_type": "1,集市购买", "max_num": 1, "id": 200490, "target": "16,15"}, {"huodong_id": "201210", "prize": "8000061,2;12100000,4;12100030,4;82001040,4", "target_type": 0, "prize_des": "获得40000点势力积分", "small_type": "2,势力贡献", "max_num": 1, "id": 200500, "target": "30,40000"}, {"huodong_id": "201210", "prize": "8000061,2;12100000,8;12100030,8;82001040,4", "target_type": 0, "prize_des": "获得80000点势力积分", "small_type": "2,势力贡献", "max_num": 1, "id": 200510, "target": "30,80000"}, {"huodong_id": "201210", "prize": "8000062,2;12100000,12;12100030,12;82001040,4", "target_type": 0, "prize_des": "获得120000点势力积分", "small_type": "2,势力贡献", "max_num": 1, "id": 200520, "target": "30,120000"}, {"huodong_id": "201210", "prize": "8000062,2;12100000,16;12100030,16;82001040,4", "target_type": 0, "prize_des": "获得160000点势力积分", "small_type": "2,势力贡献", "max_num": 1, "id": 200530, "target": "30,160000"}, {"huodong_id": "201210", "prize": "8000063,2;12100000,20;12100030,20;82001040,4", "target_type": 0, "prize_des": "获得200000点势力积分", "small_type": "2,势力贡献", "max_num": 1, "id": 200540, "target": "30,200000"}, {"huodong_id": "201210", "prize": "82010109,1;12100012,4;12100000,4;82001040,4", "target_type": 0, "prize_des": "累计治疗2000个伤兵", "small_type": "3,治疗伤兵", "max_num": 1, "id": 200550, "target": "19,2000"}, {"huodong_id": "201210", "prize": "82010109,2;12100012,8;12100000,8;82001040,4", "target_type": 0, "prize_des": "累计治疗4000个伤兵", "small_type": "3,治疗伤兵", "max_num": 1, "id": 200560, "target": "19,4000"}, {"huodong_id": "201210", "prize": "82010109,3;12100012,12;12100000,12;82001040,4", "target_type": 0, "prize_des": "累计治疗6000个伤兵", "small_type": "3,治疗伤兵", "max_num": 1, "id": 200570, "target": "19,6000"}, {"huodong_id": "201210", "prize": "82010109,4;12100012,16;12100000,16;82001040,4", "target_type": 0, "prize_des": "累计治疗8000个伤兵", "small_type": "3,治疗伤兵", "max_num": 1, "id": 200580, "target": "19,8000"}, {"huodong_id": "201210", "prize": "82010109,5;12100012,20;12100000,20;82001040,4", "target_type": 0, "prize_des": "累计治疗10000个伤兵", "small_type": "3,治疗伤兵", "max_num": 1, "id": 200590, "target": "19,10000"}, {"huodong_id": "201220", "prize": "1000006,50;82001020,4;82001040,4", "target_type": 0, "prize_des": "新服庆典第4天", "small_type": "1,拜访宝箱", "max_num": 1, "id": 200600, "target": "1,1"}, {"huodong_id": "201220", "prize": "12100000,4;12100030,4;82001028,1;8000058,1;82001040,4", "target_type": 0, "prize_des": "拜访世界中的宝箱达2次", "small_type": "1,拜访宝箱", "max_num": 1, "id": 200610, "target": "17,2"}, {"huodong_id": "201220", "prize": "12100000,8;12100030,8;82001028,2;8000058,1;82001040,4", "target_type": 0, "prize_des": "拜访世界中的宝箱达4次", "small_type": "1,拜访宝箱", "max_num": 1, "id": 200620, "target": "17,4"}, {"huodong_id": "201220", "prize": "12100000,12;12100030,12;82001028,2;8000058,1;82001040,4", "target_type": 0, "prize_des": "拜访世界中的宝箱达6次", "small_type": "1,拜访宝箱", "max_num": 1, "id": 200630, "target": "17,6"}, {"huodong_id": "201220", "prize": "12100000,16;12100030,16;82001028,2;8000058,1;82001040,4", "target_type": 0, "prize_des": "拜访世界中的宝箱达8次", "small_type": "1,拜访宝箱", "max_num": 1, "id": 200640, "target": "17,8"}, {"huodong_id": "201220", "prize": "12100000,20;12100030,20;82001028,3;8000058,1;82001040,4", "target_type": 0, "prize_des": "拜访世界中的宝箱达10次", "small_type": "1,拜访宝箱", "max_num": 1, "id": 200650, "target": "17,10"}, {"huodong_id": "201220", "prize": "12100006,8;12100000,4;12100012,4;82001040,4", "target_type": 0, "prize_des": "训练1000个士兵", "small_type": "2,训练士兵", "max_num": 1, "id": 200660, "target": "23,1000"}, {"huodong_id": "201220", "prize": "12100006,12;12100000,8;12100012,8;82001040,4", "target_type": 0, "prize_des": "训练3000个士兵", "small_type": "2,训练士兵", "max_num": 1, "id": 200670, "target": "23,3000"}, {"huodong_id": "201220", "prize": "12100006,16;12100000,12;12100012,12;82001040,4", "target_type": 0, "prize_des": "训练5000个士兵", "small_type": "2,训练士兵", "max_num": 1, "id": 200680, "target": "23,5000"}, {"huodong_id": "201220", "prize": "12100067,2;12100006,16;12100000,16;12100012,16;82001040,4", "target_type": 0, "prize_des": "训练7000个士兵", "small_type": "2,训练士兵", "max_num": 1, "id": 200690, "target": "23,7000"}, {"huodong_id": "201220", "prize": "12100067,2;12100006,20;12100000,20;12100012,20;82001040,4", "target_type": 0, "prize_des": "训练10000个士兵", "small_type": "2,训练士兵", "max_num": 1, "id": 200700, "target": "23,10000"}, {"huodong_id": "201220", "prize": "12100006,4;12100000,4;12100012,4;82001040,4", "target_type": 0, "prize_des": "势力里帮助他人达4次", "small_type": "3,帮助他人", "max_num": 1, "id": 200710, "target": "29,4"}, {"huodong_id": "201220", "prize": "12100006,8;12100000,8;12100012,8;82001040,4", "target_type": 0, "prize_des": "势力里帮助他人达8次", "small_type": "3,帮助他人", "max_num": 1, "id": 200720, "target": "29,8"}, {"huodong_id": "201220", "prize": "12100006,12;12100000,12;12100012,12;82001040,4", "target_type": 0, "prize_des": "势力里帮助他人达12次", "small_type": "3,帮助他人", "max_num": 1, "id": 200730, "target": "29,12"}, {"huodong_id": "201220", "prize": "12100067,2;12100006,16;12100000,16;12100012,8;82001040,4", "target_type": 0, "prize_des": "势力里帮助他人达16次", "small_type": "3,帮助他人", "max_num": 1, "id": 200740, "target": "29,16"}, {"huodong_id": "201220", "prize": "12100067,2;12100006,20;12100000,20;12100012,10;82001040,4", "target_type": 0, "prize_des": "势力里帮助他人达20次", "small_type": "3,帮助他人", "max_num": 1, "id": 200750, "target": "29,20"}, {"huodong_id": "201230", "prize": "1000006,60;82001020,4;82001040,4", "target_type": 0, "prize_des": "新服庆典第5天", "small_type": "1,累计消耗", "max_num": 1, "id": 200760, "target": "1,1"}, {"huodong_id": "201230", "prize": "82000009,2;12100006,4;12100000,4;12100030,4;82001040,4", "target_type": 0, "prize_des": "累计消费100元宝", "small_type": "1,累计消耗", "max_num": 1, "id": 200770, "target": "4,100"}, {"huodong_id": "201230", "prize": "82000009,4;12100006,8;12100000,8;12100030,8;82001040,4", "target_type": 0, "prize_des": "累计消费200元宝", "small_type": "1,累计消耗", "max_num": 1, "id": 200780, "target": "4,200"}, {"huodong_id": "201230", "prize": "82000009,6;12100006,12;12100000,12;12100030,12;82001040,4", "target_type": 0, "prize_des": "累计消费400元宝", "small_type": "1,累计消耗", "max_num": 1, "id": 200790, "target": "4,400"}, {"huodong_id": "201230", "prize": "82000009,8;12100006,16;12100000,16;12100030,16;82001040,4", "target_type": 0, "prize_des": "累计消费600元宝", "small_type": "1,累计消耗", "max_num": 1, "id": 200800, "target": "4,600"}, {"huodong_id": "201230", "prize": "82000009,10;12100006,20;12100000,20;12100030,20;82001040,4", "target_type": 0, "prize_des": "累计消费800元宝", "small_type": "1,累计消耗", "max_num": 1, "id": 200810, "target": "4,800"}, {"huodong_id": "201230", "prize": "1000026,50;12100006,4;12100000,4;12100030,4;82001040,4", "target_type": 0, "prize_des": "市场内的神秘商人处购买5次道具", "small_type": "2,神秘商人", "max_num": 1, "id": 200820, "target": "24,5"}, {"huodong_id": "201230", "prize": "1000026,100;12100006,8;12100000,8;12100030,8;82001040,4", "target_type": 0, "prize_des": "市场内的神秘商人处购买10次道具", "small_type": "2,神秘商人", "max_num": 1, "id": 200830, "target": "24,10"}, {"huodong_id": "201230", "prize": "1000026,200;12100006,12;12100000,12;12100030,12;82001040,4", "target_type": 0, "prize_des": "市场内的神秘商人处购买20次道具", "small_type": "2,神秘商人", "max_num": 1, "id": 200840, "target": "24,20"}, {"huodong_id": "201230", "prize": "1000026,300;12100006,16;12100000,16;12100030,16;82001040,4", "target_type": 0, "prize_des": "市场内的神秘商人处购买30次道具", "small_type": "2,神秘商人", "max_num": 1, "id": 200850, "target": "24,30"}, {"huodong_id": "201230", "prize": "1000026,400;12100006,20;12100000,20;12100030,20;82001040,4", "target_type": 0, "prize_des": "市场内的神秘商人处购买50次道具", "small_type": "2,神秘商人", "max_num": 1, "id": 200860, "target": "24,50"}, {"huodong_id": "201230", "prize": "12100067,2;12100006,4;12100000,4;12100030,4;82001040,4", "target_type": 0, "prize_des": "主城内加速时间达60分钟", "small_type": "3,飞速发展", "max_num": 1, "id": 200870, "target": "3,60"}, {"huodong_id": "201230", "prize": "12100067,3;12100006,8;12100000,8;12100030,8;82001040,4", "target_type": 0, "prize_des": "主城内加速时间达120分钟", "small_type": "3,飞速发展", "max_num": 1, "id": 200880, "target": "3,120"}, {"huodong_id": "201230", "prize": "12100067,4;12100006,12;12100000,12;12100030,12;82001040,4", "target_type": 0, "prize_des": "主城内加速时间达240分钟", "small_type": "3,飞速发展", "max_num": 1, "id": 200890, "target": "3,240"}, {"huodong_id": "201230", "prize": "12100067,5;12100006,16;12100000,16;12100030,16;82001040,4", "target_type": 0, "prize_des": "主城内加速时间达420分钟", "small_type": "3,飞速发展", "max_num": 1, "id": 200900, "target": "3,420"}, {"huodong_id": "201230", "prize": "12100067,6;12100006,20;12100000,20;12100030,20;82001040,4", "target_type": 0, "prize_des": "主城内加速时间达600分钟", "small_type": "3,飞速发展", "max_num": 1, "id": 200910, "target": "3,600"}, {"huodong_id": "201380", "prize": "12100006,10;12100000,10;8000002,1", "target_type": 0, "prize_des": "陆伯言携礼来贺！", "small_type": "", "max_num": 1, "id": 200920, "target": "2,1"}, {"huodong_id": "201120", "prize": "1000006,300", "target_type": 0, "prize_des": "累计充值达30元", "small_type": "", "max_num": 1, "id": 200930, "target": "5,30"}, {"huodong_id": "", "prize": "82005000,5;8000002,10;82001028,10;12100000,200;12100012,200", "target_type": 1, "prize_des": "每日充值68元，累计1天", "small_type": "", "max_num": 1, "id": 201000, "target": "6,68"}, {"huodong_id": "", "prize": "82005000,5;8000002,10;82001028,10;12100000,200;12100012,200", "target_type": 2, "prize_des": "每日充值68元，累计2天", "small_type": "", "max_num": 1, "id": 201010, "target": "6,68"}, {"huodong_id": "", "prize": "82005000,5;8000002,10;82001028,10;12100000,200;12100012,200", "target_type": 3, "prize_des": "每日充值68元，累计3天", "small_type": "", "max_num": 1, "id": 201020, "target": "6,68"}, {"huodong_id": "", "prize": "82005000,5;8000002,10;82001028,10;12100000,200;12100012,200", "target_type": 4, "prize_des": "每日充值68元，累计4天", "small_type": "", "max_num": 1, "id": 201030, "target": "6,68"}, {"huodong_id": "", "prize": "82005000,10;8000002,15;82001028,20;12100000,400;12100012,400", "target_type": 5, "prize_des": "每日充值68元，累计5天", "small_type": "", "max_num": 1, "id": 201040, "target": "6,68"}, {"huodong_id": "", "prize": "12100006,10;12100000,10", "target_type": 0, "prize_des": "新服保护", "small_type": "", "max_num": 1, "id": 201050, "target": "2,1"}, {"huodong_id": "2017070601", "prize": "12100037,1;82001020,4", "target_type": 0, "prize_des": "累计消费达1元宝", "small_type": "", "max_num": 1, "id": 201060, "target": "4,1"}, {"huodong_id": "2017070601", "prize": "12100067,2;82010109,2;12100006,20;12100000,20;12100012,20;12100024,20", "target_type": 0, "prize_des": "累计消费达300元宝", "small_type": "", "max_num": 1, "id": 201070, "target": "4,300"}, {"huodong_id": "2017070601", "prize": "12100067,4;82010109,4;12100006,80;12100000,80;12100012,80;12100024,80", "target_type": 0, "prize_des": "累计消费达750元宝", "small_type": "", "max_num": 1, "id": 201080, "target": "4,750"}, {"huodong_id": "2017070601", "prize": "12100067,6;82010109,6;12100006,120;12100000,120;12100012,120;12100024,120", "target_type": 0, "prize_des": "累计消费达1500元宝", "small_type": "", "max_num": 1, "id": 201090, "target": "4,1500"}, {"huodong_id": "2017070601", "prize": "12100067,8;82010109,8;12100006,160;12100000,160;12100012,160;12100024,160", "target_type": 0, "prize_des": "累计消费达3000元宝", "small_type": "", "max_num": 1, "id": 201100, "target": "4,3000"}, {"huodong_id": "2017070601", "prize": "12100067,12;82010109,10;12100006,240;12100000,240;12100012,240;12100024,240", "target_type": 0, "prize_des": "累计消费达5000元宝", "small_type": "", "max_num": 1, "id": 201110, "target": "4,5000"}, {"huodong_id": "2017070601", "prize": "12100067,24;8000002,20;8000034,20;8000010,20;8000018,20;12100024,320", "target_type": 0, "prize_des": "累计消费达10000元宝", "small_type": "", "max_num": 1, "id": 201120, "target": "4,10000"}, {"huodong_id": "2017070601", "prize": "12100067,36;5030316,1;8000002,30;12100006,600;12100000,600;12100012,600", "target_type": 0, "prize_des": "累计消费达20000元宝", "small_type": "", "max_num": 1, "id": 201130, "target": "4,20000"}, {"huodong_id": "2017070601", "prize": "12100067,48;5030318,1;8000002,40;12100006,800;12100000,800;12100012,800", "target_type": 0, "prize_des": "累计消费达30000元宝", "small_type": "", "max_num": 1, "id": 201140, "target": "4,30000"}, {"huodong_id": "2017070601", "prize": "12100067,60;5030312,1;8000002,60;12100006,1200;12100000,1200;12100012,1200", "target_type": 0, "prize_des": "累计消费达50000元宝", "small_type": "", "max_num": 1, "id": 201150, "target": "4,50000"}, {"huodong_id": "", "prize": "1,6100648,20,82003101,30;2,6100658,20,82003101,30;3,6100645,20,82003101,30;4,6100020,60,82003101,1;5,6100017,60,82003101,1;6,6100018,60,82003101,1;7,6100016,60,82003101,1;8,6100019,60,82003101,1", "target_type": 0, "prize_des": "", "small_type": "", "max_num": 1, "id": 201160, "target": "9,1"}, {"huodong_id": "201110", "prize": "9000007,1", "target_type": 0, "prize_des": "累计充值达1元", "small_type": "", "max_num": 1, "id": 201170, "target": "5,1"}, {"huodong_id": "201110", "prize": "8000002,6;1000026,500;6102040,20;12100000,40", "target_type": 0, "prize_des": "累计充值达88元", "small_type": "", "max_num": 1, "id": 201180, "target": "5,88"}, {"huodong_id": "201110", "prize": "8000002,12;1000026,1000;6102040,40;12100000,80", "target_type": 0, "prize_des": "累计充值达188元", "small_type": "", "max_num": 1, "id": 201190, "target": "5,188"}, {"huodong_id": "201110", "prize": "8000002,24;1000026,1500;6102040,80;12100000,120", "target_type": 0, "prize_des": "累计充值达388元", "small_type": "", "max_num": 1, "id": 201200, "target": "5,388"}, {"huodong_id": "201110", "prize": "5440106,1;8000002,48;6102040,100;12100000,200", "target_type": 0, "prize_des": "累计充值达888元", "small_type": "", "max_num": 1, "id": 201210, "target": "5,888"}, {"huodong_id": "201110", "prize": "5300102,1;8000002,64;12102003,40;6102040,150;12100000,400", "target_type": 0, "prize_des": "累计充值达1888元", "small_type": "", "max_num": 1, "id": 201220, "target": "5,1888"}, {"huodong_id": "201110", "prize": "5100310,1;5300106,1;8000002,80;12102003,60", "target_type": 0, "prize_des": "累计充值达3888元", "small_type": "", "max_num": 1, "id": 201230, "target": "5,3888"}, {"huodong_id": "201110", "prize": "9000007,1", "target_type": 0, "prize_des": "累计充值达1元", "small_type": "", "max_num": 1, "id": 201231, "target": "5,1"}, {"huodong_id": "201110", "prize": "8000002,6;1000026,500;6102040,20;12100000,40", "target_type": 0, "prize_des": "累计充值达88元", "small_type": "", "max_num": 1, "id": 201232, "target": "5,88"}, {"huodong_id": "201110", "prize": "8000002,12;1000026,1000;6102040,40;12100000,80", "target_type": 0, "prize_des": "累计充值达188元", "small_type": "", "max_num": 1, "id": 201233, "target": "5,188"}, {"huodong_id": "201110", "prize": "8000002,24;1000026,1500;6102040,80;12100000,120", "target_type": 0, "prize_des": "累计充值达388元", "small_type": "", "max_num": 1, "id": 201234, "target": "5,388"}, {"huodong_id": "201110", "prize": "5440100,1;8000002,48;6102040,100;12100000,200", "target_type": 0, "prize_des": "累计充值达888元", "small_type": "", "max_num": 1, "id": 201235, "target": "5,888"}, {"huodong_id": "201110", "prize": "5300104,1;8000002,64;12102003,40;6102040,150;12100000,400", "target_type": 0, "prize_des": "累计充值达1888元", "small_type": "", "max_num": 1, "id": 201236, "target": "5,1888"}, {"huodong_id": "201110", "prize": "5100310,1;5300100,1;8000002,80;12102003,60", "target_type": 0, "prize_des": "累计充值达3888元", "small_type": "", "max_num": 1, "id": 201237, "target": "5,3888"}, {"huodong_id": "201110", "prize": "9000007,1", "target_type": 0, "prize_des": "累计充值达1元", "small_type": "", "max_num": 1, "id": 201240, "target": "5,1"}, {"huodong_id": "201110", "prize": "8000002,6;1000026,500;6102040,20;12100000,40", "target_type": 0, "prize_des": "累计充值达88元", "small_type": "", "max_num": 1, "id": 201250, "target": "5,88"}, {"huodong_id": "201110", "prize": "8000002,12;1000026,1000;6102040,40;12100000,80", "target_type": 0, "prize_des": "累计充值达188元", "small_type": "", "max_num": 1, "id": 201260, "target": "5,188"}, {"huodong_id": "201110", "prize": "8000002,24;1000026,1500;6102040,80;12100000,120", "target_type": 0, "prize_des": "累计充值达388元", "small_type": "", "max_num": 1, "id": 201270, "target": "5,388"}, {"huodong_id": "201110", "prize": "5440107,1;8000002,48;6102040,100;12100000,200", "target_type": 0, "prize_des": "累计充值达888元", "small_type": "", "max_num": 1, "id": 201280, "target": "5,888"}, {"huodong_id": "201110", "prize": "5300103,1;8000002,64;12102003,40;6102040,150;12100000,400", "target_type": 0, "prize_des": "累计充值达1888元", "small_type": "", "max_num": 1, "id": 201290, "target": "5,1888"}, {"huodong_id": "201110", "prize": "5100310,1;5300107,1;8000002,80;12102003,60", "target_type": 0, "prize_des": "累计充值达3888元", "small_type": "", "max_num": 1, "id": 201300, "target": "5,3888"}, {"huodong_id": "201110", "prize": "9000014,1;5300105,1;5300101,1;8000002,120;12102003,80", "target_type": 0, "prize_des": "累计充值达8888元", "small_type": "", "max_num": 1, "id": 201310, "target": "5,8888"}, {"huodong_id": "", "prize": "9000007,1", "target_type": 0, "prize_des": "活动期间累计获得10玉石", "small_type": "", "max_num": 1, "id": 50001, "target": "45,10"}, {"huodong_id": "", "prize": "8000002,6;1000026,500;12100006,60;12100000,60;12100030,60", "target_type": 0, "prize_des": "活动期间累计获得888玉石", "small_type": "", "max_num": 1, "id": 50002, "target": "45,888"}, {"huodong_id": "", "prize": "8000002,12;1000026,1000;12100006,100;12100000,100;12100030,100", "target_type": 0, "prize_des": "活动期间累计获得1888玉石", "small_type": "", "max_num": 1, "id": 50003, "target": "45,1888"}, {"huodong_id": "", "prize": "8000002,24;1000026,1500;12100006,200;12100000,200;12100030,200", "target_type": 0, "prize_des": "活动期间累计获得3888玉石", "small_type": "", "max_num": 1, "id": 50004, "target": "45,3888"}, {"huodong_id": "", "prize": "8000002,48;1000026,1500;12100006,400;12100000,400;12100030,400", "target_type": 0, "prize_des": "活动期间累计获得8888玉石", "small_type": "", "max_num": 1, "id": 50005, "target": "45,8888"}, {"huodong_id": "", "prize": "8000002,72;1000026,1500;12100006,800;12100000,800;12100030,800", "target_type": 0, "prize_des": "活动期间累计获得18888玉石", "small_type": "", "max_num": 1, "id": 50006, "target": "45,18888"}, {"huodong_id": "", "prize": "5100310,1;12100006,1600;12100000,1600;12100030,1600", "target_type": 0, "prize_des": "活动期间累计获得38888玉石", "small_type": "", "max_num": 1, "id": 50007, "target": "45,38888"}, {"huodong_id": "", "prize": "9000008,1", "target_type": 0, "prize_des": "活动期间累计获得88888玉石", "small_type": "", "max_num": 1, "id": 50008, "target": "45,88888"}, {"huodong_id": "", "prize": "82000009,20;8000002,10;82001028,10;12100000,200;12100012,200", "target_type": 1, "prize_des": "每日获得680玉石，累计1天", "small_type": "", "max_num": 1, "id": 50101, "target": "46,680"}, {"huodong_id": "", "prize": "82000009,20;8000002,10;82001028,10;12100000,200;12100012,200", "target_type": 2, "prize_des": "每日获得680玉石，累计2天", "small_type": "", "max_num": 1, "id": 50102, "target": "46,680"}, {"huodong_id": "", "prize": "82000009,20;8000002,10;82001028,10;12100000,200;12100012,200", "target_type": 3, "prize_des": "每日获得680玉石，累计3天", "small_type": "", "max_num": 1, "id": 50103, "target": "46,680"}, {"huodong_id": "", "prize": "82000009,20;8000002,10;82001028,10;12100000,200;12100012,200", "target_type": 4, "prize_des": "每日获得680玉石，累计4天", "small_type": "", "max_num": 1, "id": 50104, "target": "46,680"}, {"huodong_id": "", "prize": "82000009,30;8000002,15;82001028,20;12100000,400;12100012,400", "target_type": 5, "prize_des": "每日获得680玉石，累计5天", "small_type": "", "max_num": 1, "id": 50105, "target": "46,680"}, {"huodong_id": "", "prize": "1000006,60;8000002,2;12100000,10;12100012,10", "target_type": 0, "prize_des": "活动期间累计获得300玉石", "small_type": "", "max_num": 1, "id": 50201, "target": "45,300"}, {"huodong_id": "2017070601", "prize": "12100037,1;82001020,4", "target_type": 0, "prize_des": "累计消费达1玉石", "small_type": "", "max_num": 1, "id": 50301, "target": "47,1"}, {"huodong_id": "2017070601", "prize": "12100067,2;82010109,2;12100006,20;12100000,20;12100012,20;12100024,20", "target_type": 0, "prize_des": "累计消费达300玉石", "small_type": "", "max_num": 1, "id": 50302, "target": "47,300"}, {"huodong_id": "2017070601", "prize": "12100067,4;82010109,4;12100006,80;12100000,80;12100012,80;12100024,80", "target_type": 0, "prize_des": "累计消费达750玉石", "small_type": "", "max_num": 1, "id": 50303, "target": "47,750"}, {"huodong_id": "2017070601", "prize": "12100067,6;82010109,6;12100006,120;12100000,120;12100012,120;12100024,120", "target_type": 0, "prize_des": "累计消费达1500玉石", "small_type": "", "max_num": 1, "id": 50304, "target": "47,1500"}, {"huodong_id": "2017070601", "prize": "12100067,8;82010109,8;12100006,160;12100000,160;12100012,160;12100024,160", "target_type": 0, "prize_des": "累计消费达3000玉石", "small_type": "", "max_num": 1, "id": 50305, "target": "47,3000"}, {"huodong_id": "2017070601", "prize": "12100067,12;82010109,10;12100006,240;12100000,240;12100012,240;12100024,240", "target_type": 0, "prize_des": "累计消费达5000玉石", "small_type": "", "max_num": 1, "id": 50306, "target": "47,5000"}, {"huodong_id": "2017070601", "prize": "12100067,24;8000002,20;8000034,20;8000010,20;8000018,20;12100024,320", "target_type": 0, "prize_des": "累计消费达10000玉石", "small_type": "", "max_num": 1, "id": 50307, "target": "47,10000"}, {"huodong_id": "", "prize": "1000006,50;82000005,2;12100036,2;82001028,1;8000057,1", "target_type": 0, "prize_des": "点击前往，给予好评，可以获得如下奖励", "small_type": "", "max_num": 1, "id": 2018131801, "target": "1,1"}, {"huodong_id": "", "prize": "1000006,50;82000005,2;12100036,2;82001028,1;8000057,1", "target_type": 0, "prize_des": "点击前往，给予好评，可以获得如下奖励", "small_type": "", "max_num": 1, "id": 2018131802, "target": "1,1"}, {"huodong_id": "", "prize": "12000090,80;8000002,5;82001027,1;82001029,1", "target_type": 1, "prize_des": "每日充值68元，累计1天", "small_type": "", "max_num": 1, "id": 201001, "target": "6,68"}, {"huodong_id": "", "prize": "12000090,80;8000002,5;82001027,1;82001029,1", "target_type": 1, "prize_des": "每日充值128元，累计1天", "small_type": "", "max_num": 1, "id": 201002, "target": "6,128"}, {"huodong_id": "", "prize": "12000090,240;8000002,15;82001027,3;82001029,1", "target_type": 1, "prize_des": "每日充值328元，累计1天", "small_type": "", "max_num": 1, "id": 201003, "target": "6,328"}, {"huodong_id": "", "prize": "12000090,80;8000002,5;82001027,1;82001029,1", "target_type": 2, "prize_des": "每日充值68元，累计2天", "small_type": "", "max_num": 1, "id": 201011, "target": "6,68"}, {"huodong_id": "", "prize": "12000090,80;8000002,5;82001027,1;82001029,1", "target_type": 2, "prize_des": "每日充值128元，累计2天", "small_type": "", "max_num": 1, "id": 201012, "target": "6,128"}, {"huodong_id": "", "prize": "12000090,240;8000002,15;82001027,3;82001029,1", "target_type": 2, "prize_des": "每日充值328元，累计2天", "small_type": "", "max_num": 1, "id": 201013, "target": "6,328"}, {"huodong_id": "", "prize": "12000090,80;8000002,5;82001027,1;82001029,1", "target_type": 3, "prize_des": "每日充值68元，累计3天", "small_type": "", "max_num": 1, "id": 201021, "target": "6,68"}, {"huodong_id": "", "prize": "12000090,80;8000002,5;82001027,1;82001029,1", "target_type": 3, "prize_des": "每日充值128元，累计3天", "small_type": "", "max_num": 1, "id": 201022, "target": "6,128"}, {"huodong_id": "", "prize": "12000090,240;8000002,15;82001027,3;82001029,1", "target_type": 3, "prize_des": "每日充值328元，累计3天", "small_type": "", "max_num": 1, "id": 201023, "target": "6,328"}, {"huodong_id": "", "prize": "12000090,80;8000002,5;82001027,1;82001029,1", "target_type": 4, "prize_des": "每日充值68元，累计4天", "small_type": "", "max_num": 1, "id": 201031, "target": "6,68"}, {"huodong_id": "", "prize": "12000090,80;8000002,5;82001027,1;82001029,1", "target_type": 4, "prize_des": "每日充值128元，累计4天", "small_type": "", "max_num": 1, "id": 201032, "target": "6,128"}, {"huodong_id": "", "prize": "12000090,240;8000002,15;82001027,3;82001029,1", "target_type": 4, "prize_des": "每日充值328元，累计4天", "small_type": "", "max_num": 1, "id": 201033, "target": "6,328"}, {"huodong_id": "", "prize": "12000090,80;8000002,10;82001027,2;82001029,2", "target_type": 5, "prize_des": "每日充值68元，累计5天", "small_type": "", "max_num": 1, "id": 201041, "target": "6,68"}, {"huodong_id": "", "prize": "12000090,80;8000002,10;82001027,2;82001029,2", "target_type": 5, "prize_des": "每日充值128元，累计5天", "small_type": "", "max_num": 1, "id": 201042, "target": "6,128"}, {"huodong_id": "", "prize": "5300106,1;12000090,240;8000002,30;82001027,6;82001029,2", "target_type": 5, "prize_des": "每日充值328元，累计5天", "small_type": "", "max_num": 1, "id": 201043, "target": "6,328"}, {"huodong_id": "", "prize": "9000007,1", "target_type": 0, "prize_des": "累计充值达1元", "small_type": "", "max_num": 1, "id": 203000, "target": "5,1"}, {"huodong_id": "", "prize": "8000002,6;1000026,500;6102040,20;12100000,40", "target_type": 0, "prize_des": "累计充值达88元", "small_type": "", "max_num": 1, "id": 203001, "target": "5,88"}, {"huodong_id": "", "prize": "9000016,1;8000002,12;1000026,1000;12100000,80", "target_type": 0, "prize_des": "累计充值达188元", "small_type": "", "max_num": 1, "id": 203002, "target": "5,188"}, {"huodong_id": "", "prize": "82005000,20;8000002,24;1000026,1500;12100000,120", "target_type": 0, "prize_des": "累计充值达388元", "small_type": "", "max_num": 1, "id": 203003, "target": "5,388"}, {"huodong_id": "", "prize": "5300103,1;82005000,30;8000002,48;12100000,200", "target_type": 0, "prize_des": "累计充值达888元", "small_type": "", "max_num": 1, "id": 203004, "target": "5,888"}, {"huodong_id": "", "prize": "5300109,1;82005000,40;8000002,64;12100000,400", "target_type": 0, "prize_des": "累计充值达1888元", "small_type": "", "max_num": 1, "id": 203005, "target": "5,1888"}, {"huodong_id": "", "prize": "5100310,1;5300111,1;82005000,60;8000002,80", "target_type": 0, "prize_des": "累计充值达3888元", "small_type": "", "max_num": 1, "id": 203006, "target": "5,3888"}, {"huodong_id": "", "prize": "9000017,1;12220050,1;5300101,1;8000002,120;12102003,100", "target_type": 0, "prize_des": "累计充值达8888元", "small_type": "", "max_num": 1, "id": 203007, "target": "5,8888"}, {"huodong_id": "", "prize": "82001020,4", "target_type": 0, "prize_des": "充值有礼", "small_type": "", "max_num": 1, "id": 204000, "target": "1,1"}, {"huodong_id": "", "prize": "82000009,5;82000011,1;12100024,50", "target_type": 0, "prize_des": "今日充值达6元", "small_type": "", "max_num": 1, "id": 204001, "target": "5,6"}, {"huodong_id": "", "prize": "82000009,10;82001029,1;8000002,5;12100024,100", "target_type": 0, "prize_des": "今日充值达30元", "small_type": "", "max_num": 1, "id": 204002, "target": "5,30"}, {"huodong_id": "", "prize": "82000009,15;82005000,2;8000002,10;12100024,150", "target_type": 0, "prize_des": "今日充值达68元", "small_type": "", "max_num": 1, "id": 204003, "target": "5,68"}, {"huodong_id": "", "prize": "82000009,20;82005000,5;8000002,15;12100024,200", "target_type": 0, "prize_des": "今日充值达128元", "small_type": "", "max_num": 1, "id": 204004, "target": "5,128"}, {"huodong_id": "", "prize": "82005001,50;82001020,4", "target_type": 0, "prize_des": "累计消费达1元宝", "small_type": "", "max_num": 1, "id": 205000, "target": "4,1"}, {"huodong_id": "", "prize": "82005001,100;8000002,2;12100000,20;12100012,20;12100024,20", "target_type": 0, "prize_des": "累计消费达300元宝", "small_type": "", "max_num": 1, "id": 205001, "target": "4,300"}, {"huodong_id": "", "prize": "82005001,150;8000002,3;12100000,40;12100012,40;12100024,40", "target_type": 0, "prize_des": "累计消费达750元宝", "small_type": "", "max_num": 1, "id": 205002, "target": "4,750"}, {"huodong_id": "", "prize": "82005001,250;8000002,5;12100000,80;12100012,80;12100024,80", "target_type": 0, "prize_des": "累计消费达1500元宝", "small_type": "", "max_num": 1, "id": 205003, "target": "4,1500"}, {"huodong_id": "", "prize": "82005001,500;8000002,10;12100000,160;12100012,160;12100024,160", "target_type": 0, "prize_des": "累计消费达3000元宝", "small_type": "", "max_num": 1, "id": 205004, "target": "4,3000"}, {"huodong_id": "", "prize": "82005001,1000;8000002,15;12100000,240;12100012,240;12100024,240", "target_type": 0, "prize_des": "累计消费达5000元宝", "small_type": "", "max_num": 1, "id": 205005, "target": "4,5000"}, {"huodong_id": "", "prize": "82005000,15;8000002,20;12100000,500;12100012,500;12100024,500", "target_type": 0, "prize_des": "累计消费达10000元宝", "small_type": "", "max_num": 1, "id": 205006, "target": "4,10000"}, {"huodong_id": "", "prize": "82005000,20;8000002,25;12100000,1000;12100012,1000;12100024,1000", "target_type": 0, "prize_des": "累计消费达20000元宝", "small_type": "", "max_num": 1, "id": 205007, "target": "4,20000"}, {"huodong_id": "", "prize": "82005000,25;8000002,30;12100000,1500;12100012,1500;12100024,1500", "target_type": 0, "prize_des": "累计消费达30000元宝", "small_type": "", "max_num": 1, "id": 205008, "target": "4,30000"}, {"huodong_id": "", "prize": "82005000,40;8000002,50;12100000,2500;12100012,2500;12100024,2500", "target_type": 0, "prize_des": "累计消费达50000元宝", "small_type": "", "max_num": 1, "id": 205009, "target": "4,50000"}, {"huodong_id": "", "prize": "82001020,4", "target_type": 0, "prize_des": "累计充值赠好礼", "small_type": "", "max_num": 1, "id": 206000, "target": "1,1"}, {"huodong_id": "", "prize": "9000007,1", "target_type": 0, "prize_des": "累计充值达1元", "small_type": "", "max_num": 1, "id": 206001, "target": "5,1"}, {"huodong_id": "", "prize": "8000002,6;1000026,500;6102040,20;12100000,40", "target_type": 0, "prize_des": "累计充值达88元", "small_type": "", "max_num": 1, "id": 206002, "target": "5,88"}, {"huodong_id": "", "prize": "9000102,1;8000002,12;1000026,1000;12100000,80", "target_type": 0, "prize_des": "累计充值达188元", "small_type": "", "max_num": 1, "id": 206003, "target": "5,188"}, {"huodong_id": "", "prize": "82010109,30;8000002,24;1000026,1500;12100000,120", "target_type": 0, "prize_des": "累计充值达388元", "small_type": "", "max_num": 1, "id": 206004, "target": "5,388"}, {"huodong_id": "", "prize": "5300105,1;82005000,30;8000002,48;12100000,200", "target_type": 0, "prize_des": "累计充值达888元", "small_type": "", "max_num": 1, "id": 206005, "target": "5,888"}, {"huodong_id": "", "prize": "5300109,1;82010109,60;8000002,64;12100000,400", "target_type": 0, "prize_des": "累计充值达1888元", "small_type": "", "max_num": 1, "id": 206006, "target": "5,1888"}, {"huodong_id": "", "prize": "5100310,1;5300107,1;82005000,60;8000002,80", "target_type": 0, "prize_des": "累计充值达3888元", "small_type": "", "max_num": 1, "id": 206007, "target": "5,3888"}, {"huodong_id": "", "prize": "9000104,1;5300101,1;82010109,120;8000002,120;12102003,100", "target_type": 0, "prize_des": "累计充值达8888元", "small_type": "", "max_num": 1, "id": 206008, "target": "5,8888"}, {"huodong_id": "", "prize": "82001020,4", "target_type": 0, "prize_des": "累计充值赠好礼", "small_type": "", "max_num": 1, "id": 207000, "target": "1,1"}, {"huodong_id": "", "prize": "9000007,1", "target_type": 0, "prize_des": "累计充值达1元", "small_type": "", "max_num": 1, "id": 207001, "target": "5,1"}, {"huodong_id": "", "prize": "82001027,5;8000002,6;1000026,500;12100000,40", "target_type": 0, "prize_des": "累计充值达88元", "small_type": "", "max_num": 1, "id": 207002, "target": "5,88"}, {"huodong_id": "", "prize": "82005000,15;8000002,12;1000026,1000;12100000,80", "target_type": 0, "prize_des": "累计充值达188元", "small_type": "", "max_num": 1, "id": 207003, "target": "5,188"}, {"huodong_id": "", "prize": "82006050,1;8000002,24;1000026,1500;12100000,120", "target_type": 0, "prize_des": "累计充值达388元", "small_type": "", "max_num": 1, "id": 207004, "target": "5,388"}, {"huodong_id": "", "prize": "82006050,1;82010109,50;8000002,48;12100000,200", "target_type": 0, "prize_des": "累计充值达888元", "small_type": "", "max_num": 1, "id": 207005, "target": "5,888"}, {"huodong_id": "", "prize": "82006050,1;82005000,60;8000002,64;12100000,400", "target_type": 0, "prize_des": "累计充值达1888元", "small_type": "", "max_num": 1, "id": 207006, "target": "5,1888"}, {"huodong_id": "", "prize": "9000113,1;82006050,1;82010109,100;8000002,80", "target_type": 0, "prize_des": "累计充值达3888元", "small_type": "", "max_num": 1, "id": 207007, "target": "5,3888"}, {"huodong_id": "", "prize": "82001027,2;12100067,2;12100006,20;12100000,20;12100030,20", "target_type": 0, "prize_des": "累计消费300玉石", "small_type": "", "max_num": 1, "id": 2000500, "target": "47,300"}, {"huodong_id": "", "prize": "9000007,1", "target_type": 0, "prize_des": "累计充值达1元", "small_type": "", "max_num": 1, "id": 2000600, "target": "5,1"}, {"huodong_id": "", "prize": "82003000,9;82001027,3;12100006,40;12100000,40;12100030,40;12100024,40", "target_type": 0, "prize_des": "累计充值达100元", "small_type": "", "max_num": 1, "id": 2000601, "target": "5,100"}, {"huodong_id": "", "prize": "82003000,18;82001027,5;12100006,80;12100000,80;12100030,80;12100024,80", "target_type": 0, "prize_des": "累计充值达200元", "small_type": "", "max_num": 1, "id": 2000602, "target": "5,200"}, {"huodong_id": "", "prize": "82003000,27;82001027,8;12100006,120;12100000,120;12100030,120;12100024,120", "target_type": 0, "prize_des": "累计充值达500元", "small_type": "", "max_num": 1, "id": 2000603, "target": "5,500"}, {"huodong_id": "", "prize": "82003000,36;82001027,12;12100006,200;12100000,200;12100030,200;12100024,200", "target_type": 0, "prize_des": "累计充值达1000元", "small_type": "", "max_num": 1, "id": 2000604, "target": "5,1000"}, {"huodong_id": "", "prize": "5300106,1;82001027,18;12100006,300;12100000,300;12100030,300;12100024,300", "target_type": 0, "prize_des": "累计充值达2000元", "small_type": "", "max_num": 1, "id": 2000605, "target": "5,2000"}, {"huodong_id": "", "prize": "5100310,1;82001027,24;12100006,400;12100000,400;12100030,400;12100024,400", "target_type": 0, "prize_des": "累计充值达3000元", "small_type": "", "max_num": 1, "id": 2000606, "target": "5,3000"}, {"huodong_id": "", "prize": "9000007,1", "target_type": 0, "prize_des": "累计充值达1元", "small_type": "", "max_num": 1, "id": 2000650, "target": "5,1"}, {"huodong_id": "", "prize": "82003000,9;82001027,3;12100006,40;12100000,40;12100030,40;12100024,40", "target_type": 0, "prize_des": "累计充值达100元", "small_type": "", "max_num": 1, "id": 2000651, "target": "5,100"}, {"huodong_id": "", "prize": "82003000,18;82001027,5;12100006,80;12100000,80;12100030,80;12100024,80", "target_type": 0, "prize_des": "累计充值达200元", "small_type": "", "max_num": 1, "id": 2000652, "target": "5,200"}, {"huodong_id": "", "prize": "82003000,27;82001027,8;12100006,120;12100000,120;12100030,120;12100024,120", "target_type": 0, "prize_des": "累计充值达500元", "small_type": "", "max_num": 1, "id": 2000653, "target": "5,500"}, {"huodong_id": "", "prize": "82003000,36;82001027,12;12100006,200;12100000,200;12100030,200;12100024,200", "target_type": 0, "prize_des": "累计充值达1000元", "small_type": "", "max_num": 1, "id": 2000654, "target": "5,1000"}, {"huodong_id": "", "prize": "5300102,1;82001027,18;12100006,300;12100000,300;12100030,300;12100024,300", "target_type": 0, "prize_des": "累计充值达2000元", "small_type": "", "max_num": 1, "id": 2000655, "target": "5,2000"}, {"huodong_id": "", "prize": "5100310,1;82001027,24;12100006,400;12100000,400;12100030,400;12100024,400", "target_type": 0, "prize_des": "累计充值达3000元", "small_type": "", "max_num": 1, "id": 2000656, "target": "5,3000"}, {"huodong_id": "", "prize": "9000007,1", "target_type": 0, "prize_des": "累计充值达1元", "small_type": "", "max_num": 1, "id": 2000700, "target": "5,1"}, {"huodong_id": "", "prize": "82003000,9;82001027,3;12100006,40;12100000,40;12100030,40;12100024,40", "target_type": 0, "prize_des": "累计充值达100元", "small_type": "", "max_num": 1, "id": 2000701, "target": "5,100"}, {"huodong_id": "", "prize": "82003000,18;82001027,5;12100006,80;12100000,80;12100030,80;12100024,80", "target_type": 0, "prize_des": "累计充值达200元", "small_type": "", "max_num": 1, "id": 2000702, "target": "5,200"}, {"huodong_id": "", "prize": "82003000,27;82001027,8;12100006,120;12100000,120;12100030,120;12100024,120", "target_type": 0, "prize_des": "累计充值达500元", "small_type": "", "max_num": 1, "id": 2000703, "target": "5,500"}, {"huodong_id": "", "prize": "82003000,36;82001027,12;12100006,200;12100000,200;12100030,200;12100024,200", "target_type": 0, "prize_des": "累计充值达1000元", "small_type": "", "max_num": 1, "id": 2000704, "target": "5,1000"}, {"huodong_id": "", "prize": "5300104,1;82001027,18;12100006,300;12100000,300;12100030,300;12100024,300", "target_type": 0, "prize_des": "累计充值达2000元", "small_type": "", "max_num": 1, "id": 2000705, "target": "5,2000"}, {"huodong_id": "", "prize": "5100310,1;82001027,24;12100006,400;12100000,400;12100030,400;12100024,400", "target_type": 0, "prize_des": "累计充值达3000元", "small_type": "", "max_num": 1, "id": 2000706, "target": "5,3000"}, {"huodong_id": "", "prize": "9000007,1", "target_type": 0, "prize_des": "累计充值达1元", "small_type": "", "max_num": 1, "id": 2000750, "target": "5,1"}, {"huodong_id": "", "prize": "82003000,9;82001027,3;12100006,40;12100000,40;12100030,40;12100024,40", "target_type": 0, "prize_des": "累计充值达100元", "small_type": "", "max_num": 1, "id": 2000751, "target": "5,100"}, {"huodong_id": "", "prize": "82003000,18;82001027,5;12100006,80;12100000,80;12100030,80;12100024,80", "target_type": 0, "prize_des": "累计充值达200元", "small_type": "", "max_num": 1, "id": 2000752, "target": "5,200"}, {"huodong_id": "", "prize": "82003000,27;82001027,8;12100006,120;12100000,120;12100030,120;12100024,120", "target_type": 0, "prize_des": "累计充值达500元", "small_type": "", "max_num": 1, "id": 2000753, "target": "5,500"}, {"huodong_id": "", "prize": "82003000,36;82001027,12;12100006,200;12100000,200;12100030,200;12100024,200", "target_type": 0, "prize_des": "累计充值达1000元", "small_type": "", "max_num": 1, "id": 2000754, "target": "5,1000"}, {"huodong_id": "", "prize": "5300108,1;82001027,18;12100006,300;12100000,300;12100030,300;12100024,300", "target_type": 0, "prize_des": "累计充值达2000元", "small_type": "", "max_num": 1, "id": 2000755, "target": "5,2000"}, {"huodong_id": "", "prize": "5100310,1;82001027,24;12100006,400;12100000,400;12100030,400;12100024,400", "target_type": 0, "prize_des": "累计充值达3000元", "small_type": "", "max_num": 1, "id": 2000756, "target": "5,3000"}, {"huodong_id": "", "prize": "9000007,1", "target_type": 0, "prize_des": "累计充值达1元", "small_type": "", "max_num": 1, "id": 2000800, "target": "5,1"}, {"huodong_id": "", "prize": "82003000,9;82001027,3;12100006,40;12100000,40;12100030,40;12100024,40", "target_type": 0, "prize_des": "累计充值达100元", "small_type": "", "max_num": 1, "id": 2000801, "target": "5,100"}, {"huodong_id": "", "prize": "82003000,18;82001027,5;12100006,80;12100000,80;12100030,80;12100024,80", "target_type": 0, "prize_des": "累计充值达200元", "small_type": "", "max_num": 1, "id": 2000802, "target": "5,200"}, {"huodong_id": "", "prize": "82003000,27;82001027,8;12100006,120;12100000,120;12100030,120;12100024,120", "target_type": 0, "prize_des": "累计充值达500元", "small_type": "", "max_num": 1, "id": 2000803, "target": "5,500"}, {"huodong_id": "", "prize": "82003000,36;82001027,12;12100006,200;12100000,200;12100030,200;12100024,200", "target_type": 0, "prize_des": "累计充值达1000元", "small_type": "", "max_num": 1, "id": 2000804, "target": "5,1000"}, {"huodong_id": "", "prize": "5300110,1;82001027,18;12100006,300;12100000,300;12100030,300;12100024,300", "target_type": 0, "prize_des": "累计充值达2000元", "small_type": "", "max_num": 1, "id": 2000805, "target": "5,2000"}, {"huodong_id": "", "prize": "5100310,1;82001027,24;12100006,400;12100000,400;12100030,400;12100024,400", "target_type": 0, "prize_des": "累计充值达3000元", "small_type": "", "max_num": 1, "id": 2000806, "target": "5,3000"}, {"huodong_id": "", "prize": "9000007,1", "target_type": 0, "prize_des": "累计充值达1元", "small_type": "", "max_num": 1, "id": 2000850, "target": "5,1"}, {"huodong_id": "", "prize": "82003000,9;82001027,3;12100006,40;12100000,40;12100030,40;12100024,40", "target_type": 0, "prize_des": "累计充值达100元", "small_type": "", "max_num": 1, "id": 2000851, "target": "5,100"}, {"huodong_id": "", "prize": "82003000,18;82001027,5;12100006,80;12100000,80;12100030,80;12100024,80", "target_type": 0, "prize_des": "累计充值达200元", "small_type": "", "max_num": 1, "id": 2000852, "target": "5,200"}, {"huodong_id": "", "prize": "82003000,27;82001027,8;12100006,120;12100000,120;12100030,120;12100024,120", "target_type": 0, "prize_des": "累计充值达500元", "small_type": "", "max_num": 1, "id": 2000853, "target": "5,500"}, {"huodong_id": "", "prize": "82003000,36;82001027,12;12100006,200;12100000,200;12100030,200;12100024,200", "target_type": 0, "prize_des": "累计充值达1000元", "small_type": "", "max_num": 1, "id": 2000854, "target": "5,1000"}, {"huodong_id": "", "prize": "5300100,1;82001027,18;12100006,300;12100000,300;12100030,300;12100024,300", "target_type": 0, "prize_des": "累计充值达2000元", "small_type": "", "max_num": 1, "id": 2000855, "target": "5,2000"}, {"huodong_id": "", "prize": "5100310,1;82001027,24;12100006,400;12100000,400;12100030,400;12100024,400", "target_type": 0, "prize_des": "累计充值达3000元", "small_type": "", "max_num": 1, "id": 2000856, "target": "5,3000"}, {"huodong_id": "", "prize": "9000007,1", "target_type": 0, "prize_des": "累计充值达1元", "small_type": "", "max_num": 1, "id": 2000900, "target": "5,1"}, {"huodong_id": "", "prize": "82003000,9;82001027,3;12100006,40;12100000,40;12100030,40;12100024,40", "target_type": 0, "prize_des": "累计充值达100元", "small_type": "", "max_num": 1, "id": 2000901, "target": "5,100"}, {"huodong_id": "", "prize": "82003000,18;82001027,5;12100006,80;12100000,80;12100030,80;12100024,80", "target_type": 0, "prize_des": "累计充值达200元", "small_type": "", "max_num": 1, "id": 2000902, "target": "5,200"}, {"huodong_id": "", "prize": "82003000,27;82001027,8;12100006,120;12100000,120;12100030,120;12100024,120", "target_type": 0, "prize_des": "累计充值达500元", "small_type": "", "max_num": 1, "id": 2000903, "target": "5,500"}, {"huodong_id": "", "prize": "82003000,36;82001027,12;12100006,200;12100000,200;12100030,200;12100024,200", "target_type": 0, "prize_des": "累计充值达1000元", "small_type": "", "max_num": 1, "id": 2000904, "target": "5,1000"}, {"huodong_id": "", "prize": "5300103,1;82001027,18;12100006,300;12100000,300;12100030,300;12100024,300", "target_type": 0, "prize_des": "累计充值达2000元", "small_type": "", "max_num": 1, "id": 2000905, "target": "5,2000"}, {"huodong_id": "", "prize": "5100310,1;82001027,24;12100006,400;12100000,400;12100030,400;12100024,400", "target_type": 0, "prize_des": "累计充值达3000元", "small_type": "", "max_num": 1, "id": 2000906, "target": "5,3000"}, {"huodong_id": "", "prize": "9000007,1", "target_type": 0, "prize_des": "累计充值达1元", "small_type": "", "max_num": 1, "id": 2000950, "target": "5,1"}, {"huodong_id": "", "prize": "82003000,9;82001027,3;12100006,40;12100000,40;12100030,40;12100024,40", "target_type": 0, "prize_des": "累计充值达100元", "small_type": "", "max_num": 1, "id": 2000951, "target": "5,100"}, {"huodong_id": "", "prize": "82003000,18;82001027,5;12100006,80;12100000,80;12100030,80;12100024,80", "target_type": 0, "prize_des": "累计充值达200元", "small_type": "", "max_num": 1, "id": 2000952, "target": "5,200"}, {"huodong_id": "", "prize": "82003000,27;82001027,8;12100006,120;12100000,120;12100030,120;12100024,120", "target_type": 0, "prize_des": "累计充值达500元", "small_type": "", "max_num": 1, "id": 2000953, "target": "5,500"}, {"huodong_id": "", "prize": "82003000,36;82001027,12;12100006,200;12100000,200;12100030,200;12100024,200", "target_type": 0, "prize_des": "累计充值达1000元", "small_type": "", "max_num": 1, "id": 2000954, "target": "5,1000"}, {"huodong_id": "", "prize": "5300107,1;82001027,18;12100006,300;12100000,300;12100030,300;12100024,300", "target_type": 0, "prize_des": "累计充值达2000元", "small_type": "", "max_num": 1, "id": 2000955, "target": "5,2000"}, {"huodong_id": "", "prize": "5100310,1;82001027,24;12100006,400;12100000,400;12100030,400;12100024,400", "target_type": 0, "prize_des": "累计充值达3000元", "small_type": "", "max_num": 1, "id": 2000956, "target": "5,3000"}, {"huodong_id": "", "prize": "9000007,1", "target_type": 0, "prize_des": "累计充值达1元", "small_type": "", "max_num": 1, "id": 2001000, "target": "5,1"}, {"huodong_id": "", "prize": "82003000,9;82001027,3;12100006,40;12100000,40;12100030,40;12100024,40", "target_type": 0, "prize_des": "累计充值达100元", "small_type": "", "max_num": 1, "id": 2001001, "target": "5,100"}, {"huodong_id": "", "prize": "82003000,18;82001027,5;12100006,80;12100000,80;12100030,80;12100024,80", "target_type": 0, "prize_des": "累计充值达200元", "small_type": "", "max_num": 1, "id": 2001002, "target": "5,200"}, {"huodong_id": "", "prize": "82003000,27;82001027,8;12100006,120;12100000,120;12100030,120;12100024,120", "target_type": 0, "prize_des": "累计充值达500元", "small_type": "", "max_num": 1, "id": 2001003, "target": "5,500"}, {"huodong_id": "", "prize": "82003000,36;82001027,12;12100006,200;12100000,200;12100030,200;12100024,200", "target_type": 0, "prize_des": "累计充值达1000元", "small_type": "", "max_num": 1, "id": 2001004, "target": "5,1000"}, {"huodong_id": "", "prize": "5300105,1;82001027,18;12100006,300;12100000,300;12100030,300;12100024,300", "target_type": 0, "prize_des": "累计充值达2000元", "small_type": "", "max_num": 1, "id": 2001005, "target": "5,2000"}, {"huodong_id": "", "prize": "5100310,1;82001027,24;12100006,400;12100000,400;12100030,400;12100024,400", "target_type": 0, "prize_des": "累计充值达3000元", "small_type": "", "max_num": 1, "id": 2001006, "target": "5,3000"}, {"huodong_id": "", "prize": "9000007,1", "target_type": 0, "prize_des": "累计充值达1元", "small_type": "", "max_num": 1, "id": 2001050, "target": "5,1"}, {"huodong_id": "", "prize": "82003000,9;82001027,3;12100006,40;12100000,40;12100030,40;12100024,40", "target_type": 0, "prize_des": "累计充值达100元", "small_type": "", "max_num": 1, "id": 2001051, "target": "5,100"}, {"huodong_id": "", "prize": "82003000,18;82001027,5;12100006,80;12100000,80;12100030,80;12100024,80", "target_type": 0, "prize_des": "累计充值达200元", "small_type": "", "max_num": 1, "id": 2001052, "target": "5,200"}, {"huodong_id": "", "prize": "82003000,27;82001027,8;12100006,120;12100000,120;12100030,120;12100024,120", "target_type": 0, "prize_des": "累计充值达500元", "small_type": "", "max_num": 1, "id": 2001053, "target": "5,500"}, {"huodong_id": "", "prize": "82003000,36;82001027,12;12100006,200;12100000,200;12100030,200;12100024,200", "target_type": 0, "prize_des": "累计充值达1000元", "small_type": "", "max_num": 1, "id": 2001054, "target": "5,1000"}, {"huodong_id": "", "prize": "5300109,1;82001027,18;12100006,300;12100000,300;12100030,300;12100024,300", "target_type": 0, "prize_des": "累计充值达2000元", "small_type": "", "max_num": 1, "id": 2001055, "target": "5,2000"}, {"huodong_id": "", "prize": "5100310,1;82001027,24;12100006,400;12100000,400;12100030,400;12100024,400", "target_type": 0, "prize_des": "累计充值达3000元", "small_type": "", "max_num": 1, "id": 2001056, "target": "5,3000"}, {"huodong_id": "", "prize": "9000007,1", "target_type": 0, "prize_des": "累计充值达1元", "small_type": "", "max_num": 1, "id": 2001100, "target": "5,1"}, {"huodong_id": "", "prize": "82003000,9;82001027,3;12100006,40;12100000,40;12100030,40;12100024,40", "target_type": 0, "prize_des": "累计充值达100元", "small_type": "", "max_num": 1, "id": 2001101, "target": "5,100"}, {"huodong_id": "", "prize": "82003000,18;82001027,5;12100006,80;12100000,80;12100030,80;12100024,80", "target_type": 0, "prize_des": "累计充值达200元", "small_type": "", "max_num": 1, "id": 2001102, "target": "5,200"}, {"huodong_id": "", "prize": "82003000,27;82001027,8;12100006,120;12100000,120;12100030,120;12100024,120", "target_type": 0, "prize_des": "累计充值达500元", "small_type": "", "max_num": 1, "id": 2001103, "target": "5,500"}, {"huodong_id": "", "prize": "82003000,36;82001027,12;12100006,200;12100000,200;12100030,200;12100024,200", "target_type": 0, "prize_des": "累计充值达1000元", "small_type": "", "max_num": 1, "id": 2001104, "target": "5,1000"}, {"huodong_id": "", "prize": "5300111,1;82001027,18;12100006,300;12100000,300;12100030,300;12100024,300", "target_type": 0, "prize_des": "累计充值达2000元", "small_type": "", "max_num": 1, "id": 2001105, "target": "5,2000"}, {"huodong_id": "", "prize": "5100310,1;82001027,24;12100006,400;12100000,400;12100030,400;12100024,400", "target_type": 0, "prize_des": "累计充值达3000元", "small_type": "", "max_num": 1, "id": 2001106, "target": "5,3000"}, {"huodong_id": "", "prize": "9000007,1", "target_type": 0, "prize_des": "累计充值达1元", "small_type": "", "max_num": 1, "id": 2001150, "target": "5,1"}, {"huodong_id": "", "prize": "82003000,9;82001027,3;12100006,40;12100000,40;12100030,40;12100024,40", "target_type": 0, "prize_des": "累计充值达100元", "small_type": "", "max_num": 1, "id": 2001151, "target": "5,100"}, {"huodong_id": "", "prize": "82003000,18;82001027,5;12100006,80;12100000,80;12100030,80;12100024,80", "target_type": 0, "prize_des": "累计充值达200元", "small_type": "", "max_num": 1, "id": 2001152, "target": "5,200"}, {"huodong_id": "", "prize": "82003000,27;82001027,8;12100006,120;12100000,120;12100030,120;12100024,120", "target_type": 0, "prize_des": "累计充值达500元", "small_type": "", "max_num": 1, "id": 2001153, "target": "5,500"}, {"huodong_id": "", "prize": "82003000,36;82001027,12;12100006,200;12100000,200;12100030,200;12100024,200", "target_type": 0, "prize_des": "累计充值达1000元", "small_type": "", "max_num": 1, "id": 2001154, "target": "5,1000"}, {"huodong_id": "", "prize": "5300101,1;82001027,18;12100006,300;12100000,300;12100030,300;12100024,300", "target_type": 0, "prize_des": "累计充值达2000元", "small_type": "", "max_num": 1, "id": 2001155, "target": "5,2000"}, {"huodong_id": "", "prize": "5100310,1;82001027,24;12100006,400;12100000,400;12100030,400;12100024,400", "target_type": 0, "prize_des": "累计充值达3000元", "small_type": "", "max_num": 1, "id": 2001156, "target": "5,3000"}, {"huodong_id": "", "prize": "12100037,1;82001020,4", "target_type": 0, "prize_des": "累计消费达10元宝", "small_type": "", "max_num": 1, "id": 2001200, "target": "4,10"}, {"huodong_id": "", "prize": "12100067,6;8000002,10;12100006,20;12100000,20;12100030,20;12100024,20", "target_type": 0, "prize_des": "累计消费达1000元宝", "small_type": "", "max_num": 1, "id": 2001201, "target": "4,1000"}, {"huodong_id": "", "prize": "12100067,8;8000002,15;12100006,40;12100000,40;12100030,40;12100024,40", "target_type": 0, "prize_des": "累计消费达2000元宝", "small_type": "", "max_num": 1, "id": 2001202, "target": "4,2000"}, {"huodong_id": "", "prize": "12100067,12;8000002,20;12100006,60;12100000,60;12100030,60;12100024,60", "target_type": 0, "prize_des": "累计消费达5000元宝", "small_type": "", "max_num": 1, "id": 2001203, "target": "4,5000"}, {"huodong_id": "", "prize": "12100067,24;8000002,30;12100006,100;12100000,100;12100030,100;12100024,100", "target_type": 0, "prize_des": "累计消费达10000元宝", "small_type": "", "max_num": 1, "id": 2001204, "target": "4,10000"}, {"huodong_id": "", "prize": "12100067,36;8000002,40;12100006,150;12100000,150;12100030,150;12100024,150", "target_type": 0, "prize_des": "累计消费达20000元宝", "small_type": "", "max_num": 1, "id": 2001205, "target": "4,20000"}, {"huodong_id": "", "prize": "12100067,48;8000002,50;12100006,200;12100000,200;12100030,200;12100024,200", "target_type": 0, "prize_des": "累计消费达30000元宝", "small_type": "", "max_num": 1, "id": 2001206, "target": "4,30000"}, {"huodong_id": "", "prize": "12100037,1;82001020,4", "target_type": 0, "prize_des": "累计消费达10元宝", "small_type": "", "max_num": 1, "id": 2001250, "target": "4,10"}, {"huodong_id": "", "prize": "82010109,4;8000002,10;12100006,20;12100000,20;12100030,20;12100024,20", "target_type": 0, "prize_des": "累计消费达1000元宝", "small_type": "", "max_num": 1, "id": 2001251, "target": "4,1000"}, {"huodong_id": "", "prize": "82010109,6;8000002,15;12100006,40;12100000,40;12100030,40;12100024,40", "target_type": 0, "prize_des": "累计消费达2000元宝", "small_type": "", "max_num": 1, "id": 2001252, "target": "4,2000"}, {"huodong_id": "", "prize": "82010109,10;8000002,20;12100006,60;12100000,60;12100030,60;12100024,60", "target_type": 0, "prize_des": "累计消费达5000元宝", "small_type": "", "max_num": 1, "id": 2001253, "target": "4,5000"}, {"huodong_id": "", "prize": "82010109,15;8000002,30;12100006,100;12100000,100;12100030,100;12100024,100", "target_type": 0, "prize_des": "累计消费达10000元宝", "small_type": "", "max_num": 1, "id": 2001254, "target": "4,10000"}, {"huodong_id": "", "prize": "82010109,20;8000002,40;12100006,150;12100000,150;12100030,150;12100024,150", "target_type": 0, "prize_des": "累计消费达20000元宝", "small_type": "", "max_num": 1, "id": 2001255, "target": "4,20000"}, {"huodong_id": "", "prize": "82010109,30;8000002,50;12100006,200;12100000,200;12100030,200;12100024,200", "target_type": 0, "prize_des": "累计消费达30000元宝", "small_type": "", "max_num": 1, "id": 2001256, "target": "4,30000"}, {"huodong_id": "", "prize": "12100037,1;82001020,4", "target_type": 0, "prize_des": "累计消费达10元宝", "small_type": "", "max_num": 1, "id": 2001300, "target": "4,10"}, {"huodong_id": "", "prize": "82001027,2;8000002,10;12100006,20;12100000,20;12100030,20;12100024,20", "target_type": 0, "prize_des": "累计消费达1000元宝", "small_type": "", "max_num": 1, "id": 2001301, "target": "4,1000"}, {"huodong_id": "", "prize": "82001027,3;8000002,15;12100006,40;12100000,40;12100030,40;12100024,40", "target_type": 0, "prize_des": "累计消费达2000元宝", "small_type": "", "max_num": 1, "id": 2001302, "target": "4,2000"}, {"huodong_id": "", "prize": "82001027,5;8000002,20;12100006,60;12100000,60;12100030,60;12100024,60", "target_type": 0, "prize_des": "累计消费达5000元宝", "small_type": "", "max_num": 1, "id": 2001303, "target": "4,5000"}, {"huodong_id": "", "prize": "82001027,8;8000002,30;12100006,100;12100000,100;12100030,100;12100024,100", "target_type": 0, "prize_des": "累计消费达10000元宝", "small_type": "", "max_num": 1, "id": 2001304, "target": "4,10000"}, {"huodong_id": "", "prize": "82001027,10;8000002,40;12100006,150;12100000,150;12100030,150;12100024,150", "target_type": 0, "prize_des": "累计消费达20000元宝", "small_type": "", "max_num": 1, "id": 2001305, "target": "4,20000"}, {"huodong_id": "", "prize": "82001027,15;8000002,50;12100006,200;12100000,200;12100030,200;12100024,200", "target_type": 0, "prize_des": "累计消费达30000元宝", "small_type": "", "max_num": 1, "id": 2001306, "target": "4,30000"}, {"huodong_id": "", "prize": "12100037,1;82001020,4", "target_type": 0, "prize_des": "累计消费达10元宝", "small_type": "", "max_num": 1, "id": 2001350, "target": "4,10"}, {"huodong_id": "", "prize": "82000009,6;8000002,10;12100006,20;12100000,20;12100030,20;12100024,20", "target_type": 0, "prize_des": "累计消费达1000元宝", "small_type": "", "max_num": 1, "id": 2001351, "target": "4,1000"}, {"huodong_id": "", "prize": "82000009,8;8000002,15;12100006,40;12100000,40;12100030,40;12100024,40", "target_type": 0, "prize_des": "累计消费达2000元宝", "small_type": "", "max_num": 1, "id": 2001352, "target": "4,2000"}, {"huodong_id": "", "prize": "82000009,12;8000002,20;12100006,60;12100000,60;12100030,60;12100024,60", "target_type": 0, "prize_des": "累计消费达5000元宝", "small_type": "", "max_num": 1, "id": 2001353, "target": "4,5000"}, {"huodong_id": "", "prize": "82000009,24;8000002,30;12100006,100;12100000,100;12100030,100;12100024,100", "target_type": 0, "prize_des": "累计消费达10000元宝", "small_type": "", "max_num": 1, "id": 2001354, "target": "4,10000"}, {"huodong_id": "", "prize": "82000009,36;8000002,40;12100006,150;12100000,150;12100030,150;12100024,150", "target_type": 0, "prize_des": "累计消费达20000元宝", "small_type": "", "max_num": 1, "id": 2001355, "target": "4,20000"}, {"huodong_id": "", "prize": "82000009,48;8000002,50;12100006,200;12100000,200;12100030,200;12100024,200", "target_type": 0, "prize_des": "累计消费达30000元宝", "small_type": "", "max_num": 1, "id": 2001356, "target": "4,30000"}, {"huodong_id": "", "prize": "12100037,1;82001020,4", "target_type": 0, "prize_des": "累计消费达10元宝", "small_type": "", "max_num": 1, "id": 2001400, "target": "4,10"}, {"huodong_id": "", "prize": "12102003,15;8000002,10;12100006,20;12100000,20;12100030,20;12100024,20", "target_type": 0, "prize_des": "累计消费达1000元宝", "small_type": "", "max_num": 1, "id": 2001401, "target": "4,1000"}, {"huodong_id": "", "prize": "12102003,25;8000002,15;12100006,40;12100000,40;12100030,40;12100024,40", "target_type": 0, "prize_des": "累计消费达2000元宝", "small_type": "", "max_num": 1, "id": 2001402, "target": "4,2000"}, {"huodong_id": "", "prize": "12102003,40;8000002,20;12100006,60;12100000,60;12100030,60;12100024,60", "target_type": 0, "prize_des": "累计消费达5000元宝", "small_type": "", "max_num": 1, "id": 2001403, "target": "4,5000"}, {"huodong_id": "", "prize": "12102003,75;8000002,30;12100006,100;12100000,100;12100030,100;12100024,100", "target_type": 0, "prize_des": "累计消费达10000元宝", "small_type": "", "max_num": 1, "id": 2001404, "target": "4,10000"}, {"huodong_id": "", "prize": "12102003,100;8000002,40;12100006,150;12100000,150;12100030,150;12100024,150", "target_type": 0, "prize_des": "累计消费达20000元宝", "small_type": "", "max_num": 1, "id": 2001405, "target": "4,20000"}, {"huodong_id": "", "prize": "12102003,150;8000002,50;12100006,200;12100000,200;12100030,200;12100024,200", "target_type": 0, "prize_des": "累计消费达30000元宝", "small_type": "", "max_num": 1, "id": 2001406, "target": "4,30000"}, {"huodong_id": "", "prize": "12100037,1;82001020,4", "target_type": 0, "prize_des": "累计消费达10元宝", "small_type": "", "max_num": 1, "id": 2001450, "target": "4,10"}, {"huodong_id": "", "prize": "8000002,28;12100006,20;12100000,20;12100030,20;12100024,20", "target_type": 0, "prize_des": "累计消费达1000元宝", "small_type": "", "max_num": 1, "id": 2001451, "target": "4,1000"}, {"huodong_id": "", "prize": "8000002,38;12100006,40;12100000,40;12100030,40;12100024,40", "target_type": 0, "prize_des": "累计消费达2000元宝", "small_type": "", "max_num": 1, "id": 2001452, "target": "4,2000"}, {"huodong_id": "", "prize": "8000002,48;12100006,60;12100000,60;12100030,60;12100024,60", "target_type": 0, "prize_des": "累计消费达5000元宝", "small_type": "", "max_num": 1, "id": 2001453, "target": "4,5000"}, {"huodong_id": "", "prize": "8000002,68;12100006,100;12100000,100;12100030,100;12100024,100", "target_type": 0, "prize_des": "累计消费达10000元宝", "small_type": "", "max_num": 1, "id": 2001454, "target": "4,10000"}, {"huodong_id": "", "prize": "8000002,88;12100006,150;12100000,150;12100030,150;12100024,150", "target_type": 0, "prize_des": "累计消费达20000元宝", "small_type": "", "max_num": 1, "id": 2001455, "target": "4,20000"}, {"huodong_id": "", "prize": "8000002,108;12100006,200;12100000,200;12100030,200;12100024,200", "target_type": 0, "prize_des": "累计消费达30000元宝", "small_type": "", "max_num": 1, "id": 2001456, "target": "4,30000"}]