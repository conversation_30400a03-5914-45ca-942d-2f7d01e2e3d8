<?xml version="1.0" encoding="UTF-8" ?>
<xsd:schema xmlns="http://www.springframework.org/schema/data/jpa"
	xmlns:xsd="http://www.w3.org/2001/XMLSchema" 
	xmlns:tool="http://www.springframework.org/schema/tool"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:repository="http://www.springframework.org/schema/data/repository"
	targetNamespace="http://www.springframework.org/schema/data/jpa"
	elementFormDefault="qualified" attributeFormDefault="unqualified">

	<xsd:import namespace="http://www.springframework.org/schema/tool" />
	<xsd:import namespace="http://www.springframework.org/schema/context" />
	<xsd:import namespace="http://www.springframework.org/schema/data/repository"
		schemaLocation="http://www.springframework.org/schema/data/repository/spring-repository-1.0.xsd" />

	<xsd:complexType name="jpa-repository">
		<xsd:complexContent>
			<xsd:extension base="repository:repository">
				<xsd:attributeGroup ref="repository:transactional-repository-attributes" />
				<xsd:attribute name="entity-manager-factory-ref" type="entityManagerFactoryRef" />
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="repositories">
		<xsd:complexType>
			<xsd:complexContent>
				<xsd:extension base="repository:repositories">
					<xsd:sequence>
						<xsd:element name="repository" minOccurs="0" maxOccurs="unbounded" type="jpa-repository" />
					</xsd:sequence>
					<xsd:attributeGroup ref="repository:transactional-repository-attributes" />
					<xsd:attribute name="entity-manager-factory-ref" type="entityManagerFactoryRef" />
				</xsd:extension>
			</xsd:complexContent>
		</xsd:complexType>
	</xsd:element>

	<xsd:element name="auditing">
		<xsd:complexType>
			<xsd:attribute name="auditor-aware-ref">
				<xsd:annotation>
					<xsd:appinfo>
						<tool:annotation kind="ref">
							<tool:assignable-to	type="org.springframework.data.domain.AuditorAware" />
						</tool:annotation>
					</xsd:appinfo>
				</xsd:annotation>
			</xsd:attribute>
			<xsd:attribute name="set-dates" default="true" type="xsd:boolean" />
			<xsd:attribute name="date-time-provider-ref">
				<xsd:annotation>
					<xsd:appinfo>
						<tool:annotation kind="ref">
							<tool:assignable-to type="org.springframework.data.jpa.domain.support.DateTimeProvider" />
						</tool:annotation>
					</xsd:appinfo>
				</xsd:annotation>
			</xsd:attribute>
		</xsd:complexType>
	</xsd:element>

	<xsd:simpleType name="entityManagerFactoryRef">
		<xsd:annotation>
			<xsd:appinfo>
				<tool:annotation kind="ref">
					<tool:assignable-to type="org.springframework.orm.jpa.AbstractEntityManagerFactoryBean" />
				</tool:annotation>
			</xsd:appinfo>
		</xsd:annotation>
		<xsd:union memberTypes="xsd:string" />
	</xsd:simpleType>


</xsd:schema>