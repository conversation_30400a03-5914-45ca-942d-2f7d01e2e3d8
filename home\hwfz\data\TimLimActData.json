[{"desc_long": "低级探索券探索1次\\s\\s\\s\\s1000积分\\n高级探索券探索1次\\s\\s\\s\\s10000积分\\n元宝探索1次\\s\\s\\s\\s10000积分", "rank_reward_type": 1, "point_reward": "67000,82000001,1;200000,8000002,25;400000,12201070,1", "activity_type": 4, "id": 1, "desc_short": "探索的次数越多，获得的积分越多", "name": "武将探索"}, {"desc_long": "采集10铜钱\\s\\s\\s\\s1积分\\n采集10粮食\\s\\s\\s\\s1积分\\n采集10木材\\s\\s\\s\\s1积分\\n采集10铁矿\\s\\s\\s\\s10积分\\n采集10黄金\\s\\s\\s\\s100积分\\n采集10元宝\\s\\s\\s\\s1000积分", "rank_reward_type": 2, "point_reward": "310000,82000002,1;950000,12100038,1;1900000,1000006,150", "activity_type": 1, "id": 2, "desc_short": "采集的资源越多、越贵重，获得的积分越多", "name": "采集资源"}, {"desc_long": "消灭单个1级怪物\\s\\s\\s\\s500积分\\n消灭单个2级怪物\\s\\s\\s\\s1000积分\\n消灭单个3级怪物\\s\\s\\s\\s1500积分\\n消灭单个4级怪物\\s\\s\\s\\s2000积分\\n消灭单个5级怪物\\s\\s\\s\\s2500积分\\n消灭单个6级怪物\\s\\s\\s\\s3000积分\\n消灭单个7级怪物\\s\\s\\s\\s3500积分\\n消灭单个8级怪物\\s\\s\\s\\s4000积分\\n消灭单个9级怪物\\s\\s\\s\\s4500积分\\n消灭单个10级怪物\\s\\s\\s\\s5000积分\\n消灭单个11级怪物\\s\\s\\s\\s5600积分\\n消灭单个12级怪物\\s\\s\\s\\s6200积分\\n消灭单个13级怪物\\s\\s\\s\\s6800积分\\n消灭单个14级怪物\\s\\s\\s\\s7400积分\\n消灭单个15级怪物\\s\\s\\s\\s8000积分\\n消灭单个16级怪物\\s\\s\\s\\s8600积分\\n消灭单个17级怪物\\s\\s\\s\\s9200积分\\n消灭单个18级怪物\\s\\s\\s\\s9800积分\\n消灭单个19级怪物\\s\\s\\s\\s10400积分\\n消灭单个20级怪物\\s\\s\\s\\s11000积分\\n消灭单个21级怪物\\s\\s\\s\\s11800积分\\n消灭单个22级怪物\\s\\s\\s\\s12600积分\\n消灭单个23级怪物\\s\\s\\s\\s13400积分\\n消灭单个24级怪物\\s\\s\\s\\s14200积分\\n消灭单个25级怪物\\s\\s\\s\\s15000积分\\n消灭单个26级怪物\\s\\s\\s\\s15800积分\\n消灭单个27级怪物\\s\\s\\s\\s16600积分\\n消灭单个28级怪物\\s\\s\\s\\s17400积分\\n消灭单个29级怪物\\s\\s\\s\\s18200积分\\n消灭单个30级怪物\\s\\s\\s\\s19000积分", "rank_reward_type": 2, "point_reward": "16000,8000002,1;50000,8000061,1;100000,1000006,150", "activity_type": 5, "id": 3, "desc_short": "消灭的野外系统军队等级越高，获得的积分越多", "name": "消灭野外系统军队"}, {"desc_long": "招募单个1级兵\\s\\s\\s\\s15积分\\n招募单个2级兵\\s\\s\\s\\s60积分\\n招募单个3级兵\\s\\s\\s\\s180积分\\n招募单个4级兵\\s\\s\\s\\s400积分\\n招募单个5级兵\\s\\s\\s\\s700积分\\n招募单个6级兵\\s\\s\\s\\s1000积分\\n", "rank_reward_type": 2, "point_reward": "105000,8000065,1;315000,8000058,1;630000,1000006,150", "activity_type": 2, "id": 4, "desc_short": "训练的士兵等级越高，获得的积分越多", "name": "招募士兵"}, {"desc_long": "歼灭单个1级兵\\s\\s\\s\\s3积分\\n歼灭单个2级兵\\s\\s\\s\\s12积分\\n歼灭单个3级兵\\s\\s\\s\\s36积分\\n歼灭单个4级兵\\s\\s\\s\\s80积分\\n歼灭单个5级兵\\s\\s\\s\\s140积分\\n歼灭单个6级兵\\s\\s\\s\\s200积分\\n", "rank_reward_type": 2, "point_reward": "120000,8000002,3;372000,82000011,1;756000,1000006,500", "activity_type": 6, "id": 5, "desc_short": "尽你所能的攻击敌军吧，士兵等级越高伤亡越多，获得的积分越多", "name": "战意滔天"}, {"desc_long": "低级探索券探索1次\\s\\s\\s\\s1000积分\\n高级探索券探索1次\\s\\s\\s\\s10000积分\\n元宝探索1次\\s\\s\\s\\s10000积分", "rank_reward_type": 200, "point_reward": "67000,82000010,1;200000,8000002,5;400000,12201070,1", "activity_type": 4, "id": 6, "desc_short": "探索的次数越多，获得的积分越多", "name": "武将探索"}, {"desc_long": "低级探索券探索1次\\s\\s\\s\\s1000积分\\n高级探索券探索1次\\s\\s\\s\\s10000积分\\n元宝探索1次\\s\\s\\s\\s10000积分", "rank_reward_type": 201, "point_reward": "67000,82000001,1;200000,8000002,25;400000,12201079,1", "activity_type": 4, "id": 7, "desc_short": "探索的次数越多，获得的积分越多", "name": "武将探索"}, {"desc_long": "低级探索券探索1次\\s\\s\\s\\s1000积分\\n高级探索券探索1次\\s\\s\\s\\s10000积分\\n元宝探索1次\\s\\s\\s\\s10000积分", "rank_reward_type": 202, "point_reward": "67000,82000001,1;200000,8000002,25;400000,12201040,1", "activity_type": 4, "id": 8, "desc_short": "探索的次数越多，获得的积分越多", "name": "武将探索"}, {"desc_long": "探索陵寝\\s\\s\\s\\s20000积分", "rank_reward_type": 25, "point_reward": "50000,8000057,1;100000,8000065,1;200000,82001028,5", "activity_type": 7, "id": 9, "desc_short": "探索陵寝，探索的陵寝数量越多，获得的积分越多。", "name": "探索陵寝"}, {"desc_long": "拜访宝箱\\s\\s\\s\\s10000积分", "rank_reward_type": 24, "point_reward": "50000,8000057,1;100000,8000065,1;200000,82001028,5", "activity_type": 8, "id": 10, "desc_short": "拜访宝箱的次数越多，获得的积分越多。", "name": "拜访宝箱"}, {"desc_long": "采集10铜钱\\s\\s\\s\\s1积分\\n采集10粮食\\s\\s\\s\\s1积分\\n采集10木材\\s\\s\\s\\s1积分\\n采集10铁矿\\s\\s\\s\\s10积分\\n采集10黄金\\s\\s\\s\\s100积分\\n采集10元宝\\s\\s\\s\\s1000积分", "rank_reward_type": 27, "point_reward": "60000,8000057,1;120000,8000065,1;250000,82001028,5", "activity_type": 1, "id": 11, "desc_short": "采集的资源越多、越贵重，获得的积分越多。", "name": "采集资源"}, {"desc_long": "消灭单个1级野外系统军队\\s\\s\\s\\s500积分\\n消灭单个2级野外系统军队\\s\\s\\s\\s1000积分\\n消灭单个3级野外系统军队\\s\\s\\s\\s1500积分\\n消灭单个4级野外系统军队\\s\\s\\s\\s2000积分\\n消灭单个5级野外系统军队\\s\\s\\s\\s2500积分\\n消灭单个6级野外系统军队\\s\\s\\s\\s3000积分\\n消灭单个7级野外系统军队\\s\\s\\s\\s3500积分\\n消灭单个8级野外系统军队\\s\\s\\s\\s4000积分\\n消灭单个9级野外系统军队\\s\\s\\s\\s4500积分\\n消灭单个10级野外系统军队\\s\\s\\s\\s5000积分\\n消灭单个11级野外系统军队\\s\\s\\s\\s5600积分\\n消灭单个12级野外系统军队\\s\\s\\s\\s6200积分\\n消灭单个13级野外系统军队\\s\\s\\s\\s6800积分\\n消灭单个14级野外系统军队\\s\\s\\s\\s7400积分\\n消灭单个15级野外系统军队\\s\\s\\s\\s8000积分\\n消灭单个16级野外系统军队\\s\\s\\s\\s8600积分\\n消灭单个17级野外系统军队\\s\\s\\s\\s9200积分\\n消灭单个18级野外系统军队\\s\\s\\s\\s9800积分\\n消灭单个19级野外系统军队\\s\\s\\s\\s10400积分\\n消灭单个20级野外系统军队\\s\\s\\s\\s11000积分\\n消灭单个21级野外系统军队\\s\\s\\s\\s11800积分\\n消灭单个22级野外系统军队\\s\\s\\s\\s12600积分\\n消灭单个23级野外系统军队\\s\\s\\s\\s13400积分\\n消灭单个24级野外系统军队\\s\\s\\s\\s14200积分\\n消灭单个25级野外系统军队\\s\\s\\s\\s15000积分\\n消灭单个26级野外系统军队\\s\\s\\s\\s15800积分\\n消灭单个27级野外系统军队\\s\\s\\s\\s16600积分\\n消灭单个28级野外系统军队\\s\\s\\s\\s17400积分\\n消灭单个29级野外系统军队\\s\\s\\s\\s18200积分\\n消灭单个30级野外系统军队\\s\\s\\s\\s19000积分", "rank_reward_type": 26, "point_reward": "80000,8000057,1;150000,8000065,1;300000,82001028,5", "activity_type": 5, "id": 12, "desc_short": "消灭的野外系统军队等级越高，获得的积分越多。", "name": "消灭野外系统军队"}, {"desc_long": "训练单个1级兵\\s\\s\\s\\s10积分\\n训练单个2级兵\\s\\s\\s\\s30积分\\n训练单个3级兵\\s\\s\\s\\s100积分\\n训练单个4级兵\\s\\s\\s\\s180积分\\n训练单个5级兵\\s\\s\\s\\s270积分\\n训练单个6级兵\\s\\s\\s\\s360积分", "rank_reward_type": 28, "point_reward": "80000,8000057,1;150000,8000065,1;300000,82001028,5", "activity_type": 2, "id": 13, "desc_short": "训练的士兵等级越高，获得的积分越多。", "name": "训练士兵"}, {"desc_long": "歼灭单个2级兵\\s\\s\\s\\s8积分\\n歼灭单个3级兵\\s\\s\\s\\s32积分\\n歼灭单个4级兵\\s\\s\\s\\s80积分\\n歼灭单个5级兵\\s\\s\\s\\s140积分\\n歼灭单个6级兵\\s\\s\\s\\s200积分", "rank_reward_type": 29, "point_reward": "80000,8000057,1;150000,8000065,2;300000,82001028,10", "activity_type": 6, "id": 14, "desc_short": "击退或者击杀敌方士兵，士兵等级越高伤亡越多，获得的积分越多。", "name": "战意滔天"}, {"desc_long": "消耗10粮食\\s\\s\\s\\s1积分\\n消耗10木材\\s\\s\\s\\s1积分\\n消耗10铁矿\\s\\s\\s\\s10积分\\n消耗10黄金\\s\\s\\s\\s100积分\\n消耗10秒时间\\s\\s\\s\\s1积分", "rank_reward_type": 29, "point_reward": "80000,8000057,1;150000,8000065,2;300000,82001028,10", "activity_type": 9, "id": 15, "desc_short": "建筑升级消耗的资源越多，花费的时间越长，获得的积分越多。", "name": "建筑升级"}, {"desc_long": "[FFDD8CFF]积分获取：[-]\\n[FFEEC7FF]高级探索：10分[-]\\n[FFEEC7FF]低级探索：1分[-]", "rank_reward_type": -1, "point_reward": "0-60,8000060,1;60,82000005,5;60,8000058,2;60,8000002,3;60,12100014,1;60,12100008,1;60,12100002,1;60,12100032,1;0,7064000,1&1-60,82001028,5;60,12100067,5;60,8000058,2;60,8000002,3;60,12100014,1;60,12100008,1;60,12100002,1;60,12100032,1;0,7053004,1", "activity_type": 4, "id": 100, "desc_short": "[FFDD8CFF]活动介绍：[-]*n[FFEEC7FF]活动期间内，主公进行武将探索可以获得积分，积分可用来兑换道具同时点亮祭坛，当祭坛全部被点亮时即可免费领取终极大奖！活动结束后，积分将被清零。[-]", "name": "武坛"}, {"desc_long": "[FFDD8CFF]积分获取：[-]\\n[FFEEC7FF]高级探索：10分[-]\\n[FFEEC7FF]低级探索：1分[-]", "rank_reward_type": -1, "point_reward": "0-30,8000060,1;30,82000005,5;30,8000058,2;30,8000002,3;30,12100014,1;30,12100008,1;30,12100002,1;30,12100032,1;0,82010109,25&1-30,82001028,5;30,12100067,5;30,8000058,2;30,8000002,3;30,12100014,1;30,12100008,1;30,12100002,1;30,12100032,1;0,82010109,25", "activity_type": 4, "id": 101, "desc_short": "[FFDD8CFF]活动介绍：[-]*n[FFEEC7FF]活动期间内，主公进行武将探索可以获得积分，积分可用来兑换道具同时点亮祭坛，当祭坛全部被点亮时即可免费领取终极大奖！活动结束后，积分将被清零。[-]", "name": "祭坛拜将"}, {"desc_long": "[FFDD8CFF]积分获取：[-]\\n[FFEEC7FF]高级探索：10分[-]\\n[FFEEC7FF]低级探索：1分[-]", "rank_reward_type": -1, "point_reward": "0-30,8000060,1;30,82000005,5;30,8000058,2;30,8000002,3;30,12100014,1;30,12100008,1;30,12100002,1;30,12100032,1;0,82010109,25&1-30,82001028,5;30,12100067,5;30,8000058,2;30,8000002,3;30,12100014,1;30,12100008,1;30,12100002,1;30,12100032,1;0,82010109,25", "activity_type": 4, "id": 102, "desc_short": "[FFDD8CFF]活动介绍：[-]*n[FFEEC7FF]活动期间内，主公进行武将探索可以获得积分，积分可用来兑换道具同时点亮祭坛，当祭坛全部被点亮时即可免费领取终极大奖！活动结束后，积分将被清零。[-]", "name": "祭坛拜将"}, {"desc_long": "[FFDD8CFF]积分获取：[-]\\n[FFEEC7FF]高级探索：10分[-]\\n[FFEEC7FF]低级探索：1分[-]", "rank_reward_type": -1, "point_reward": "0-30,8000060,1;30,82000005,5;30,8000058,2;30,8000002,3;30,12100014,1;30,12100008,1;30,12100002,1;30,12100032,1;0,7053006,1&1-30,82001028,5;30,12100067,5;30,8000058,2;30,8000002,3;30,12100014,1;30,12100008,1;30,12100002,1;30,12100032,1;0,7062000,1", "activity_type": 4, "id": 103, "desc_short": "[FFDD8CFF]活动介绍：[-]*n[FFEEC7FF]活动期间内，主公进行武将探索可以获得积分，积分可用来兑换道具同时点亮祭坛，当祭坛全部被点亮时即可免费领取终极大奖！活动结束后，积分将被清零。[-]", "name": "祭坛拜将"}, {"desc_long": "[FFDD8CFF]积分获取：[-]\\n[FFEEC7FF]高级探索：10分[-]\\n[FFEEC7FF]低级探索：1分[-]", "rank_reward_type": -1, "point_reward": "0-250,82001028,30;250,8000010,15;250,8000018,15;200,12102003,50;100,8000002,6;150,12100000,200;150,12100012,200;150,12100024,200;0,7052004,1", "activity_type": 4, "id": 104, "desc_short": "[FFDD8CFF]活动介绍：[-]*n[FFEEC7FF]活动期间内，主公进行武将探索可以获得积分，积分可用来兑换道具同时点亮祭坛，当祭坛全部被点亮时即可免费领取终极大奖！活动结束后，积分将被清零。[-]", "name": "祭坛拜将"}, {"desc_long": "[FFDD8CFF]积分获取：[-]\\n[FFEEC7FF]高级探索：10分[-]\\n[FFEEC7FF]低级探索：1分[-]", "rank_reward_type": -1, "point_reward": "1-250,82001028,30;250,8000010,15;250,8000018,15;200,12102003,50;100,8000002,6;150,12100000,200;150,12100012,200;150,12100024,200;0,7054003,1", "activity_type": 4, "id": 105, "desc_short": "[FFDD8CFF]活动介绍：[-]*n[FFEEC7FF]活动期间内，主公进行武将探索可以获得积分，积分可用来兑换道具同时点亮祭坛，当祭坛全部被点亮时即可免费领取终极大奖！活动结束后，积分将被清零。[-]", "name": "祭坛拜将"}, {"desc_long": "拜访宝箱\\s\\s\\s\\s10000积分", "rank_reward_type": 24, "point_reward": "50000,8000058,1;100000,8000065,1;200000,82001028,5", "activity_type": 8, "id": 20, "desc_short": "拜访宝箱的次数越多，获得的积分越多。", "name": "拜访宝箱"}, {"desc_long": "探索陵寝\\s\\s\\s\\s20000积分", "rank_reward_type": 25, "point_reward": "50000,8000058,1;100000,8000065,1;200000,82001028,5", "activity_type": 7, "id": 21, "desc_short": "探索陵寝，探索的陵寝数量越多，获得的积分越多。", "name": "探索陵寝"}, {"desc_long": "消灭单个1级野外系统军队\\s\\s\\s\\s500积分\\n消灭单个2级野外系统军队\\s\\s\\s\\s1000积分\\n消灭单个3级野外系统军队\\s\\s\\s\\s1500积分\\n消灭单个4级野外系统军队\\s\\s\\s\\s2000积分\\n消灭单个5级野外系统军队\\s\\s\\s\\s2500积分\\n消灭单个6级野外系统军队\\s\\s\\s\\s3000积分\\n消灭单个7级野外系统军队\\s\\s\\s\\s3500积分\\n消灭单个8级野外系统军队\\s\\s\\s\\s4000积分\\n消灭单个9级野外系统军队\\s\\s\\s\\s4500积分\\n消灭单个10级野外系统军队\\s\\s\\s\\s5000积分\\n消灭单个11级野外系统军队\\s\\s\\s\\s5600积分\\n消灭单个12级野外系统军队\\s\\s\\s\\s6200积分\\n消灭单个13级野外系统军队\\s\\s\\s\\s6800积分\\n消灭单个14级野外系统军队\\s\\s\\s\\s7400积分\\n消灭单个15级野外系统军队\\s\\s\\s\\s8000积分\\n消灭单个16级野外系统军队\\s\\s\\s\\s8600积分\\n消灭单个17级野外系统军队\\s\\s\\s\\s9200积分\\n消灭单个18级野外系统军队\\s\\s\\s\\s9800积分\\n消灭单个19级野外系统军队\\s\\s\\s\\s10400积分\\n消灭单个20级野外系统军队\\s\\s\\s\\s11000积分\\n消灭单个21级野外系统军队\\s\\s\\s\\s11800积分\\n消灭单个22级野外系统军队\\s\\s\\s\\s12600积分\\n消灭单个23级野外系统军队\\s\\s\\s\\s13400积分\\n消灭单个24级野外系统军队\\s\\s\\s\\s14200积分\\n消灭单个25级野外系统军队\\s\\s\\s\\s15000积分\\n消灭单个26级野外系统军队\\s\\s\\s\\s15800积分\\n消灭单个27级野外系统军队\\s\\s\\s\\s16600积分\\n消灭单个28级野外系统军队\\s\\s\\s\\s17400积分\\n消灭单个29级野外系统军队\\s\\s\\s\\s18200积分\\n消灭单个30级野外系统军队\\s\\s\\s\\s19000积分", "rank_reward_type": 26, "point_reward": "80000,8000058,1;150000,8000065,1;300000,82001028,5", "activity_type": 5, "id": 22, "desc_short": "消灭的野外系统军队等级越高，获得的积分越多。", "name": "消灭野外系统军队"}, {"desc_long": "采集10铜钱\\s\\s\\s\\s1积分\\n采集10粮食\\s\\s\\s\\s1积分\\n采集10木材\\s\\s\\s\\s1积分\\n采集10铁矿\\s\\s\\s\\s10积分\\n采集10黄金\\s\\s\\s\\s100积分\\n采集10元宝\\s\\s\\s\\s1000积分", "rank_reward_type": 27, "point_reward": "60000,8000058,1;120000,8000065,1;250000,82001028,5", "activity_type": 1, "id": 23, "desc_short": "采集的资源越多、越贵重，获得的积分越多。", "name": "采集资源"}, {"desc_long": "训练单个1级兵\\s\\s\\s\\s10积分\\n训练单个2级兵\\s\\s\\s\\s30积分\\n训练单个3级兵\\s\\s\\s\\s100积分\\n训练单个4级兵\\s\\s\\s\\s180积分\\n训练单个5级兵\\s\\s\\s\\s270积分\\n训练单个6级兵\\s\\s\\s\\s360积分", "rank_reward_type": 28, "point_reward": "80000,8000058,1;150000,8000065,1;300000,82001028,5", "activity_type": 2, "id": 24, "desc_short": "训练的士兵等级越高，获得的积分越多。", "name": "训练士兵"}, {"desc_long": "歼灭单个2级兵\\s\\s\\s\\s8积分\\n歼灭单个3级兵\\s\\s\\s\\s32积分\\n歼灭单个4级兵\\s\\s\\s\\s80积分\\n歼灭单个5级兵\\s\\s\\s\\s140积分\\n歼灭单个6级兵\\s\\s\\s\\s200积分", "rank_reward_type": -1, "point_reward": "80000,8000058,1;150000,8000065,2;300000,82001028,10", "activity_type": 6, "id": 25, "desc_short": "击退或者击杀敌方士兵，士兵等级越高伤亡越多，获得的积分越多。", "name": "战意滔天"}, {"desc_long": "[FFDD8CFF]积分获取：[-]\\n[FFEEC7FF]高级探索：10分[-]\\n[FFEEC7FF]低级探索：1分[-]", "rank_reward_type": -1, "point_reward": "0-250,82001028,30;250,8000010,15;250,8000018,15;200,12102003,50;100,8000002,6;150,12100000,200;150,12100012,200;150,12100024,200;0,7052002,1", "activity_type": 4, "id": 200, "desc_short": "[FFDD8CFF]活动介绍：[-]*n[FFEEC7FF]活动期间内，主公进行武将探索可以获得积分，积分可用来兑换道具同时点亮祭坛，当祭坛全部被点亮时即可免费领取终极大奖！活动结束后，积分将被清零。[-]", "name": "祭坛拜将"}, {"desc_long": "[FFDD8CFF]积分获取：[-]\\n[FFEEC7FF]高级探索：10分[-]\\n[FFEEC7FF]低级探索：1分[-]", "rank_reward_type": -1, "point_reward": "1-250,82001028,30;250,8000010,15;250,8000018,15;200,12102003,50;100,8000002,6;150,12100000,200;150,12100012,200;150,12100024,200;0,7054001,1", "activity_type": 4, "id": 201, "desc_short": "[FFDD8CFF]活动介绍：[-]*n[FFEEC7FF]活动期间内，主公进行武将探索可以获得积分，积分可用来兑换道具同时点亮祭坛，当祭坛全部被点亮时即可免费领取终极大奖！活动结束后，积分将被清零。[-]", "name": "祭坛拜将"}, {"desc_long": "[FFDD8CFF]积分获取：[-]\\n[FFEEC7FF]高级探索：10分[-]\\n[FFEEC7FF]低级探索：1分[-]", "rank_reward_type": -1, "point_reward": "1-250,82001028,30;250,8000010,15;250,8000018,15;200,12102003,50;100,8000002,6;150,12100000,200;150,12100012,200;150,12100024,200;0,7053000,1", "activity_type": 4, "id": 202, "desc_short": "[FFDD8CFF]活动介绍：[-]*n[FFEEC7FF]活动期间内，主公进行武将探索可以获得积分，积分可用来兑换道具同时点亮祭坛，当祭坛全部被点亮时即可免费领取终极大奖！活动结束后，积分将被清零。[-]", "name": "祭坛拜将"}, {"desc_long": "[FFDD8CFF]积分获取：[-]\\n[FFEEC7FF]高级探索：10分[-]\\n[FFEEC7FF]低级探索：1分[-]", "rank_reward_type": -1, "point_reward": "1-250,82001028,30;250,8000010,15;250,8000018,15;200,12102003,50;100,8000002,6;150,12100000,200;150,12100012,200;150,12100024,200;0,7054003,1", "activity_type": 4, "id": 203, "desc_short": "[FFDD8CFF]活动介绍：[-]*n[FFEEC7FF]活动期间内，主公进行武将探索可以获得积分，积分可用来兑换道具同时点亮祭坛，当祭坛全部被点亮时即可免费领取终极大奖！活动结束后，积分将被清零。[-]", "name": "祭坛拜将"}, {"desc_long": "[FFDD8CFF]积分获取：[-]\\n[FFEEC7FF]高级探索：10分[-]\\n[FFEEC7FF]低级探索：1分[-]", "rank_reward_type": -1, "point_reward": "0-500,82001028,50;500,8000010,30;500,8000018,30;400,12102003,100;200,8000002,12;300,12100000,400;300,12100012,400;300,12100024,400;0,7053005,1", "activity_type": 4, "id": 204, "desc_short": "[FFDD8CFF]活动介绍：[-]*n[FFEEC7FF]活动期间内，主公进行武将探索可以获得积分，积分可用来兑换道具同时点亮祭坛，当祭坛全部被点亮时即可免费领取终极大奖！活动结束后，积分将被清零。[-]", "name": "祭坛拜将"}, {"desc_long": "[FFDD8CFF]积分获取：[-]\\n[FFEEC7FF]高级探索：10分[-]\\n[FFEEC7FF]低级探索：1分[-]", "rank_reward_type": -1, "point_reward": "0-500,82001028,50;500,8000010,30;500,8000018,30;400,12102003,100;200,8000002,12;300,12100000,400;300,12100012,400;300,12100024,400;0,7053005,1&1-250,82001028,30;250,8000010,15;250,8000018,15;200,12102003,50;100,8000002,6;150,12100000,200;150,12100012,200;150,12100024,200;0,7054003,1", "activity_type": 4, "id": 20401, "desc_short": "[FFDD8CFF]活动介绍：[-]*n[FFEEC7FF]活动期间内，主公进行武将探索可以获得积分，积分可用来兑换道具同时点亮祭坛，当祭坛全部被点亮时即可免费领取终极大奖！活动结束后，积分将被清零。[-]", "name": "祭坛拜将"}, {"desc_long": "[FFDD8CFF]积分获取：[-]\\n[FFEEC7FF]高级探索：10分[-]\\n[FFEEC7FF]低级探索：1分[-]", "rank_reward_type": -1, "point_reward": "1-500,82001028,50;500,8000010,30;500,8000018,30;400,12102003,100;200,8000002,12;300,12100000,400;300,12100012,400;300,12100024,400;0,7051001,1", "activity_type": 4, "id": 205, "desc_short": "[FFDD8CFF]活动介绍：[-]*n[FFEEC7FF]活动期间内，主公进行武将探索可以获得积分，积分可用来兑换道具同时点亮祭坛，当祭坛全部被点亮时即可免费领取终极大奖！活动结束后，积分将被清零。[-]", "name": "祭坛拜将"}, {"desc_long": "研究院研究科技提升1战力\\s\\s\\s\\s10积分\\n战争堡研究科技提升1战力\\s\\s\\s\\s20积分\\n发展堡研究科技提升1战力\\s\\s\\s\\s15积分", "rank_reward_type": -1, "point_reward": "80000,8000058,1;80000,8000058,1;150000,8000065,2;150000,8000058,1;300000,82001028,10;600000,82001028,20", "activity_type": 10, "id": 26, "desc_short": "研究院、战争堡、发展堡进行科技研究，获得积分", "name": "研究科技"}, {"desc_long": "治疗单个1级兵\\s\\s\\s\\s5积分\\n治疗单个2级兵\\s\\s\\s\\s15积分\\n治疗单个3级兵\\s\\s\\s\\s50积分\\n治疗单个4级兵\\s\\s\\s\\s90积分\\n治疗单个5级兵\\s\\s\\s\\s135积分\\n治疗单个6级兵\\s\\s\\s\\s180积分", "rank_reward_type": -1, "point_reward": "80000,8000058,1;80000,8000058,1;150000,8000065,2;150000,8000058,1;300000,82001028,10;600000,82001028,30", "activity_type": 11, "id": 27, "desc_short": "治疗伤兵可获得积分，士兵等级越高、数量越多，获得的积分越多", "name": "治疗伤兵"}, {"desc_long": "受伤或者死亡单个1级兵\\s\\s\\s\\s5积分\\n受伤或者死亡单个2级兵\\s\\s\\s\\s15积分\\n受伤或者死亡单个3级兵\\s\\s\\s\\s50积分\\n受伤或者死亡单个4级兵\\s\\s\\s\\s90积分\\n受伤或者死亡单个5级兵\\s\\s\\s\\s135积分\\n受伤或者死亡单个6级兵\\s\\s\\s\\s180积分", "rank_reward_type": -1, "point_reward": "80000,8000058,1;80000,8000058,1;150000,8000065,2;150000,8000058,1;300000,82001028,10;600000,82001028,40", "activity_type": 12, "id": 28, "desc_short": "士兵受伤或者死亡可获得积分，士兵等级越高、数量越多，获得的积分越多", "name": "士兵死亡"}, {"desc_long": "[FFDD8CFF]积分获取：[-]\\n[FFEEC7FF]高级探索：10分[-]\\n[FFEEC7FF]低级探索：1分[-]", "rank_reward_type": -1, "point_reward": "1-250,82001028,30;250,8000010,15;250,8000018,15;200,12102003,50;100,8000002,6;150,12100000,200;150,12100012,200;150,12100024,200;0,7053003,1", "activity_type": 4, "id": 300, "desc_short": "[FFDD8CFF]活动介绍：[-]*n[FFEEC7FF]活动期间内，主公进行武将探索可以获得积分，积分可用来兑换道具同时点亮祭坛，当祭坛全部被点亮时即可免费领取终极大奖！活动结束后，积分将被清零。[-]", "name": "武坛"}, {"desc_long": "[FFDD8CFF]积分获取：[-]\\n[FFEEC7FF]高级探索：10分[-]\\n[FFEEC7FF]低级探索：1分[-]", "rank_reward_type": -1, "point_reward": "1-250,82001028,30;250,8000010,15;250,8000018,15;200,12102003,50;100,8000002,6;150,12100000,200;150,12100012,200;150,12100024,200;0,7052005,1", "activity_type": 4, "id": 301, "desc_short": "[FFDD8CFF]活动介绍：[-]*n[FFEEC7FF]活动期间内，主公进行武将探索可以获得积分，积分可用来兑换道具同时点亮祭坛，当祭坛全部被点亮时即可免费领取终极大奖！活动结束后，积分将被清零。[-]", "name": "祭坛拜将"}, {"desc_long": "[FFDD8CFF]积分获取：[-]\\n[FFEEC7FF]高级探索：10分[-]\\n[FFEEC7FF]低级探索：1分[-]", "rank_reward_type": -1, "point_reward": "1-250,82001028,30;250,8000010,15;250,8000018,15;200,12102003,50;100,8000002,6;150,12100000,200;150,12100012,200;150,12100024,200;0,7054003,1", "activity_type": 4, "id": 302, "desc_short": "[FFDD8CFF]活动介绍：[-]*n[FFEEC7FF]活动期间内，主公进行武将探索可以获得积分，积分可用来兑换道具同时点亮祭坛，当祭坛全部被点亮时即可免费领取终极大奖！活动结束后，积分将被清零。[-]", "name": "祭坛拜将"}, {"desc_long": "[FFDD8CFF]积分获取：[-]\\n[FFEEC7FF]高级探索：10分[-]\\n[FFEEC7FF]低级探索：1分[-]", "rank_reward_type": -1, "point_reward": "0-250,82001028,30;250,8000010,15;250,8000018,15;200,12102003,50;100,8000002,6;150,12100000,200;150,12100012,200;150,12100024,200;0,7052004,1", "activity_type": 4, "id": 303, "desc_short": "[FFDD8CFF]活动介绍：[-]*n[FFEEC7FF]活动期间内，主公进行武将探索可以获得积分，积分可用来兑换道具同时点亮祭坛，当祭坛全部被点亮时即可免费领取终极大奖！活动结束后，积分将被清零。[-]", "name": "祭坛拜将"}, {"desc_long": "[FFDD8CFF]积分获取：[-]\\n[FFEEC7FF]高级探索：10分[-]\\n[FFEEC7FF]低级探索：1分[-]", "rank_reward_type": -1, "point_reward": "1-250,82001028,30;250,8000010,15;250,8000018,15;200,12102003,50;100,8000002,6;150,12100000,200;150,12100012,200;150,12100024,200;0,7054001,1", "activity_type": 4, "id": 304, "desc_short": "[FFDD8CFF]活动介绍：[-]*n[FFEEC7FF]活动期间内，主公进行武将探索可以获得积分，积分可用来兑换道具同时点亮祭坛，当祭坛全部被点亮时即可免费领取终极大奖！活动结束后，积分将被清零。[-]", "name": "祭坛拜将"}, {"desc_long": "[FFDD8CFF]积分获取：[-]\\n[FFEEC7FF]高级探索：10分[-]\\n[FFEEC7FF]低级探索：1分[-]", "rank_reward_type": -1, "point_reward": "0-250,82001028,30;250,8000010,15;250,8000018,15;200,12102003,50;100,8000002,6;150,12100000,200;150,12100012,200;150,12100024,200;0,7052002,1", "activity_type": 4, "id": 400, "desc_short": "[FFDD8CFF]活动介绍：[-]*n[FFEEC7FF]活动期间内，主公进行武将探索可以获得积分，积分可用来兑换道具同时点亮祭坛，当祭坛全部被点亮时即可免费领取终极大奖！活动结束后，积分将被清零。[-]", "name": "祭坛拜将"}, {"desc_long": "[FFDD8CFF]积分获取：[-]\\n[FFEEC7FF]高级探索：10分[-]\\n[FFEEC7FF]低级探索：1分[-]", "rank_reward_type": -1, "point_reward": "0-250,82001028,30;250,8000010,15;250,8000018,15;200,12102003,50;100,8000002,6;150,12100000,200;150,12100012,200;150,12100024,200;0,7054004,1", "activity_type": 4, "id": 401, "desc_short": "[FFDD8CFF]活动介绍：[-]*n[FFEEC7FF]活动期间内，主公进行武将探索可以获得积分，积分可用来兑换道具同时点亮祭坛，当祭坛全部被点亮时即可免费领取终极大奖！活动结束后，积分将被清零。[-]", "name": "祭坛拜将"}, {"desc_long": "[FFDD8CFF]积分获取：[-]\\n[FFEEC7FF]高级探索：10分[-]\\n[FFEEC7FF]低级探索：1分[-]", "rank_reward_type": -1, "point_reward": "1-250,82001028,30;250,8000010,15;250,8000018,15;200,12102003,50;100,8000002,6;150,12100000,200;150,12100012,200;150,12100024,200;0,7053002,1", "activity_type": 4, "id": 402, "desc_short": "[FFDD8CFF]活动介绍：[-]*n[FFEEC7FF]活动期间内，主公进行武将探索可以获得积分，积分可用来兑换道具同时点亮祭坛，当祭坛全部被点亮时即可免费领取终极大奖！活动结束后，积分将被清零。[-]", "name": "祭坛拜将"}, {"desc_long": "[FFDD8CFF]积分获取：[-]\\n[FFEEC7FF]高级探索：10分[-]\\n[FFEEC7FF]低级探索：1分[-]", "rank_reward_type": -1, "point_reward": "1-250,82001028,30;250,8000010,15;250,8000018,15;200,12102003,50;100,8000002,6;150,12100000,200;150,12100012,200;150,12100024,200;0,7053000,1", "activity_type": 4, "id": 403, "desc_short": "[FFDD8CFF]活动介绍：[-]*n[FFEEC7FF]活动期间内，主公进行武将探索可以获得积分，积分可用来兑换道具同时点亮祭坛，当祭坛全部被点亮时即可免费领取终极大奖！活动结束后，积分将被清零。[-]", "name": "祭坛拜将"}, {"desc_long": "[FFDD8CFF]积分获取：[-]\\n[FFEEC7FF]高级探索：10分[-]\\n[FFEEC7FF]低级探索：1分[-]", "rank_reward_type": -1, "point_reward": "0-500,82001028,50;500,8000010,30;500,8000018,30;400,12102003,100;200,8000002,12;300,12100000,400;300,12100012,400;300,12100024,400;0,7053005,1", "activity_type": 4, "id": 404, "desc_short": "[FFDD8CFF]活动介绍：[-]*n[FFEEC7FF]活动期间内，主公进行武将探索可以获得积分，积分可用来兑换道具同时点亮祭坛，当祭坛全部被点亮时即可免费领取终极大奖！活动结束后，积分将被清零。[-]", "name": "祭坛拜将"}, {"desc_long": "[FFDD8CFF]积分获取：[-]\\n[FFEEC7FF]高级探索：10分[-]\\n[FFEEC7FF]低级探索：1分[-]", "rank_reward_type": -1, "point_reward": "1-500,82001028,50;500,8000010,30;500,8000018,30;400,12102003,100;200,8000002,12;300,12100000,400;300,12100012,400;300,12100024,400;0,7051001,1", "activity_type": 4, "id": 501, "desc_short": "[FFDD8CFF]活动介绍：[-]*n[FFEEC7FF]活动期间内，主公进行武将探索可以获得积分，积分可用来兑换道具同时点亮祭坛，当祭坛全部被点亮时即可免费领取终极大奖！活动结束后，积分将被清零。[-]", "name": "祭坛拜将"}, {"desc_long": "训练单个1级兵\\s\\s\\s\\s10积分\\n训练单个2级兵\\s\\s\\s\\s30积分\\n训练单个3级兵\\s\\s\\s\\s100积分\\n训练单个4级兵\\s\\s\\s\\s180积分\\n训练单个5级兵\\s\\s\\s\\s270积分\\n训练单个6级兵\\s\\s\\s\\s360积分", "rank_reward_type": -1, "point_reward": "15000,12100000,10;15000,12100006,10;15000,12100030,10;15000,12100024,10;75000,82000009,10;75000,82001028,3;75000,12100012,20;75000,1000042,200;150000,12100000,30;150000,12100006,30;150000,12100030,30;150000,12100024,30;300000,82000009,20;300000,82001028,5;300000,12100012,50;300000,1000042,500;750000,82001027,1;750000,12102003,20;750000,6102040,50;750000,8000010,5", "activity_type": 2, "id": 1000, "desc_short": "训练的士兵等级越高，获得的积分越多。", "name": "训练士兵"}, {"desc_long": "治疗单个1级兵\\s\\s\\s\\s5积分\\n治疗单个2级兵\\s\\s\\s\\s15积分\\n治疗单个3级兵\\s\\s\\s\\s50积分\\n治疗单个4级兵\\s\\s\\s\\s90积分\\n治疗单个5级兵\\s\\s\\s\\s135积分\\n治疗单个6级兵\\s\\s\\s\\s180积分", "rank_reward_type": -1, "point_reward": "50000,12100000,10;50000,12100006,10;50000,12100030,10;50000,12100024,10;200000,82000009,10;200000,82001028,3;200000,12100012,20;200000,1000042,200;500000,12100000,30;500000,12100006,30;500000,12100030,30;500000,12100024,30;1000000,82000009,20;1000000,82001028,5;1000000,12100012,50;1000000,1000042,500;2000000,82001027,1;2000000,12102003,20;2000000,6102040,50;2000000,8000026,5", "activity_type": 11, "id": 1001, "desc_short": "治疗伤兵可获得积分，士兵等级越高、数量越多，获得的积分越多", "name": "治疗伤兵"}, {"desc_long": "歼灭单个2级兵\\s\\s\\s\\s8积分\\n歼灭单个3级兵\\s\\s\\s\\s32积分\\n歼灭单个4级兵\\s\\s\\s\\s80积分\\n歼灭单个5级兵\\s\\s\\s\\s140积分\\n歼灭单个6级兵\\s\\s\\s\\s200积分", "rank_reward_type": -1, "point_reward": "50000,12100000,10;50000,12100006,10;50000,12100030,10;50000,12100024,10;250000,82000009,10;250000,82001028,3;250000,12100012,20;250000,1000042,200;500000,12100000,30;500000,12100006,30;500000,12100030,30;500000,12100024,30;1000000,82000009,20;1000000,82001028,5;1000000,12100012,50;1000000,1000042,500;2500000,12102021,1;2500000,12102003,50;2500000,6102040,100;2500000,8000002,10", "activity_type": 6, "id": 1002, "desc_short": "击退或者击杀敌方士兵，士兵等级越高伤亡越多，获得的积分越多。", "name": "战意滔天"}, {"desc_long": "训练单个1级兵\\s\\s\\s\\s10积分\\n训练单个2级兵\\s\\s\\s\\s30积分\\n训练单个3级兵\\s\\s\\s\\s100积分\\n训练单个4级兵\\s\\s\\s\\s180积分\\n训练单个5级兵\\s\\s\\s\\s270积分\\n训练单个6级兵\\s\\s\\s\\s360积分", "rank_reward_type": -1, "point_reward": "22000,12100000,10;22000,12100006,10;22000,12100030,10;22000,12100024,10;110000,82000009,10;110000,82001028,3;110000,12100012,20;110000,1000042,200;220000,12100000,30;220000,12100006,30;220000,12100030,30;220000,12100024,30;440000,82000009,20;440000,82001028,5;440000,12100012,50;440000,1000042,500;1100000,82001027,1;1100000,12102003,20;1100000,6102040,50;1100000,8000010,5", "activity_type": 2, "id": 1010, "desc_short": "训练的士兵等级越高，获得的积分越多。", "name": "训练士兵"}, {"desc_long": "治疗单个1级兵\\s\\s\\s\\s5积分\\n治疗单个2级兵\\s\\s\\s\\s15积分\\n治疗单个3级兵\\s\\s\\s\\s50积分\\n治疗单个4级兵\\s\\s\\s\\s90积分\\n治疗单个5级兵\\s\\s\\s\\s135积分\\n治疗单个6级兵\\s\\s\\s\\s180积分", "rank_reward_type": -1, "point_reward": "70000,12100000,10;70000,12100006,10;70000,12100030,10;70000,12100024,10;280000,82000009,10;280000,82001028,3;280000,12100012,20;280000,1000042,200;700000,12100000,30;700000,12100006,30;700000,12100030,30;700000,12100024,30;1400000,82000009,20;1400000,82001028,5;1400000,12100012,50;1400000,1000042,500;2800000,82001027,1;2800000,12102003,20;2800000,6102040,50;2800000,8000026,5", "activity_type": 11, "id": 1011, "desc_short": "治疗伤兵可获得积分，士兵等级越高、数量越多，获得的积分越多", "name": "治疗伤兵"}, {"desc_long": "歼灭单个2级兵\\s\\s\\s\\s8积分\\n歼灭单个3级兵\\s\\s\\s\\s32积分\\n歼灭单个4级兵\\s\\s\\s\\s80积分\\n歼灭单个5级兵\\s\\s\\s\\s140积分\\n歼灭单个6级兵\\s\\s\\s\\s200积分", "rank_reward_type": -1, "point_reward": "80000,12100000,10;80000,12100006,10;80000,12100030,10;80000,12100024,10;400000,82000009,10;400000,82001028,3;400000,12100012,20;400000,1000042,200;800000,12100000,30;800000,12100006,30;800000,12100030,30;800000,12100024,30;1600000,82000009,20;1600000,82001028,5;1600000,12100012,50;1600000,1000042,500;4000000,12102021,1;4000000,12102003,50;4000000,6102040,100;4000000,8000002,10", "activity_type": 6, "id": 1012, "desc_short": "击退或者击杀敌方士兵，士兵等级越高伤亡越多，获得的积分越多。", "name": "战意滔天"}, {"desc_long": "训练单个1级兵\\s\\s\\s\\s10积分\\n训练单个2级兵\\s\\s\\s\\s30积分\\n训练单个3级兵\\s\\s\\s\\s100积分\\n训练单个4级兵\\s\\s\\s\\s180积分\\n训练单个5级兵\\s\\s\\s\\s270积分\\n训练单个6级兵\\s\\s\\s\\s360积分", "rank_reward_type": -1, "point_reward": "32000,12100000,10;32000,12100006,10;32000,12100030,10;32000,12100024,10;160000,82000009,10;160000,82001028,3;160000,12100012,20;160000,1000042,200;320000,12100000,30;320000,12100006,30;320000,12100030,30;320000,12100024,30;640000,82000009,20;640000,82001028,5;640000,12100012,50;640000,1000042,500;1600000,82001027,1;1600000,12102003,20;1600000,6102040,50;1600000,8000010,5", "activity_type": 2, "id": 1020, "desc_short": "训练的士兵等级越高，获得的积分越多。", "name": "训练士兵"}, {"desc_long": "治疗单个1级兵\\s\\s\\s\\s5积分\\n治疗单个2级兵\\s\\s\\s\\s15积分\\n治疗单个3级兵\\s\\s\\s\\s50积分\\n治疗单个4级兵\\s\\s\\s\\s90积分\\n治疗单个5级兵\\s\\s\\s\\s135积分\\n治疗单个6级兵\\s\\s\\s\\s180积分", "rank_reward_type": -1, "point_reward": "100000,12100000,10;100000,12100006,10;100000,12100030,10;100000,12100024,10;400000,82000009,10;400000,82001028,3;400000,12100012,20;400000,1000042,200;1000000,12100000,30;1000000,12100006,30;1000000,12100030,30;1000000,12100024,30;2000000,82000009,20;2000000,82001028,5;2000000,12100012,50;2000000,1000042,500;4000000,82001027,1;4000000,12102003,20;4000000,6102040,50;4000000,8000026,5", "activity_type": 11, "id": 1021, "desc_short": "治疗伤兵可获得积分，士兵等级越高、数量越多，获得的积分越多", "name": "治疗伤兵"}, {"desc_long": "歼灭单个2级兵\\s\\s\\s\\s8积分\\n歼灭单个3级兵\\s\\s\\s\\s32积分\\n歼灭单个4级兵\\s\\s\\s\\s80积分\\n歼灭单个5级兵\\s\\s\\s\\s140积分\\n歼灭单个6级兵\\s\\s\\s\\s200积分", "rank_reward_type": -1, "point_reward": "120000,12100000,10;120000,12100006,10;120000,12100030,10;120000,12100024,10;600000,82000009,10;600000,82001028,3;600000,12100012,20;600000,1000042,200;1200000,12100000,30;1200000,12100006,30;1200000,12100030,30;1200000,12100024,30;2400000,82000009,20;2400000,82001028,5;2400000,12100012,50;2400000,1000042,500;6000000,12102021,1;6000000,12102003,50;6000000,6102040,100;6000000,8000002,10", "activity_type": 6, "id": 1022, "desc_short": "击退或者击杀敌方士兵，士兵等级越高伤亡越多，获得的积分越多。", "name": "战意滔天"}, {"desc_long": "训练单个1级兵\\s\\s\\s\\s10积分\\n训练单个2级兵\\s\\s\\s\\s30积分\\n训练单个3级兵\\s\\s\\s\\s100积分\\n训练单个4级兵\\s\\s\\s\\s180积分\\n训练单个5级兵\\s\\s\\s\\s270积分\\n训练单个6级兵\\s\\s\\s\\s360积分", "rank_reward_type": -1, "point_reward": "46000,12100000,10;46000,12100006,10;46000,12100030,10;46000,12100024,10;230000,82000009,10;230000,82001028,3;230000,12100012,20;230000,1000042,200;460000,12100000,30;460000,12100006,30;460000,12100030,30;460000,12100024,30;920000,82000009,20;920000,82001028,5;920000,12100012,50;920000,1000042,500;2300000,82001027,1;2300000,12102003,20;2300000,6102040,50;2300000,8000010,5", "activity_type": 2, "id": 1030, "desc_short": "训练的士兵等级越高，获得的积分越多。", "name": "训练士兵"}, {"desc_long": "治疗单个1级兵\\s\\s\\s\\s5积分\\n治疗单个2级兵\\s\\s\\s\\s15积分\\n治疗单个3级兵\\s\\s\\s\\s50积分\\n治疗单个4级兵\\s\\s\\s\\s90积分\\n治疗单个5级兵\\s\\s\\s\\s135积分\\n治疗单个6级兵\\s\\s\\s\\s180积分", "rank_reward_type": -1, "point_reward": "140000,12100000,10;140000,12100006,10;140000,12100030,10;140000,12100024,10;560000,82000009,10;560000,82001028,3;560000,12100012,20;560000,1000042,200;1400000,12100000,30;1400000,12100006,30;1400000,12100030,30;1400000,12100024,30;2800000,82000009,20;2800000,82001028,5;2800000,12100012,50;2800000,1000042,500;5600000,82001027,1;5600000,12102003,20;5600000,6102040,50;5600000,8000026,5", "activity_type": 11, "id": 1031, "desc_short": "治疗伤兵可获得积分，士兵等级越高、数量越多，获得的积分越多", "name": "治疗伤兵"}, {"desc_long": "歼灭单个2级兵\\s\\s\\s\\s8积分\\n歼灭单个3级兵\\s\\s\\s\\s32积分\\n歼灭单个4级兵\\s\\s\\s\\s80积分\\n歼灭单个5级兵\\s\\s\\s\\s140积分\\n歼灭单个6级兵\\s\\s\\s\\s200积分", "rank_reward_type": -1, "point_reward": "180000,12100000,10;180000,12100006,10;180000,12100030,10;180000,12100024,10;900000,82000009,10;900000,82001028,3;900000,12100012,20;900000,1000042,200;1800000,12100000,30;1800000,12100006,30;1800000,12100030,30;1800000,12100024,30;3600000,82000009,20;3600000,82001028,5;3600000,12100012,50;3600000,1000042,500;9000000,12102021,1;9000000,12102003,50;9000000,6102040,100;9000000,8000002,10", "activity_type": 6, "id": 1032, "desc_short": "击退或者击杀敌方士兵，士兵等级越高伤亡越多，获得的积分越多。", "name": "战意滔天"}, {"desc_long": "训练单个1级兵\\s\\s\\s\\s10积分\\n训练单个2级兵\\s\\s\\s\\s30积分\\n训练单个3级兵\\s\\s\\s\\s100积分\\n训练单个4级兵\\s\\s\\s\\s180积分\\n训练单个5级兵\\s\\s\\s\\s270积分\\n训练单个6级兵\\s\\s\\s\\s360积分", "rank_reward_type": -1, "point_reward": "67000,12100000,10;67000,12100006,10;67000,12100030,10;67000,12100024,10;340000,82000009,10;340000,82001028,3;340000,12100012,20;340000,1000042,200;670000,12100000,30;670000,12100006,30;670000,12100030,30;670000,12100024,30;1300000,82000009,20;1300000,82001028,5;1300000,12100012,50;1300000,1000042,500;3400000,82001027,1;3400000,12102003,20;3400000,6102040,50;3400000,8000010,5", "activity_type": 2, "id": 1040, "desc_short": "训练的士兵等级越高，获得的积分越多。", "name": "训练士兵"}, {"desc_long": "治疗单个1级兵\\s\\s\\s\\s5积分\\n治疗单个2级兵\\s\\s\\s\\s15积分\\n治疗单个3级兵\\s\\s\\s\\s50积分\\n治疗单个4级兵\\s\\s\\s\\s90积分\\n治疗单个5级兵\\s\\s\\s\\s135积分\\n治疗单个6级兵\\s\\s\\s\\s180积分", "rank_reward_type": -1, "point_reward": "200000,12100000,10;200000,12100006,10;200000,12100030,10;200000,12100024,10;780000,82000009,10;780000,82001028,3;780000,12100012,20;780000,1000042,200;2000000,12100000,30;2000000,12100006,30;2000000,12100030,30;2000000,12100024,30;3900000,82000009,20;3900000,82001028,5;3900000,12100012,50;3900000,1000042,500;7800000,82001027,1;7800000,12102003,20;7800000,6102040,50;7800000,8000026,5", "activity_type": 11, "id": 1041, "desc_short": "治疗伤兵可获得积分，士兵等级越高、数量越多，获得的积分越多", "name": "治疗伤兵"}, {"desc_long": "歼灭单个2级兵\\s\\s\\s\\s8积分\\n歼灭单个3级兵\\s\\s\\s\\s32积分\\n歼灭单个4级兵\\s\\s\\s\\s80积分\\n歼灭单个5级兵\\s\\s\\s\\s140积分\\n歼灭单个6级兵\\s\\s\\s\\s200积分", "rank_reward_type": -1, "point_reward": "270000,12100000,10;270000,12100006,10;270000,12100030,10;270000,12100024,10;1400000,82000009,10;1400000,82001028,3;1400000,12100012,20;1400000,1000042,200;2700000,12100000,30;2700000,12100006,30;2700000,12100030,30;2700000,12100024,30;5400000,82000009,20;5400000,82001028,5;5400000,12100012,50;5400000,1000042,500;14000000,12102021,1;14000000,12102003,50;14000000,6102040,100;14000000,8000002,10", "activity_type": 6, "id": 1042, "desc_short": "击退或者击杀敌方士兵，士兵等级越高伤亡越多，获得的积分越多。", "name": "战意滔天"}, {"desc_long": "训练单个1级兵\\s\\s\\s\\s10积分\\n训练单个2级兵\\s\\s\\s\\s30积分\\n训练单个3级兵\\s\\s\\s\\s100积分\\n训练单个4级兵\\s\\s\\s\\s180积分\\n训练单个5级兵\\s\\s\\s\\s270积分\\n训练单个6级兵\\s\\s\\s\\s360积分", "rank_reward_type": -1, "point_reward": "97000,12100000,10;97000,12100006,10;97000,12100030,10;97000,12100024,10;490000,82000009,10;490000,82001028,3;490000,12100012,20;490000,1000042,200;970000,12100000,30;970000,12100006,30;970000,12100030,30;970000,12100024,30;1900000,82000009,20;1900000,82001028,5;1900000,12100012,50;1900000,1000042,500;4900000,82001027,1;4900000,12102003,20;4900000,6102040,50;4900000,8000010,5", "activity_type": 2, "id": 1050, "desc_short": "训练的士兵等级越高，获得的积分越多。", "name": "训练士兵"}, {"desc_long": "治疗单个1级兵\\s\\s\\s\\s5积分\\n治疗单个2级兵\\s\\s\\s\\s15积分\\n治疗单个3级兵\\s\\s\\s\\s50积分\\n治疗单个4级兵\\s\\s\\s\\s90积分\\n治疗单个5级兵\\s\\s\\s\\s135积分\\n治疗单个6级兵\\s\\s\\s\\s180积分", "rank_reward_type": -1, "point_reward": "280000,12100000,10;280000,12100006,10;280000,12100030,10;280000,12100024,10;1100000,82000009,10;1100000,82001028,3;1100000,12100012,20;1100000,1000042,200;2800000,12100000,30;2800000,12100006,30;2800000,12100030,30;2800000,12100024,30;5500000,82000009,20;5500000,82001028,5;5500000,12100012,50;5500000,1000042,500;11000000,82001027,1;11000000,12102003,20;11000000,6102040,50;11000000,8000026,5", "activity_type": 11, "id": 1051, "desc_short": "治疗伤兵可获得积分，士兵等级越高、数量越多，获得的积分越多", "name": "治疗伤兵"}, {"desc_long": "歼灭单个2级兵\\s\\s\\s\\s8积分\\n歼灭单个3级兵\\s\\s\\s\\s32积分\\n歼灭单个4级兵\\s\\s\\s\\s80积分\\n歼灭单个5级兵\\s\\s\\s\\s140积分\\n歼灭单个6级兵\\s\\s\\s\\s200积分", "rank_reward_type": -1, "point_reward": "410000,12100000,10;410000,12100006,10;410000,12100030,10;410000,12100024,10;2100000,82000009,10;2100000,82001028,3;2100000,12100012,20;2100000,1000042,200;4100000,12100000,30;4100000,12100006,30;4100000,12100030,30;4100000,12100024,30;8200000,82000009,20;8200000,82001028,5;8200000,12100012,50;8200000,1000042,500;21000000,12102021,1;21000000,12102003,50;21000000,6102040,100;21000000,8000002,10", "activity_type": 6, "id": 1052, "desc_short": "击退或者击杀敌方士兵，士兵等级越高伤亡越多，获得的积分越多。", "name": "战意滔天"}, {"desc_long": "训练单个1级兵\\s\\s\\s\\s10积分\\n训练单个2级兵\\s\\s\\s\\s30积分\\n训练单个3级兵\\s\\s\\s\\s100积分\\n训练单个4级兵\\s\\s\\s\\s180积分\\n训练单个5级兵\\s\\s\\s\\s270积分\\n训练单个6级兵\\s\\s\\s\\s360积分", "rank_reward_type": -1, "point_reward": "140000,12100000,10;140000,12100006,10;140000,12100030,10;140000,12100024,10;710000,82000009,10;710000,82001028,3;710000,12100012,20;710000,1000042,200;1400000,12100000,30;1400000,12100006,30;1400000,12100030,30;1400000,12100024,30;2800000,82000009,20;2800000,82001028,5;2800000,12100012,50;2800000,1000042,500;7100000,82001027,1;7100000,12102003,20;7100000,6102040,50;7100000,8000010,5", "activity_type": 2, "id": 1060, "desc_short": "训练的士兵等级越高，获得的积分越多。", "name": "训练士兵"}, {"desc_long": "治疗单个1级兵\\s\\s\\s\\s5积分\\n治疗单个2级兵\\s\\s\\s\\s15积分\\n治疗单个3级兵\\s\\s\\s\\s50积分\\n治疗单个4级兵\\s\\s\\s\\s90积分\\n治疗单个5级兵\\s\\s\\s\\s135积分\\n治疗单个6级兵\\s\\s\\s\\s180积分", "rank_reward_type": -1, "point_reward": "390000,12100000,10;390000,12100006,10;390000,12100030,10;390000,12100024,10;1500000,82000009,10;1500000,82001028,3;1500000,12100012,20;1500000,1000042,200;3900000,12100000,30;3900000,12100006,30;3900000,12100030,30;3900000,12100024,30;7700000,82000009,20;7700000,82001028,5;7700000,12100012,50;7700000,1000042,500;15000000,82001027,1;15000000,12102003,20;15000000,6102040,50;15000000,8000026,5", "activity_type": 11, "id": 1061, "desc_short": "治疗伤兵可获得积分，士兵等级越高、数量越多，获得的积分越多", "name": "治疗伤兵"}, {"desc_long": "歼灭单个2级兵\\s\\s\\s\\s8积分\\n歼灭单个3级兵\\s\\s\\s\\s32积分\\n歼灭单个4级兵\\s\\s\\s\\s80积分\\n歼灭单个5级兵\\s\\s\\s\\s140积分\\n歼灭单个6级兵\\s\\s\\s\\s200积分", "rank_reward_type": -1, "point_reward": "620000,12100000,10;620000,12100006,10;620000,12100030,10;620000,12100024,10;3100000,82000009,10;3100000,82001028,3;3100000,12100012,20;3100000,1000042,200;6200000,12100000,30;6200000,12100006,30;6200000,12100030,30;6200000,12100024,30;12000000,82000009,20;12000000,82001028,5;12000000,12100012,50;12000000,1000042,500;31000000,12102021,1;31000000,12102003,50;31000000,6102040,100;31000000,8000002,10", "activity_type": 6, "id": 1062, "desc_short": "击退或者击杀敌方士兵，士兵等级越高伤亡越多，获得的积分越多。", "name": "战意滔天"}, {"desc_long": "训练单个1级兵\\s\\s\\s\\s10积分\\n训练单个2级兵\\s\\s\\s\\s30积分\\n训练单个3级兵\\s\\s\\s\\s100积分\\n训练单个4级兵\\s\\s\\s\\s180积分\\n训练单个5级兵\\s\\s\\s\\s270积分\\n训练单个6级兵\\s\\s\\s\\s360积分", "rank_reward_type": -1, "point_reward": "200000,12100000,10;200000,12100006,10;200000,12100030,10;200000,12100024,10;1000000,82000009,10;1000000,82001028,3;1000000,12100012,20;1000000,1000042,200;2000000,12100000,30;2000000,12100006,30;2000000,12100030,30;2000000,12100024,30;4100000,82000009,20;4100000,82001028,5;4100000,12100012,50;4100000,1000042,500;10000000,82001027,1;10000000,12102003,20;10000000,6102040,50;10000000,8000010,5", "activity_type": 2, "id": 1070, "desc_short": "训练的士兵等级越高，获得的积分越多。", "name": "训练士兵"}, {"desc_long": "治疗单个1级兵\\s\\s\\s\\s5积分\\n治疗单个2级兵\\s\\s\\s\\s15积分\\n治疗单个3级兵\\s\\s\\s\\s50积分\\n治疗单个4级兵\\s\\s\\s\\s90积分\\n治疗单个5级兵\\s\\s\\s\\s135积分\\n治疗单个6级兵\\s\\s\\s\\s180积分", "rank_reward_type": -1, "point_reward": "540000,12100000,10;540000,12100006,10;540000,12100030,10;540000,12100024,10;2200000,82000009,10;2200000,82001028,3;2200000,12100012,20;2200000,1000042,200;5400000,12100000,30;5400000,12100006,30;5400000,12100030,30;5400000,12100024,30;11000000,82000009,20;11000000,82001028,5;11000000,12100012,50;11000000,1000042,500;22000000,82001027,1;22000000,12102003,20;22000000,6102040,50;22000000,8000026,5", "activity_type": 11, "id": 1071, "desc_short": "治疗伤兵可获得积分，士兵等级越高、数量越多，获得的积分越多", "name": "治疗伤兵"}, {"desc_long": "歼灭单个2级兵\\s\\s\\s\\s8积分\\n歼灭单个3级兵\\s\\s\\s\\s32积分\\n歼灭单个4级兵\\s\\s\\s\\s80积分\\n歼灭单个5级兵\\s\\s\\s\\s140积分\\n歼灭单个6级兵\\s\\s\\s\\s200积分", "rank_reward_type": -1, "point_reward": "930000,12100000,10;930000,12100006,10;930000,12100030,10;930000,12100024,10;4700000,82000009,10;4700000,82001028,3;4700000,12100012,20;4700000,1000042,200;9300000,12100000,30;9300000,12100006,30;9300000,12100030,30;9300000,12100024,30;19000000,82000009,20;19000000,82001028,5;19000000,12100012,50;19000000,1000042,500;47000000,12102021,1;47000000,12102003,50;47000000,6102040,100;47000000,8000002,10", "activity_type": 6, "id": 1072, "desc_short": "击退或者击杀敌方士兵，士兵等级越高伤亡越多，获得的积分越多。", "name": "战意滔天"}, {"desc_long": "训练单个1级兵\\s\\s\\s\\s10积分\\n训练单个2级兵\\s\\s\\s\\s30积分\\n训练单个3级兵\\s\\s\\s\\s100积分\\n训练单个4级兵\\s\\s\\s\\s180积分\\n训练单个5级兵\\s\\s\\s\\s270积分\\n训练单个6级兵\\s\\s\\s\\s360积分", "rank_reward_type": -1, "point_reward": "300000,12100000,10;300000,12100006,10;300000,12100030,10;300000,12100024,10;1500000,82000009,10;1500000,82001028,3;1500000,12100012,20;1500000,1000042,200;3000000,12100000,30;3000000,12100006,30;3000000,12100030,30;3000000,12100024,30;5900000,82000009,20;5900000,82001028,5;5900000,12100012,50;5900000,1000042,500;15000000,82001027,1;15000000,12102003,20;15000000,6102040,50;15000000,8000010,5", "activity_type": 2, "id": 1080, "desc_short": "训练的士兵等级越高，获得的积分越多。", "name": "训练士兵"}, {"desc_long": "治疗单个1级兵\\s\\s\\s\\s5积分\\n治疗单个2级兵\\s\\s\\s\\s15积分\\n治疗单个3级兵\\s\\s\\s\\s50积分\\n治疗单个4级兵\\s\\s\\s\\s90积分\\n治疗单个5级兵\\s\\s\\s\\s135积分\\n治疗单个6级兵\\s\\s\\s\\s180积分", "rank_reward_type": -1, "point_reward": "760000,12100000,10;760000,12100006,10;760000,12100030,10;760000,12100024,10;3000000,82000009,10;3000000,82001028,3;3000000,12100012,20;3000000,1000042,200;7600000,12100000,30;7600000,12100006,30;7600000,12100030,30;7600000,12100024,30;15000000,82000009,20;15000000,82001028,5;15000000,12100012,50;15000000,1000042,500;30000000,82001027,1;30000000,12102003,20;30000000,6102040,50;30000000,8000026,5", "activity_type": 11, "id": 1081, "desc_short": "治疗伤兵可获得积分，士兵等级越高、数量越多，获得的积分越多", "name": "治疗伤兵"}, {"desc_long": "歼灭单个2级兵\\s\\s\\s\\s8积分\\n歼灭单个3级兵\\s\\s\\s\\s32积分\\n歼灭单个4级兵\\s\\s\\s\\s80积分\\n歼灭单个5级兵\\s\\s\\s\\s140积分\\n歼灭单个6级兵\\s\\s\\s\\s200积分", "rank_reward_type": -1, "point_reward": "1400000,12100000,10;1400000,12100006,10;1400000,12100030,10;1400000,12100024,10;7000000,82000009,10;7000000,82001028,3;7000000,12100012,20;7000000,1000042,200;14000000,12100000,30;14000000,12100006,30;14000000,12100030,30;14000000,12100024,30;28000000,82000009,20;28000000,82001028,5;28000000,12100012,50;28000000,1000042,500;70000000,12102021,1;70000000,12102003,50;70000000,6102040,100;70000000,8000002,10", "activity_type": 6, "id": 1082, "desc_short": "击退或者击杀敌方士兵，士兵等级越高伤亡越多，获得的积分越多。", "name": "战意滔天"}, {"desc_long": "击败11级禁卫军获得\\s\\s\\s\\s500积分\\n击败12级禁卫军获得\\s\\s\\s\\s1000积分\\n击败13级禁卫军获得\\s\\s\\s\\s1500积分\\n击败14级禁卫军获得\\s\\s\\s\\s2000积分\\n击败15级禁卫军获得\\s\\s\\s\\s2500积分\\n击败16级禁卫军获得\\s\\s\\s\\s3000积分\\n击败17级禁卫军获得\\s\\s\\s\\s3500积分\\n击败18级禁卫军获得\\s\\s\\s\\s4000积分\\n击败19级禁卫军获得\\s\\s\\s\\s4500积分\\n击败20级禁卫军获得\\s\\s\\s\\s5000积分\\n击败21级禁卫军获得\\s\\s\\s\\s5500积分\\n击败22级禁卫军获得\\s\\s\\s\\s6000积分\\n击败23级禁卫军获得\\s\\s\\s\\s6500积分\\n击败24级禁卫军获得\\s\\s\\s\\s7000积分\\n击败25级禁卫军获得\\s\\s\\s\\s7500积分\\n击败26级禁卫军获得\\s\\s\\s\\s8000积分\\n击败27级禁卫军获得\\s\\s\\s\\s8500积分\\n击败28级禁卫军获得\\s\\s\\s\\s9000积分\\n击败29级禁卫军获得\\s\\s\\s\\s9500积分\\n击败30级禁卫军获得\\s\\s\\s\\s10000积分", "rank_reward_type": -1, "point_reward": "50000,8000058,1;100000,8000065,1;200000,82001028,5", "activity_type": 5, "id": 2000, "desc_short": "王城禁卫在21：30至21：35出现，周一出现在魏王城，周三出现在蜀王城，周五出现在吴王城，周日出现在洛阳。", "name": "王城禁卫"}, {"desc_long": "击败1级蛮夷入侵者获得\\s\\s\\s\\s2积分\\n击败2级蛮夷入侵者获得\\s\\s\\s\\s4积分\\n击败3级蛮夷入侵者获得\\s\\s\\s\\s6积分\\n击败4级蛮夷入侵者获得\\s\\s\\s\\s8积分\\n击败5级蛮夷入侵者获得\\s\\s\\s\\s10积分\\n击败6级蛮夷入侵者获得\\s\\s\\s\\s12积分\\n击败7级蛮夷入侵者获得\\s\\s\\s\\s14积分\\n击败8级蛮夷入侵者获得\\s\\s\\s\\s16积分\\n击败9级蛮夷入侵者获得\\s\\s\\s\\s18积分\\n击败10级蛮夷入侵者获得\\s\\s\\s\\s20积分", "rank_reward_type": -1, "point_reward": "50000,8000058,1;100000,8000065,1;200000,82001028,5", "activity_type": 5, "id": 2010, "desc_short": "蛮夷部队每周二、四、六的21：30至21：35出现在世界各处出现，消灭蛮夷部队，获得积分奖励！", "name": "蛮夷入侵"}, {"desc_long": "采集10铜钱\\s\\s\\s\\s1积分\\n采集10粮食\\s\\s\\s\\s1积分\\n采集10木材\\s\\s\\s\\s1积分\\n采集10铁矿\\s\\s\\s\\s10积分\\n采集10黄金\\s\\s\\s\\s100积分\\n采集10元宝\\s\\s\\s\\s1000积分", "rank_reward_type": -1, "point_reward": "60000,8000058,1;120000,8000065,1;250000,82001028,5", "activity_type": 1, "id": 2100, "desc_short": "采集的资源越多、越贵重，获得的积分越多。", "name": "采集资源"}, {"desc_long": "研究院研究科技提升1战力\\s\\s\\s\\s10积分\\n战争堡研究科技提升1战力\\s\\s\\s\\s20积分\\n发展堡研究科技提升1战力\\s\\s\\s\\s15积分", "rank_reward_type": -1, "point_reward": "16000,8000002,1;50000,8000061,1;100000,1000006,100", "activity_type": 10, "id": 2101, "desc_short": "研究院、战争堡、发展堡进行科技研究，获得积分", "name": "研究科技"}, {"desc_long": "拜访宝箱\\s\\s\\s\\s10000积分", "rank_reward_type": -1, "point_reward": "50000,8000058,1;100000,8000065,1;200000,82001028,5", "activity_type": 8, "id": 2102, "desc_short": "拜访宝箱的次数越多，获得的积分越多。", "name": "拜访宝箱"}, {"desc_long": "训练单个1级兵\\s\\s\\s\\s10积分\\n训练单个2级兵\\s\\s\\s\\s30积分\\n训练单个3级兵\\s\\s\\s\\s100积分\\n训练单个4级兵\\s\\s\\s\\s180积分\\n训练单个5级兵\\s\\s\\s\\s270积分\\n训练单个6级兵\\s\\s\\s\\s360积分", "rank_reward_type": -1, "point_reward": "50000,8000058,1;100000,8000065,1;200000,82001028,5", "activity_type": 2, "id": 2103, "desc_short": "训练的士兵等级越高，获得的积分越多。", "name": "训练士兵"}, {"desc_long": "探索陵寝\\s\\s\\s\\s20000积分", "rank_reward_type": -1, "point_reward": "50000,8000058,1;100000,8000065,1;200000,82001028,5", "activity_type": 7, "id": 2104, "desc_short": "探索陵寝，探索的陵寝数量越多，获得的积分越多。", "name": "探索陵寝"}, {"desc_long": "治疗单个1级兵\\s\\s\\s\\s5积分\\n治疗单个2级兵\\s\\s\\s\\s15积分\\n治疗单个3级兵\\s\\s\\s\\s50积分\\n治疗单个4级兵\\s\\s\\s\\s90积分\\n治疗单个5级兵\\s\\s\\s\\s135积分\\n治疗单个6级兵\\s\\s\\s\\s180积分", "rank_reward_type": -1, "point_reward": "760000,12100000,10;760000,12100006,10;760000,12100030,10;760000,12100024,10;3000000,82000009,10;3000000,82001028,3;3000000,12100012,40;7600000,12100000,30;7600000,12100006,30;7600000,12100030,30;7600000,12100024,30;15000000,82000009,20;15000000,82001028,5;15000000,12100012,100;30000000,82001027,1;30000000,12102003,20;30000000,6102040,50;30000000,8000026,5", "activity_type": 11, "id": 2105, "desc_short": "治疗伤兵可获得积分，士兵等级越高、数量越多，获得的积分越多", "name": "治疗伤兵"}, {"desc_long": "消耗10粮食\\s\\s\\s\\s1积分\\n消耗10木材\\s\\s\\s\\s1积分\\n消耗10铁矿\\s\\s\\s\\s10积分\\n消耗10黄金\\s\\s\\s\\s100积分\\n消耗10秒时间\\s\\s\\s\\s1积分", "rank_reward_type": -1, "point_reward": "50000,8000058,1;100000,8000065,1;200000,82001028,5", "activity_type": 9, "id": 2106, "desc_short": "建筑升级消耗的资源越多，花费的时间越长，获得的积分越多。", "name": "建筑升级"}, {"desc_long": "歼灭单个1级兵\\s\\s\\s\\s3积分\\n歼灭单个2级兵\\s\\s\\s\\s12积分\\n歼灭单个3级兵\\s\\s\\s\\s36积分\\n歼灭单个4级兵\\s\\s\\s\\s80积分\\n歼灭单个5级兵\\s\\s\\s\\s140积分\\n歼灭单个6级兵\\s\\s\\s\\s200积分\\n", "rank_reward_type": -1, "point_reward": "1400000,12100000,10;1400000,12100006,10;1400000,12100030,10;1400000,12100024,10;7000000,82000009,10;7000000,82001028,3;7000000,12100012,40;14000000,12100000,30;14000000,12100006,30;14000000,12100030,30;14000000,12100024,30;28000000,82000009,20;28000000,82001028,5;28000000,12100012,100;70000000,12102021,1;70000000,12102003,50;70000000,6102040,100;70000000,8000002,10", "activity_type": 6, "id": 2107, "desc_short": "击退或者击杀敌方士兵，士兵等级越高伤亡越多，获得的积分越多。", "name": "战意滔天"}]