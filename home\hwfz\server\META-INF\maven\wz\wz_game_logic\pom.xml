<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>wz</groupId>
		<artifactId>game_main</artifactId>
		<version>0.0.1-SNAPSHOT</version>
		<relativePath>../game_main/pom.xml</relativePath>
	</parent>
	<artifactId>wz_game_logic</artifactId>
	<packaging>jar</packaging>
	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<encoding>UTF-8</encoding>
					<source>1.8</source>
					<target>1.8</target>
				</configuration>
			</plugin>
			<plugin>
				<artifactId>maven-assembly-plugin</artifactId>
				<configuration>
					<appendAssemblyId>false</appendAssemblyId>
					<descriptorRefs>
						<descriptorRef>jar-with-dependencies</descriptorRef>
					</descriptorRefs>
					<archive>
						<manifest>
							<mainClass>game.server.GameLogicServer</mainClass>
						</manifest>
					</archive>
				</configuration>
				<executions>
					<execution>
						<id>make-assembly</id>
						<phase>package</phase>
						<goals>
							<goal>assembly</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>


		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<includes>
					<include>**/*.properties</include>
					<include>**/*.xml</include>
					<include>**/*.txt</include>
					<include>**/*.json</include>
				</includes>
			</resource>
		</resources>
	</build>


	<dependencies>
		<dependency>
			<groupId>wz</groupId>
			<artifactId>wz_common</artifactId>
		</dependency>

		<!-- spring -->
		<!--<dependency>-->
			<!--<groupId>org.springframework</groupId>-->
			<!--<artifactId>spring-webmvc</artifactId>-->
			<!--<version>4.2.3.RELEASE</version>-->
		<!--</dependency>-->

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context</artifactId>
			<version>4.2.3.RELEASE</version>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context-support</artifactId>
			<version>4.2.3.RELEASE</version>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-core</artifactId>
			<version>4.2.3.RELEASE</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.data</groupId>
			<artifactId>spring-data-jpa</artifactId>
			<version>1.10.4.RELEASE</version>
		</dependency>

		<!--dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
    		<artifactId>jackson-core</artifactId>
    		<version>2.2.2</version>
		</dependency-->
		<!--<dependency>-->
			<!--<groupId>org.springframework</groupId>-->
			<!--<artifactId>spring-orm</artifactId>-->
			<!--<version>4.2.3.RELEASE</version>-->
		<!--</dependency>-->
		<dependency>
			<groupId>cglib</groupId>
			<artifactId>cglib</artifactId>
			<version>3.2.4</version>
		</dependency>

		<!-- jta -->
		<!--<dependency>-->
			<!--<groupId>javax.transaction</groupId>-->
			<!--<artifactId>jta</artifactId>-->
			<!--<version>1.1</version>-->
		<!--</dependency>-->

		<!-- dbcp -->
		<!--<dependency>-->
			<!--<groupId>commons-dbcp</groupId>-->
			<!--<artifactId>commons-dbcp</artifactId>-->
			<!--<version>1.4</version>-->
		<!--</dependency>-->
		<dependency>
			<groupId>commons-cli</groupId>
			<artifactId>commons-cli</artifactId>
			<version>1.3.1</version>
		</dependency>

		<dependency>
			<groupId>org.mongodb</groupId>
			<artifactId>mongo-java-driver</artifactId>
			<version>3.2.2</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.data</groupId>
			<artifactId>spring-data-mongodb</artifactId>
			<version>1.9.4.RELEASE</version>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
			<version>4.2.3.RELEASE</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjweaver</artifactId>
			<version>1.8.7</version>
		</dependency>
		<dependency>
		    <groupId>org.graylog2</groupId>
		    <artifactId>syslog4j</artifactId>
		    <version>0.9.57</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/commons-beanutils/commons-beanutils -->
<dependency>
    <groupId>commons-beanutils</groupId>
    <artifactId>commons-beanutils</artifactId>
    <version>1.9.2</version>
</dependency>

	</dependencies>

</project>