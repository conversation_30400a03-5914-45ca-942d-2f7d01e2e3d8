# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
####################################################
#
#           ANSI COLOR LOGGER CONFIGURATION
#
# Format for AnsiColorLogger.*=
#  Attribute;Foreground;Background
#
#  Attribute is one of the following:
#  0 -> Reset All Attributes (return to normal mode)
#  1 -> Bright (Usually turns on BOLD)
#  2 -> Dim
#  3 -> Underline
#  5 -> link
#  7 -> Reverse
#  8 -> Hidden
#
#  Foreground is one of the following:
#  30 -> Black
#  31 -> Red
#  32 -> Green
#  33 -> Yellow
#  34 -> Blue
#  35 -> Magenta
#  36 -> Cyan
#  37 -> White
#
#  Background is one of the following:
#  40 -> Black
#  41 -> Red
#  42 -> Green
#  43 -> Yellow
#  44 -> Blue
#  45 -> Magenta
#  46 -> Cyan
#  47 -> White
#
####################################################

AnsiColorLogger.ERROR_COLOR=2;31
AnsiColorLogger.WARNING_COLOR=2;35
AnsiColorLogger.INFO_COLOR=2;36
AnsiColorLogger.VERBOSE_COLOR=2;32
AnsiColorLogger.DEBUG_COLOR=2;34
