<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<groupId>org.springframework.data</groupId>
	<artifactId>spring-data-jpa</artifactId>
	<version>1.10.4.RELEASE</version>

	<name>Spring Data JPA</name>
	<description>Spring Data module for JPA repositories.</description>
	<url>http://projects.spring.io/spring-data-jpa</url>

	<parent>
		<groupId>org.springframework.data.build</groupId>
		<artifactId>spring-data-parent</artifactId>
		<version>1.8.4.RELEASE</version>
	</parent>

	<properties>

		<dist.key>DATAJPA</dist.key>

		<eclipselink>2.6.2</eclipselink>
		<hibernate>3.6.10.Final</hibernate>
		<hibernate.artifact>hibernate-entitymanager</hibernate.artifact>
		<hsqldb1>********</hsqldb1>
		<jpa>2.0.0</jpa>
		<openjpa>2.4.1</openjpa>
		<springdata.commons>1.12.4.RELEASE</springdata.commons>

		<sonar.dynamicAnalysis>reuseReports</sonar.dynamicAnalysis>

	</properties>

	<profiles>
		<profile>
			<id>hibernate-41</id>
			<properties>
				<hibernate>4.1.12.Final</hibernate>
			</properties>
		</profile>
		<profile>
			<id>hibernate-42</id>
			<properties>
				<hibernate>4.2.21.Final</hibernate>
			</properties>
		</profile>
		<profile>
			<id>hibernate-42-next</id>
			<properties>
				<hibernate>4.2.24-SNAPSHOT</hibernate>
			</properties>
			<repositories>
				<repository>
					<id>jboss</id>
					<url>https://repository.jboss.org/nexus/content/repositories/public</url>
				</repository>
			</repositories>
		</profile>
		<profile>
			<id>hibernate-43</id>
			<properties>
				<hibernate>4.3.11.Final</hibernate>
			</properties>
		</profile>
		<profile>
			<id>hibernate-43-next</id>
			<properties>
				<hibernate>4.3.12-SNAPSHOT</hibernate>
			</properties>
			<repositories>
				<repository>
					<id>jboss</id>
					<url>https://repository.jboss.org/nexus/content/repositories/public</url>
				</repository>
			</repositories>
		</profile>
		<profile>
			<id>hibernate-5</id>
			<properties>
				<hibernate>5.0.9.Final</hibernate>
			</properties>
		</profile>
		<profile>
			<id>hibernate-5-next</id>
			<properties>
				<hibernate>5.0.10-SNAPSHOTS</hibernate>
			</properties>
			<repositories>
				<repository>
					<id>jboss</id>
					<url>https://repository.jboss.org/nexus/content/repositories/public</url>
				</repository>
			</repositories>
		</profile>
		<profile>
			<id>hibernate-51</id>
			<properties>
				<hibernate>5.1.0.Final</hibernate>
			</properties>
		</profile>
		<profile>
			<id>hibernate-51-next</id>
			<properties>
				<hibernate>5.1.1-SNAPSHOT</hibernate>
			</properties>
			<repositories>
				<repository>
					<id>jboss</id>
					<url>https://repository.jboss.org/nexus/content/repositories/public</url>
				</repository>
			</repositories>
		</profile>
		<profile>
			<id>hibernate-52</id>
			<properties>
				<hibernate>5.2.2.Final</hibernate>
				<hibernate.artifact>hibernate-core</hibernate.artifact>
			</properties>
		</profile>
		<profile>
			<id>hibernate-52-next</id>
			<properties>
				<hibernate>5.2.3-SNAPSHOT</hibernate>
				<hibernate.artifact>hibernate-core</hibernate.artifact>
			</properties>
			<repositories>
				<repository>
					<id>jboss</id>
					<url>https://repository.jboss.org/nexus/content/repositories/public</url>
				</repository>
			</repositories>
		</profile>
		<profile>
			<id>eclipselink-next</id>
			<properties>
				<eclipselink>2.6.3-SNAPSHOT</eclipselink>
			</properties>
			<repositories>
				<repository>
					<id>oss-sonatype</id>
					<url>https://oss.sonatype.org/content/groups/staging</url>
				</repository>
			</repositories>
		</profile>
		<profile>
			<id>eclipselink-27-next</id>
			<properties>
				<eclipselink>2.7.0-SNAPSHOT</eclipselink>
			</properties>
			<repositories>
				<repository>
					<id>oss-sonatype</id>
					<url>https://oss.sonatype.org/content/groups/staging</url>
				</repository>
			</repositories>
		</profile>
		<profile>
			<id>release</id>
			<build>
				<plugins>
					<plugin>
						<groupId>org.jfrog.buildinfo</groupId>
						<artifactId>artifactory-maven-plugin</artifactId>
						<inherited>false</inherited>
					</plugin>
				</plugins>
			</build>
		</profile>
	</profiles>

	<dependencies>

		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>spring-data-commons</artifactId>
			<version>${springdata.commons}</version>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-orm</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-aop</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-tx</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-beans</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-instrument</artifactId>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-core</artifactId>
			<exclusions>
				<exclusion>
					<groupId>commons-logging</groupId>
					<artifactId>commons-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjrt</artifactId>
			<version>${aspectj}</version>
		</dependency>
		<dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjweaver</artifactId>
			<version>${aspectj}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-aspects</artifactId>
			<scope>compile</scope>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.hsqldb</groupId>
			<artifactId>hsqldb</artifactId>
			<version>2.2.8</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>joda-time</groupId>
			<artifactId>joda-time</artifactId>
			<version>${jodatime}</version>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.threeten</groupId>
			<artifactId>threetenbp</artifactId>
			<version>${threetenbp}</version>
			<optional>true</optional>
		</dependency>

		<!-- Persistence providers -->

		<dependency>
			<groupId>org.eclipse.persistence</groupId>
			<artifactId>org.eclipse.persistence.jpa</artifactId>
			<version>${eclipselink}</version>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>${hibernate.artifact}</artifactId>
			<version>${hibernate}</version>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.apache.openjpa</groupId>
			<artifactId>openjpa-persistence-jdbc</artifactId>
			<version>${openjpa}</version>
			<optional>true</optional>
			<exclusions>
				<exclusion>
					<groupId>commons-logging</groupId>
					<artifactId>commons-logging</artifactId>
				</exclusion>
				<!-- Transitive dependency pulls in JUnit 3.8.1 -->
				<!-- http://sourceforge.net/tracker/?func=detail&aid=2572567&group_id=31479&atid=402282 -->
				<exclusion>
					<groupId>junit</groupId>
					<artifactId>junit</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- QueryDsl -->
		<dependency>
			<groupId>com.querydsl</groupId>
			<artifactId>querydsl-apt</artifactId>
			<version>${querydsl}</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>com.querydsl</groupId>
			<artifactId>querydsl-jpa</artifactId>
			<version>${querydsl}</version>
			<optional>true</optional>
		</dependency>

		<!-- CDI -->
		<dependency>
			<groupId>javax.enterprise</groupId>
			<artifactId>cdi-api</artifactId>
			<version>${cdi}</version>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>javax.el</groupId>
			<artifactId>el-api</artifactId>
			<version>${cdi}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.openwebbeans.test</groupId>
			<artifactId>cditest-owb</artifactId>
			<version>${webbeans}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<version>3.0.1</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>17.0</version>
			<scope>test</scope>
		</dependency>

	</dependencies>

	<build>
		<plugins>

			<!--
			     Jacoco plugin redeclared to make sure it's downloaded and
			     the agents can be explicitly added to the test executions.
			-->
			<plugin>
				<groupId>org.jacoco</groupId>
				<artifactId>jacoco-maven-plugin</artifactId>
				<version>${jacoco}</version>
				<configuration>
					<destFile>${jacoco.destfile}</destFile>
				</configuration>
				<executions>
					<execution>
						<id>jacoco-initialize</id>
						<goals>
							<goal>prepare-agent</goal>
						</goals>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>2.12</version>
				<dependencies>
					<dependency>
						<groupId>org.springframework</groupId>
						<artifactId>spring-instrument</artifactId>
						<version>${spring}</version>
						<scope>runtime</scope>
					</dependency>
					<dependency>
						<groupId>org.apache.openjpa</groupId>
						<artifactId>openjpa</artifactId>
						<version>${openjpa}</version>
						<scope>runtime</scope>
					</dependency>
					<dependency>
						<groupId>org.hsqldb</groupId>
						<artifactId>hsqldb</artifactId>
						<version>${hsqldb1}</version>
						<scope>runtime</scope>
					</dependency>
				</dependencies>
				<executions>
					<execution>
						<id>default-test</id>
						<configuration>
							<excludes>
								<exclude>**/*</exclude>
							</excludes>
						</configuration>
					</execution>
					<execution>
						<id>unit-tests</id>
						<goals>
							<goal>test</goal>
						</goals>
						<phase>test</phase>
						<configuration>
							<includes>
								<include>**/*UnitTests.java</include>
							</includes>
						</configuration>
					</execution>
					<execution>
						<id>integration-tests</id>
						<goals>
							<goal>test</goal>
						</goals>
						<phase>test</phase>
						<configuration>
							<includes>
								<include>**/*IntegrationTests.java</include>
								<include>**/*Tests.java</include>
							</includes>
							<excludes>
								<exclude>**/*UnitTests.java</exclude>
								<exclude>**/OpenJpa*</exclude>
								<exclude>**/EclipseLink*</exclude>
							</excludes>
							<argLine>-javaagent:${settings.localRepository}/org/springframework/spring-instrument/${spring}/spring-instrument-${spring}.jar -javaagent:${settings.localRepository}/org/jacoco/org.jacoco.agent/${jacoco}/org.jacoco.agent-${jacoco}-runtime.jar=destfile=${jacoco.destfile}</argLine>
						</configuration>
					</execution>
					<execution>
						<id>eclipselink-tests</id>
						<goals>
							<goal>test</goal>
						</goals>
						<phase>test</phase>
						<configuration>
							<includes>
								<include>**/EclipseLink*Tests.java</include>
							</includes>
							<argLine>-javaagent:${settings.localRepository}/org/jacoco/org.jacoco.agent/${jacoco}/org.jacoco.agent-${jacoco}-runtime.jar=destfile=${jacoco.destfile} -javaagent:${settings.localRepository}/org/eclipse/persistence/org.eclipse.persistence.jpa/${eclipselink}/org.eclipse.persistence.jpa-${eclipselink}.jar -javaagent:${settings.localRepository}/org/springframework/spring-instrument/${spring}/spring-instrument-${spring}.jar</argLine>
						</configuration>
					</execution>
					<execution>
						<id>openjpa-tests</id>
						<goals>
							<goal>test</goal>
						</goals>
						<phase>test</phase>
						<configuration>
							<includes>
								<include>**/OpenJpa*Tests.java</include>
							</includes>
							<argLine>-javaagent:${settings.localRepository}/org/jacoco/org.jacoco.agent/${jacoco}/org.jacoco.agent-${jacoco}-runtime.jar=destfile=${jacoco.destfile} -javaagent:${settings.localRepository}/org/apache/openjpa/openjpa/${openjpa}/openjpa-${openjpa}.jar</argLine>
							<classpathDependencyExcludes>
								<classpathDepencyExclude>org.hsqldb:hsqldb</classpathDepencyExclude>
							</classpathDependencyExcludes>
							<additionalClasspathElements>
								<additionalClasspathElement>${settings.localRepository}/org/hsqldb/hsqldb/${hsqldb1}/hsqldb-${hsqldb1}.jar</additionalClasspathElement>
							</additionalClasspathElements>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>aspectj-maven-plugin</artifactId>
				<version>1.8</version>
				<dependencies>
					<dependency>
						<groupId>org.aspectj</groupId>
						<artifactId>aspectjrt</artifactId>
						<version>${aspectj}</version>
					</dependency>
					<dependency>
						<groupId>org.aspectj</groupId>
						<artifactId>aspectjtools</artifactId>
						<version>${aspectj}</version>
					</dependency>
				</dependencies>
				<executions>
					<execution>
						<goals>
							<goal>compile</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<verbose>true</verbose>
					<!--
						To workaround:

						- https://issues.apache.org/jira/browse/MCOMPILER-205
						- https://issues.apache.org/jira/browse/MCOMPILER-209
						- https://github.com/mojohaus/aspectj-maven-plugin/issues/15

					-->
					<forceAjcCompile>true</forceAjcCompile>
					<aspectLibraries>
						<aspectLibrary>
							<groupId>org.springframework</groupId>
							<artifactId>spring-aspects</artifactId>
						</aspectLibrary>
					</aspectLibraries>
					<includes>
						<include>**/domain/support/AuditingEntityListener.java</include>
					</includes>
					<complianceLevel>${source.level}</complianceLevel>
					<source>${source.level}</source>
					<target>${source.level}</target>
					<xmlConfigured>aop.xml</xmlConfigured>
				</configuration>
			</plugin>

			<plugin>
				<groupId>com.mysema.maven</groupId>
				<artifactId>apt-maven-plugin</artifactId>
				<version>${apt}</version>
				<configuration>
					<logOnlyOnError>true</logOnlyOnError>
					<processors>
						<processor>com.querydsl.apt.jpa.JPAAnnotationProcessor</processor>
						<processor>org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor</processor>
					</processors>
				</configuration>
				<executions>
					<execution>
						<id>sources</id>
						<phase>generate-sources</phase>
						<goals>
							<goal>process</goal>
						</goals>
						<configuration>
							<outputDirectory>target/generated-sources/annotations</outputDirectory>
						</configuration>
					</execution>
					<execution>
						<id>test-sources</id>
						<phase>generate-test-sources</phase>
						<goals>
							<goal>test-process</goal>
						</goals>
						<configuration>
							<testOutputDirectory>target/generated-test-sources/test-annotations</testOutputDirectory>
							<options>
								<querydsl.excludedClasses>
									org.springframework.data.jpa.repository.util.JpaClassUtilsUnitTests.NamedUser,org.springframework.data.jpa.repository.query.ParameterBinderUnitTests.SampleEmbeddable
								</querydsl.excludedClasses>
							</options>
						</configuration>
					</execution>
				</executions>
				<dependencies>
					<dependency>
						<groupId>org.hibernate</groupId>
						<artifactId>hibernate-jpamodelgen</artifactId>
						<version>1.3.0.Final</version>
					</dependency>
					<dependency>
						<groupId>com.google.guava</groupId>
						<artifactId>guava</artifactId>
						<version>17.0</version>
					</dependency>
				</dependencies>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-assembly-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>wagon-maven-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>org.asciidoctor</groupId>
				<artifactId>asciidoctor-maven-plugin</artifactId>
			</plugin>
		</plugins>
	</build>

	<repositories>
		<repository>
			<id>spring-libs-release</id>
			<url>https://repo.spring.io/libs-release</url>
		</repository>
	</repositories>

	<pluginRepositories>
		<pluginRepository>
			<id>spring-plugins-release</id>
			<url>https://repo.spring.io/plugins-release</url>
		</pluginRepository>
	</pluginRepositories>

</project>
