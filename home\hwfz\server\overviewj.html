<!--

     Licensed to the Apache Software Foundation (ASF) under one
     or more contributor license agreements.  See the NOTICE file
     distributed with this work for additional information
     regarding copyright ownership.  The ASF licenses this file
     to you under the Apache License, Version 2.0 (the
     "License"); you may not use this file except in compliance
     with the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing,
     software distributed under the License is distributed on an
     "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
     KIND, either express or implied.  See the License for the
     specific language governing permissions and limitations
     under the License.

-->
<html>
  <head>
    <title>Groovy - An agile dynamic language for the Java Platform</title>
  </head>
  <body>
    <h2>Groovy - An agile dynamic language for the Java Platform<br/>(JavaDoc for Java classes)</h2>

    Groovy...
    <ul>
      <li>is an agile and <b>dynamic language</b> for the <b>Java Virtual Machine</b></li>
      <li>builds upon the strengths of Java but has <b>additional power features</b> inspired by languages like Python, Ruby and Smalltalk</li>
      <li>makes <b>modern programming features</b> available to Java developers with <b>almost-zero learning curve</b></li>
      <li>supports <b>Domain-Specific Languages</b> and other compact syntax so your code becomes <b>easy to read and maintain</b></li>
      <li>makes writing shell and build scripts easy with its <b>powerful processing primitives</b>, OO abilities and an Ant DSL</li>
      <li>increases developer productivity by <b>reducing scaffolding code</b> when developing web, GUI, database or console applications</li>
      <li><b>simplifies testing</b> by supporting unit testing and mocking out-of-the-box</li>
      <li>seamlessly <b>integrates with all existing Java objects and libraries</b></li>
      <li>compiles straight to Java bytecode so you can use it anywhere you can use Java</li>
    </ul>
  </body>
</html>
