<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 2012 The Netty Project
  ~
  ~ The Netty Project licenses this file to you under the Apache License,
  ~ version 2.0 (the "License"); you may not use this file except in compliance
  ~ with the License. You may obtain a copy of the License at:
  ~
  ~   http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
  ~ WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
  ~ License for the specific language governing permissions and limitations
  ~ under the License.
  -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>io.netty</groupId>
    <artifactId>netty-parent</artifactId>
    <version>4.0.29.Final</version>
  </parent>

  <artifactId>netty-all</artifactId>
  <packaging>jar</packaging>

  <name>Netty/All-in-One</name>

  <properties>
    <quickbuild>true</quickbuild>
    <generatedSourceDir>${project.build.directory}/src</generatedSourceDir>
    <dependencyVersionsDir>${project.build.directory}/versions</dependencyVersionsDir>
  </properties>

  <profiles>
    <profile>
      <id>full</id>
      <properties>
        <quickbuild>false</quickbuild>
      </properties>
    </profile>
    <profile>
      <id>coverage</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.jacoco</groupId>
            <artifactId>jacoco-maven-plugin</artifactId>
            <executions>
              <execution>
                <id>jacoco-merge</id>
                <phase>prepare-package</phase>
                <goals>
                  <goal>merge</goal>
                </goals>
                <configuration>
                  <fileSets>
                    <fileSet>
                      <directory>${project.parent.build.directory}/..</directory>
                      <includes>
                        <include>**/target/jacoco.exec</include>
                      </includes>
                    </fileSet>
                  </fileSets>
                </configuration>
              </execution>
              <execution>
                <id>jacoco-report</id>
                <phase>prepare-package</phase>
                <goals>
                  <goal>report</goal>
                </goals>
                <configuration>
                  <outputDirectory>${project.build.directory}/jacoco-report</outputDirectory>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>linux</id>
      <activation>
        <os>
          <family>linux</family>
        </os>
      </activation>
      <dependencies>
        <!-- All release modules -->
        <dependency>
          <groupId>${project.groupId}</groupId>
          <artifactId>netty-transport-native-epoll</artifactId>
          <version>${project.version}</version>
          <classifier>${os.detected.classifier}</classifier>
          <scope>compile</scope>
          <optional>true</optional>
        </dependency>
      </dependencies>
    </profile>
  </profiles>

  <dependencies>

    <!-- All release modules -->
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-buffer</artifactId>
      <version>${project.version}</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-codec</artifactId>
      <version>${project.version}</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-codec-haproxy</artifactId>
      <version>${project.version}</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-codec-http</artifactId>
      <version>${project.version}</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-codec-socks</artifactId>
      <version>${project.version}</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-common</artifactId>
      <version>${project.version}</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-handler</artifactId>
      <version>${project.version}</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-transport</artifactId>
      <version>${project.version}</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-transport-rxtx</artifactId>
      <version>${project.version}</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-transport-sctp</artifactId>
      <version>${project.version}</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-transport-udt</artifactId>
      <version>${project.version}</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-example</artifactId>
      <version>${project.version}</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>

    <!-- Add optional dependencies explicitly to avoid Javadoc warnings and errors. -->
    <dependency>
      <groupId>com.google.protobuf</groupId>
      <artifactId>protobuf-java</artifactId>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.jboss.marshalling</groupId>
      <artifactId>jboss-marshalling</artifactId>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>commons-logging</groupId>
      <artifactId>commons-logging</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>log4j</groupId>
      <artifactId>log4j</artifactId>
      <optional>true</optional>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <artifactId>maven-clean-plugin</artifactId>
        <executions>
          <execution>
            <id>clean-first</id>
            <phase>generate-resources</phase>
            <goals>
              <goal>clean</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-dependency-plugin</artifactId>
        <executions>
          <!-- Populate the properties whose key is groupId:artifactId:type
                                   and whose value is the path to the artifact -->
          <execution>
            <id>locate-dependencies</id>
            <phase>initialize</phase>
            <goals>
              <goal>properties</goal>
            </goals>
          </execution>

          <!-- Unpack all source files -->
          <execution>
            <id>unpack-sources</id>
            <phase>prepare-package</phase>
            <goals>
              <goal>unpack-dependencies</goal>
            </goals>
            <configuration>
              <classifier>sources</classifier>
              <includes>io/netty/**</includes>
              <includeGroupIds>${project.groupId}</includeGroupIds>
              <outputDirectory>${generatedSourceDir}</outputDirectory>
            </configuration>
          </execution>

          <!-- Unpack all class files -->
          <execution>
            <id>unpack-jars</id>
            <phase>prepare-package</phase>
            <goals>
              <goal>unpack-dependencies</goal>
            </goals>
            <configuration>
              <excludes>io/netty/example/**,META-INF/native/libnetty-tcnative*</excludes>
              <includes>io/netty/**,META-INF/native/**</includes>
              <includeGroupIds>${project.groupId}</includeGroupIds>
              <outputDirectory>${project.build.outputDirectory}</outputDirectory>
            </configuration>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <artifactId>maven-antrun-plugin</artifactId>
        <executions>
          <!-- Instead of generating a new version property file, merge others' version property files into one. -->
          <execution>
            <id>write-version-properties</id>
            <phase>none</phase>
          </execution>
          <execution>
            <id>merge-version-properties</id>
            <phase>prepare-package</phase>
            <goals>
              <goal>run</goal>
            </goals>
            <configuration>
              <target>
                <taskdef resource="net/sf/antcontrib/antlib.xml" />
                <propertyselector property="versions" match="^(${project.groupId}:(?!netty-example)[^:]+:jar(?::[^:]+)?)$" select="\1" />
                <for list="${versions}" param="x">
                  <sequential>
                    <unzip src="${@{x}}" dest="${dependencyVersionsDir}">
                      <patternset>
                        <include name="META-INF/${project.groupId}.versions.properties" />
                      </patternset>
                    </unzip>
                    <concat destfile="${project.build.outputDirectory}/META-INF/${project.groupId}.versions.properties" append="true">
                            <path path="${dependencyVersionsDir}/META-INF/${project.groupId}.versions.properties" />
                    </concat>
                  </sequential>
                </for>
                <delete dir="${dependencyVersionsDir}" quiet="true" />
              </target>
            </configuration>
          </execution>

          <!-- Clean everything once finished so that IDE doesn't find the unpacked files. -->
          <execution>
            <id>clean-source-directory</id>
            <phase>package</phase>
            <goals>
              <goal>run</goal>
            </goals>
            <configuration>
              <target>
                <delete dir="${generatedSourceDir}" quiet="true" />
                <delete dir="${dependencyVersionsDir}" quiet="true" />
                <delete dir="${project.build.outputDirectory}" quiet="true" />
              </target>
            </configuration>
          </execution>
        </executions>
      </plugin>

      <!-- Include the directory where the source files were unpacked -->
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>build-helper-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>add-source</id>
            <phase>prepare-package</phase>
            <goals>
              <goal>add-source</goal>
            </goals>
            <configuration>
              <sources>
                <source>${generatedSourceDir}</source>
              </sources>
            </configuration>
          </execution>
        </executions>
      </plugin>

      <!-- Disable OSGi bundle manifest generation -->
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <executions>
          <execution>
            <id>generate-manifest</id>
            <phase>none</phase>
          </execution>
        </executions>
      </plugin>
      <!-- Override the default JAR configuration -->
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <executions>
          <execution>
            <id>default-jar</id>
            <phase>none</phase>
          </execution>
          <execution>
            <id>all-in-one-jar</id>
            <phase>package</phase>
            <goals>
              <goal>jar</goal>
            </goals>
        <configuration>
          <archive>
            <manifest>
              <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
            </manifest>
            <index>true</index>
          </archive>
        </configuration>
          </execution>
        </executions>
      </plugin>

      <!-- Disable animal sniffer -->
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>animal-sniffer-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>default</id>
            <phase>none</phase>
          </execution>
        </executions>
      </plugin>

      <!-- Disable checkstyle -->
      <plugin>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <executions>
          <execution>
            <id>check-style</id>
            <phase>none</phase>
          </execution>
        </executions>
      </plugin>

      <!-- Disable all plugin executions configured by jar packaging -->
      <plugin>
        <artifactId>maven-resources-plugin</artifactId>
        <executions>
          <execution>
            <id>default-resources</id>
            <phase>none</phase>
          </execution>
          <execution>
            <id>default-testResources</id>
            <phase>none</phase>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <executions>
          <execution>
            <id>default-compile</id>
            <phase>none</phase>
          </execution>
          <execution>
            <id>default-testCompile</id>
            <phase>none</phase>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <executions>
          <execution>
            <id>default-test</id>
            <phase>none</phase>
          </execution>
        </executions>
      </plugin>

      <!-- Generate Xref -->
      <plugin>
        <artifactId>maven-jxr-plugin</artifactId>
        <executions>
          <execution>
            <id>generate-xref</id>
            <phase>package</phase>
            <goals>
              <goal>jxr</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <skip>${quickbuild}</skip>
          <linkJavadoc>true</linkJavadoc>
          <destDir>${project.build.directory}/xref</destDir>
          <javadocDir>${project.build.directory}/api</javadocDir>
          <docTitle>Netty Source Xref (${project.version})</docTitle>
          <windowTitle>Netty Source Xref (${project.version})</windowTitle>
        </configuration>
        <dependencies>
          <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>netty-build</artifactId>
            <version>19</version>
          </dependency>
        </dependencies>
      </plugin>

      <!-- Generate Javadoc -->
      <plugin>
        <artifactId>maven-javadoc-plugin</artifactId>
        <executions>
          <execution>
            <id>attach-javadocs</id>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <skip>${quickbuild}</skip>
          <excludePackageNames>*.internal,*.example</excludePackageNames>
          <docfilessubdirs>true</docfilessubdirs>
          <outputDirectory>${project.build.directory}/api</outputDirectory>
          <overview>${project.basedir}/src/javadoc/overview.html</overview>
          <doctitle>Netty API Reference (${project.version})</doctitle>
          <windowtitle>Netty API Reference (${project.version})</windowtitle>
          <detectJavaApiLink>false</detectJavaApiLink>
          <additionalparam>
            -link http://docs.oracle.com/javase/7/docs/api/
            -link http://code.google.com/apis/protocolbuffers/docs/reference/java/
            -link http://docs.oracle.com/javaee/6/api/
            -link http://www.slf4j.org/apidocs/
            -link http://commons.apache.org/proper/commons-logging/javadocs/api-1.1.2/
            -link http://logging.apache.org/log4j/1.2/apidocs/

            -group "Low-level data representation" io.netty.buffer*
            -group "Central interface for all I/O operations" io.netty.channel*
            -group "Client &amp; Server bootstrapping utilities" io.netty.bootstrap*
            -group "Reusable I/O event interceptors" io.netty.handler*
            -group "Miscellaneous" io.netty.util*
          </additionalparam>
          <locale>en_US</locale>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>

