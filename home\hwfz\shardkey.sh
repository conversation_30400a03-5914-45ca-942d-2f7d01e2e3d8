serverid=$HOSTNAME
#serverid="test"
echo $serverid
#开启shardkey
./mongo 127.0.0.1:27017/admin --eval "printjson(db.runCommand({'enablesharding':'$serverid'}));"
#为表创建shardkey
./mongo 127.0.0.1:27017/$serverid --eval "
printjson(sh.shardCollection('$serverid'+'.wzCharacter', { 'accountId':1} )),
printjson(sh.shardCollection('$serverid'+'.accountInfo', { '_id':'hashed'} )),
printjson(sh.shardCollection('$serverid'+'.bagContainer', { '_id':'hashed'} )),
printjson(sh.shardCollection('$serverid'+'.buildContainer', { '_id':'hashed'} )),
printjson(sh.shardCollection('$serverid'+'.channelMsgEntity', { 'guildId':'hashed'} )),
printjson(sh.shardCollection('$serverid'+'.charBuff', { '_id':'hashed'} )),
printjson(sh.shardCollection('$serverid'+'.charDungeonInfo', { '_id':'hashed'} )),
printjson(sh.shardCollection('$serverid'+'.charPropsContainer', { '_id':'hashed'} )),
printjson(sh.shardCollection('$serverid'+'.charShop', { '_id':'hashed'} )),
printjson(sh.shardCollection('$serverid'+'.charSkillContainer', { '_id':'hashed'} )),
printjson(sh.shardCollection('$serverid'+'.charTech', { '_id':'hashed'} )),
printjson(sh.shardCollection('$serverid'+'.characterCity', { '_id':'hashed'} )),
printjson(sh.shardCollection('$serverid'+'.dailyTaskEntity', { '_id':'hashed'} )),
printjson(sh.shardCollection('$serverid'+'.equipContainer', { '_id':'hashed'} )),
printjson(sh.shardCollection('$serverid'+'.friendContainer', { '_id':'hashed'} )),
printjson(sh.shardCollection('$serverid'+'.gloryContainer', { '_id':'hashed'} )),
printjson(sh.shardCollection('$serverid'+'.helpDefenseContainer', { '_id':'hashed'} )),
printjson(sh.shardCollection('$serverid'+'.heroContainer', { '_id':'hashed'} )),
printjson(sh.shardCollection('$serverid'+'.inviteRecruitInfo', { '_id':'hashed'} )),
printjson(sh.shardCollection('$serverid'+'.recharge', { '_id':'hashed'} )),
printjson(sh.shardCollection('$serverid'+'.runShopContainer', { '_id':'hashed'} )) ,
printjson(sh.shardCollection('$serverid'+'.signIn', { '_id':'hashed'} )),
printjson(sh.shardCollection('$serverid'+'.talentContainer', { '_id':'hashed'} )),
printjson(sh.shardCollection('$serverid'+'.warInfo', { '_id':1} )) ,
printjson(sh.shardCollection('$serverid'+'.worldContainer', { '_id':'hashed'} )) ,
printjson(sh.shardCollection('$serverid'+'.cityEntity', { '_id':'hashed'} ))	,
printjson(sh.shardCollection('$serverid'+'.buildingBase', { 'playerId':'hashed'} )),
printjson(sh.shardCollection('$serverid'+'.activity', { 'playerId':'hashed'} )),
printjson(sh.shardCollection('$serverid'+'.taskEntity', { 'playerId':'hashed'} )) ,
printjson(sh.shardCollection('$serverid'+'.wujiang', { 'playerId':'hashed'} )),
printjson(sh.shardCollection('$serverid'+'.worldPlayerArmyEntity', { 'playerId':'hashed'} )),
printjson(sh.shardCollection('$serverid'+'.mailEntity', { 'receiveId':'hashed'} )),
printjson(sh.shardCollection('$serverid'+'.guild', { '_id':'hashed'} )),
printjson(sh.shardCollection('$serverid'+'.guildBuffContainer', { '_id':'hashed'} )),
printjson(sh.shardCollection('$serverid'+'.guildLog', { 'guildId':'hashed'} )),
printjson(sh.shardCollection('$serverid'+'.guildMsgEntity', { 'guildId':'hashed'} )),
printjson(sh.shardCollection('$serverid'+'.guildTaskEntity', { '_id':'hashed'} )),
printjson(sh.shardCollection('$serverid'+'.guildTaskPlayerData', { '_id':'hashed'} )),
printjson(sh.shardCollection('$serverid'+'.guildToushiPlayerData', { '_id':'hashed'} )),
printjson(sh.shardCollection('$serverid'+'.guildBuilding', { 'guildId':'hashed'} )),
printjson(sh.shardCollection('$serverid'+'.worldResourceEntity', { '_id':'hashed'} )),
printjson(sh.shardCollection('$serverid'+'.orderForm', { 'sn':'hashed'} )),
printjson(sh.shardCollection('$serverid'+'.personMsgEntity', { 'receiveId':'hashed'} )),
printjson(sh.shardCollection('$serverid'+'.msgPushSetting', { '_id':'hashed'} )),
printjson(sh.shardCollection('$serverid'+'.wulueBagContainer', { '_id':'hashed'} )),
printjson(sh.shardCollection('$serverid'+'.arenaMemberContainer', { '_id':'hashed'} )),
printjson(sh.shardCollection('$serverid'+'.arenaRecordContainer', { '_id':'hashed'} ));"
#创建索引
./mongo 127.0.0.1:27017/$serverid --eval "
printjson(db.accountInfo.createIndex({'account_id': 1}, {background: true})),
printjson(db.worldResourceEntity.createIndex({'x':1, 'y':1}, {background: true})),
printjson(db.orderForm.createIndex({'sn':1}, {background: true})),
printjson(db.guildMsgEntity.createIndex({'receiveIdList': 1}, {background: true})),
printjson(db.guild.createIndex({'countryId': 1}, {background: true})),
printjson(db.personMsgEntity.createIndex({'receiveId':1}, {background: true})),
printjson(db.charPropsContainer.createIndex({'baseProps.props.values': 1}, {background: true})),
printjson(db.jifenContainer.createIndex({'jifens': 1}, {background: true})),
printjson(db.wujiang.createIndex({'playerId': 1, background: true}));"