<?php
include 'user/config.php';

if ($_POST) {
    $checknum = trim($_POST['checknum']);
    $quid = trim($_POST['qu']);
    $qu = $quarr[$quid];
    $uid = trim($_POST['uid']);
    $voucher_num = trim($_POST['voucher_num']);
    $url = $qu['url'];
    
    if ($checknum == $gmcode) {
        if ($quid >= 1) {
            if ($uid != '') {
                if ($voucher_num > 0) {
                    // 代金券ID
                    $voucher_id = 82006051;
                    
                    // 通过邮件发送代金券
                    $pot = 'sendMail.pf?name=' . urlencode($uid) . '&items=' . $voucher_id . ',' . $voucher_num . '&context=' . urlencode('GM发放了' . $voucher_num . '张充值代金券，请查收！');
                    
                    set_time_limit(0);
                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL, $url . $pot);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                    $output = curl_exec($ch);
                    curl_close($ch);
                    
                    if (strpos($output, 'success') !== false || strpos($output, '成功') !== false) {
                        exit('成功发放' . $voucher_num . '张代金券给玩家' . $uid);
                    } else {
                        exit('发放失败：' . $output);
                    }
                } else {
                    exit('代金券数量必须大于0');
                }
            } else {
                exit('角色名不能为空');
            }
        } else {
            exit('请选择区服');
        }
    } else {
        exit('GM校验码错误');
    }
} else {
    exit('请求方式错误');
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>发放代金券</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <script src="js/jquery-1.11.0.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script src="layer/layer.js"></script>
</head>
<body>
<div class="container">
    <br>
    <div class="row">
        <div class="container-fluid">
            <div class="modal-dialog">
                <div class="modal-content">
                    <ul class="breadcrumb">
                        <li><b>发放代金券</b></li>
                    </ul>
                    <div class="modal-body">
                        <div class="form-horizontal" role="form">
                            <div class="form-group">
                                <div class="col-sm-10">
                                    <input type="password" id="checknum" name="checknum" class="form-control" maxlength="16" value="" placeholder="输入GM校验码" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-10">
                                    <select id="qu" name="qu" class="form-control selectpicker" data-size="5" title="请选择区服" required>
                                        <?php
                                        include_once './user/config.php';
                                        foreach($quarr as $key=>$value){
                                            if($value['hidde']!=true){
                                                echo '<option value="'.$key.'">'.$value['name'].'</option>';
                                            }
                                        }
                                        ?>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-10">
                                    <input type="text" id="uid" name="uid" class="form-control" value="" placeholder="请输入角色名" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-10">
                                    <input type="number" id="voucher_num" name="voucher_num" class="form-control" value="" placeholder="代金券数量" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class=" col-sm-10">
                                    <button type="submit" class="btn btn-primary btn-block" onclick="giveVoucherBtn()">发放代金券</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
var checknum = '';
var qu = '';
var uid = '';

$('#checknum').blur(function(){
    checknum = $.trim($(this).val());
});

$('#qu').change(function(){
    qu = $.trim($(this).val());
});

$('#uid').blur(function(){
    uid = $.trim($(this).val());
});

function giveVoucherBtn() {
    if (checknum == '') {
        layer.msg('请输入GM校验码');
        return false;
    }
    if (uid == '') {
        layer.msg('角色名不能为空');
        return false;
    }
    
    var voucher_num = $('#voucher_num').val();
    if (voucher_num == '' || voucher_num <= 0) {
        layer.msg('请输入有效的代金券数量');
        return false;
    }
    
    $.ajaxSetup({
        contentType: "application/x-www-form-urlencoded; charset=utf-8"
    });
    $.post("give_voucher.php", {
        uid: uid,
        voucher_num: voucher_num,
        qu: qu,
        checknum: checknum
    },
    function(data) {
        layer.msg(data);
    });
}
</script>
</body>
</html>
