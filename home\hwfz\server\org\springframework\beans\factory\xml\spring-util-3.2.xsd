<?xml version="1.0" encoding="UTF-8" standalone="no"?>

<xsd:schema xmlns="http://www.springframework.org/schema/util"
		xmlns:xsd="http://www.w3.org/2001/XMLSchema"
		xmlns:beans="http://www.springframework.org/schema/beans"
		xmlns:tool="http://www.springframework.org/schema/tool"
		targetNamespace="http://www.springframework.org/schema/util"
		elementFormDefault="qualified"
		attributeFormDefault="unqualified">

	<xsd:import namespace="http://www.springframework.org/schema/beans" schemaLocation="http://www.springframework.org/schema/beans/spring-beans-3.2.xsd"/>
	<xsd:import namespace="http://www.springframework.org/schema/tool" schemaLocation="http://www.springframework.org/schema/tool/spring-tool-3.2.xsd"/>

	<xsd:element name="constant">
		<xsd:annotation>
			<xsd:documentation>
	Reference a public, static field on a type and expose its value as
	a bean. For example <code>&lt;util:constant static-field=&quot;java.lang.Integer.MAX_VALUE&quot;/&gt;</code>.
			</xsd:documentation>
		</xsd:annotation>
		<xsd:complexType>
			<xsd:attribute name="id" type="xsd:string"/>
			<xsd:attribute name="static-field" type="xsd:string" use="required"/>
		</xsd:complexType>
	</xsd:element>

	<xsd:element name="property-path">
		<xsd:annotation>
			<xsd:documentation>
	Reference a property on a bean (or as a nested value) and expose its values as
	a bean. For example &lt;util:property-path path=&quot;order.customer.name&quot;/&gt;.
			</xsd:documentation>
		</xsd:annotation>
		<xsd:complexType>
			<xsd:attribute name="id" type="xsd:string"/>
			<xsd:attribute name="path" type="xsd:string" use="required"/>
		</xsd:complexType>
	</xsd:element>

	<xsd:element name="list">
		<xsd:annotation>
			<xsd:documentation source="java:org.springframework.beans.factory.config.ListFactoryBean">
	Builds a List instance of the specified type, populated with the specified content.
			</xsd:documentation>
			<xsd:appinfo>
				<tool:annotation>
					<tool:exports type="java.util.List"/>
				</tool:annotation>
			</xsd:appinfo>
		</xsd:annotation>
		<xsd:complexType>
			<xsd:complexContent>
				<xsd:extension base="beans:listOrSetType">
					<xsd:attribute name="id" type="xsd:string"/>
					<xsd:attribute name="list-class" type="xsd:string">
						<xsd:annotation>
							<xsd:appinfo>
								<tool:annotation>
									<tool:expected-type type="java.lang.Class"/>
									<tool:assignable-to type="java.util.List"/>
								</tool:annotation>
							</xsd:appinfo>
						</xsd:annotation>
					</xsd:attribute>
					<xsd:attribute name="scope" type="xsd:string">
						<xsd:annotation>
							<xsd:documentation><![CDATA[
	The scope of this collection bean: typically "singleton" (one shared instance,
	which will be returned by all calls to getBean with the given id), or
	"prototype" (independent instance resulting from each call to getBean).
	Default is "singleton". Further scopes, such as "request" or "session",
	might be supported by extended bean factories (e.g. in a web environment).
							]]></xsd:documentation>
						</xsd:annotation>
					</xsd:attribute>
				</xsd:extension>
			</xsd:complexContent>
		</xsd:complexType>
	</xsd:element>

	<xsd:element name="set">
		<xsd:annotation>
			<xsd:documentation source="java:org.springframework.beans.factory.config.SetFactoryBean">
	Builds a Set instance of the specified type, populated with the specified content.
			</xsd:documentation>
			<xsd:appinfo>
				<tool:annotation>
					<tool:exports type="java.util.Set"/>
				</tool:annotation>
			</xsd:appinfo>
		</xsd:annotation>
		<xsd:complexType>
			<xsd:complexContent>
				<xsd:extension base="beans:listOrSetType">
					<xsd:attribute name="id" type="xsd:string"/>
					<xsd:attribute name="set-class" type="xsd:string">
						<xsd:annotation>
							<xsd:appinfo>
								<tool:annotation>
									<tool:expected-type type="java.lang.Class"/>
									<tool:assignable-to type="java.util.Set"/>
								</tool:annotation>
							</xsd:appinfo>
						</xsd:annotation>
					</xsd:attribute>
					<xsd:attribute name="scope" type="xsd:string">
						<xsd:annotation>
							<xsd:documentation><![CDATA[
	The scope of this collection bean: typically "singleton" (one shared instance,
	which will be returned by all calls to getBean with the given id), or
	"prototype" (independent instance resulting from each call to getBean).
	Default is "singleton". Further scopes, such as "request" or "session",
	might be supported by extended bean factories (e.g. in a web environment).
							]]></xsd:documentation>
						</xsd:annotation>
					</xsd:attribute>
				</xsd:extension>
			</xsd:complexContent>
		</xsd:complexType>
	</xsd:element>

	<xsd:element name="map">
		<xsd:annotation>
			<xsd:documentation source="java:org.springframework.beans.factory.config.MapFactoryBean">
	Builds a Map instance of the specified type, populated with the specified content.
			</xsd:documentation>
			<xsd:appinfo>
				<tool:annotation>
					<tool:exports type="java.util.Map"/>
				</tool:annotation>
			</xsd:appinfo>
		</xsd:annotation>
		<xsd:complexType>
			<xsd:complexContent>
				<xsd:extension base="beans:mapType">
					<xsd:attribute name="id" type="xsd:string"/>
					<xsd:attribute name="map-class" type="xsd:string">
						<xsd:annotation>
							<xsd:appinfo>
								<tool:annotation>
									<tool:expected-type type="java.lang.Class"/>
									<tool:assignable-to type="java.util.Map"/>
								</tool:annotation>
							</xsd:appinfo>
						</xsd:annotation>
					</xsd:attribute>
					<xsd:attribute name="scope" type="xsd:string">
						<xsd:annotation>
							<xsd:documentation><![CDATA[
	The scope of this collection bean: typically "singleton" (one shared instance,
	which will be returned by all calls to getBean with the given id), or
	"prototype" (independent instance resulting from each call to getBean).
	Default is "singleton". Further scopes, such as "request" or "session",
	might be supported by extended bean factories (e.g. in a web environment).
							]]></xsd:documentation>
						</xsd:annotation>
					</xsd:attribute>
				</xsd:extension>
			</xsd:complexContent>
		</xsd:complexType>
	</xsd:element>

	<xsd:element name="properties">
		<xsd:annotation>
			<xsd:documentation source="java:org.springframework.beans.factory.config.PropertiesFactoryBean">
	Loads a Properties instance from the resource location specified by the '<code>location</code>' attribute.
			</xsd:documentation>
			<xsd:appinfo>
				<tool:annotation>
					<tool:exports type="java.util.Properties"/>
				</tool:annotation>
			</xsd:appinfo>
		</xsd:annotation>
		<xsd:complexType>
			<xsd:complexContent>
				<xsd:extension base="beans:propsType">
					<xsd:attribute name="id" type="xsd:string"/>
					<xsd:attribute name="location" type="xsd:string">
						<xsd:annotation>
							<xsd:appinfo>
								<tool:annotation>
									<tool:expected-type type="org.springframework.core.io.Resource"/>
								</tool:annotation>
							</xsd:appinfo>
						</xsd:annotation>
					</xsd:attribute>
					<xsd:attribute name="local-override" type="xsd:boolean">
						<xsd:annotation>
							<xsd:documentation><![CDATA[
	Specifies whether local properties override properties from files.
	Default is "false": properties from files override local defaults.
	If set to "true", local properties will override defaults from files.
							]]></xsd:documentation>
						</xsd:annotation>
					</xsd:attribute>
					<xsd:attribute name="scope" type="xsd:string">
						<xsd:annotation>
							<xsd:documentation><![CDATA[
	The scope of this collection bean: typically "singleton" (one shared instance,
	which will be returned by all calls to getBean with the given id), or
	"prototype" (independent instance resulting from each call to getBean).
	Default is "singleton". Further scopes, such as "request" or "session",
	might be supported by extended bean factories (e.g. in a web environment).
							]]></xsd:documentation>
						</xsd:annotation>
					</xsd:attribute>
				</xsd:extension>
			</xsd:complexContent>
		</xsd:complexType>
	</xsd:element>

</xsd:schema>
