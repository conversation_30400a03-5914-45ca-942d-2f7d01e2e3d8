Spring Data JPA Changelog
=========================

Changes in version 1.10.4.RELEASE (2016-09-29)
----------------------------------------------
* DATAJPA-974 - Projections referring to collection attributes don't create a proper selection (missing joins).
* DATAJPA-970 - Ensure JDK 6 compatibility of regular expression used in QueryUtils.
* DATAJPA-966 - Release 1.10.4 (Hopper SR4).


Changes in version 1.9.6.RELEASE (2016-09-29)
---------------------------------------------
* DATAJPA-975 - Release 1.9.6 (Gosling SR6).
* DATAJPA-970 - Ensure JDK 6 compatibility of regular expression used in QueryUtils.


Changes in version 1.9.5.RELEASE (2016-09-20)
---------------------------------------------
* DATAJPA-965 - Mapping Sort instances to ORDER BY expressions should be restricted to fields for manually defined queries.
* DATAJPA-964 - Release 1.9.5 (Gosling SR5).
* DATAJPA-960 - Order by clause not created correctly if a manually defined query is not using an alias.
* DATAJPA-953 - Fix typo in reference documentation.
* DATAJPA-908 - Add note about when @Param is needed for named parameters.
* DATAJPA-904 - Potential NullPointerException in JpaPersistentPropertyImpl.isEntity().
* DATAJPA-903 - Stream<Object[]> missing result attributes on Hibernate.
* DATAJPA-891 - Avoid exceptions being thrown and caught immediately in JpaPersistentPropertyImpl.isEntity().
* DATAJPA-888 - Clarify usage of native queries with Pageable.
* DATAJPA-413 - Nested Id classes fail to populate in JpaMetamodelEntityInformation.


Changes in version 1.10.3.RELEASE (2016-09-20)
----------------------------------------------
* DATAJPA-965 - Mapping Sort instances to ORDER BY expressions should be restricted to fields for manually defined queries.
* DATAJPA-960 - Order by clause not created correctly if a manually defined query is not using an alias.
* DATAJPA-956 - Error creating DefaultJpaContext bean with a non-EntityManagerFactory JndiObjectFactoryBean defined via JavaConfig.
* DATAJPA-953 - Fix typo in reference documentation.
* DATAJPA-951 - Projections not handled correctly when Optional is used as wrapping return type.
* DATAJPA-950 - Upgrade Hibernate 5 build profile to 5.2.2.
* DATAJPA-938 - Complex select clauses in manually declared query using constructor expressions fail.
* DATAJPA-937 - Broken placeholder in exception message to be created in QueryByExamplePredicateBuilder.getPredicate(…).
* DATAJPA-929 - NullPointerException in AbstractStringBasedJpaQuery.isJpaManaged().
* DATAJPA-916 - Release 1.10.3 (Hopper SR3).
* DATAJPA-413 - Nested Id classes fail to populate in JpaMetamodelEntityInformation.


Changes in version 1.11.0.M1 (2016-07-27)
-----------------------------------------
* DATAJPA-937 - Broken placeholder in exception message to be created in QueryByExamplePredicateBuilder.getPredicate(…).
* DATAJPA-932 - Integrate version badge from spring.io.
* DATAJPA-923 - Add support for query by example using OR operator instead of AND.
* DATAJPA-915 - Update Hibernate build profiles.
* DATAJPA-912 - Optimize out the count query for paging when the data query returns less than a full page with a 0 offset.
* DATAJPA-909 - Count query breaks if pagination is used with projections.
* DATAJPA-908 - Add note about when @Param is needed for named parameters.
* DATAJPA-904 - Potential NullPointerException in JpaPersistentPropertyImpl.isEntity().
* DATAJPA-903 - Stream<Object[]> missing result attributes on Hibernate.
* DATAJPA-893 - Update Spring Data JPA version in Github readme.
* DATAJPA-891 - Avoid exceptions being thrown and caught immediately in JpaPersistentPropertyImpl.isEntity().
* DATAJPA-888 - Clarify usage of native queries with Pageable.
* DATAJPA-886 - NullPointerException for repository query methods using DTOs.
* DATAJPA-885 - No aliases found in result tuple.
* DATAJPA-883 - Auditing broken in Hopper GA.
* DATAJPA-882 - Release 1.11 M1 (Ingalls).
* DATAJPA-413 - Nested Id classes fail to populate in JpaMetamodelEntityInformation.


Changes in version 1.10.2.RELEASE (2016-06-15)
----------------------------------------------
* DATAJPA-909 - Count query breaks if pagination is used with projections.
* DATAJPA-908 - Add note about when @Param is needed for named parameters.
* DATAJPA-904 - Potential NullPointerException in JpaPersistentPropertyImpl.isEntity().
* DATAJPA-903 - Stream<Object[]> missing result attributes on Hibernate.
* DATAJPA-891 - Avoid exceptions being thrown and caught immediately in JpaPersistentPropertyImpl.isEntity().
* DATAJPA-888 - Clarify usage of native queries with Pageable.
* DATAJPA-886 - NullPointerException for repository query methods using DTOs.
* DATAJPA-885 - No aliases found in result tuple.
* DATAJPA-884 - Release 1.10.2 (Hopper SR2).


Changes in version 1.10.1.RELEASE (2016-04-06)
----------------------------------------------
* DATAJPA-883 - Auditing broken in Hopper GA.
* DATAJPA-881 - Release 1.10.1 (Hopper SR1).


Changes in version 1.10.0.RELEASE (2016-04-06)
----------------------------------------------
* DATAJPA-880 - Add pull request template.
* DATAJPA-878 - Release 1.10 GA (Hopper).
* DATAJPA-876 - Update documentation for Spring Data JPA 1.10.
* DATAJPA-838 - Add section on ad-hoc @EntityGraphs to reference documentation.


Changes in version 1.10.0.RC1 (2016-03-18)
------------------------------------------
* DATAJPA-871 - Allow usage of composed annotations using @AliasFor.
* DATAJPA-869 - Release 1.10 RC1 (Hopper).
* DATAJPA-867 - Upgrade persistence provider dependencies to latest version.
* DATAJPA-864 - Query execution fails for manually defined queries containing a constructor expression.
* DATAJPA-862 - Reference documentation contains wrong property index "?0" in @Query example.
* DATAJPA-813 - Error creating DefaultJpaContext bean in test cases.
* DATAJPA-218 - Support for Query by Example.


Changes in version 1.9.4.RELEASE (2016-02-23)
---------------------------------------------
* DATAJPA-862 - Reference documentation contains wrong property index "?0" in @Query example.
* DATAJPA-860 - Release 1.9.4 (Gosling SR4).
* DATAJPA-859 - Add build profile for Hibernate 5.1.
* DATAJPA-858 - Contains binding inspects first property for collections, not the leaf property.
* DATAJPA-850 - Reference documentation should mention @EntityListener to set up auditing.
* DATAJPA-848 - AbstractPersistable.equals(…) always returns false if target entity is a proxy.
* DATAJPA-847 - Upgrade to EclipseLink 2.6.2.
* DATAJPA-845 - PersistenceProvider static from* methods are a performance hit.


Changes in version 1.10.0.M1 (2016-02-12)
-----------------------------------------
* DATAJPA-859 - Add build profile for Hibernate 5.1.
* DATAJPA-858 - Contains binding inspects first property for collections, not the leaf property.
* DATAJPA-854 - Add code of conduct.
* DATAJPA-852 - Release 1.10 M1 (Hopper).
* DATAJPA-850 - Reference documentation should mention @EntityListener to set up auditing.
* DATAJPA-849 - Sample in README broken.
* DATAJPA-848 - AbstractPersistable.equals(…) always returns false if target entity is a proxy.
* DATAJPA-847 - Upgrade to EclipseLink 2.6.2.
* DATAJPA-845 - PersistenceProvider static from* methods are a performance hit.
* DATAJPA-839 - EntityGraph, specified for repository method, that was called first, applies to all other methods of the same repository.
* DATAJPA-834 - Use a less common bean name for EntityManagerBeanDefinitionRegistrarPostProcessor to avoid collisions.
* DATAJPA-833 - Improve log message in ClasspathScanningPersistenceUnitPostProcessor.
* DATAJPA-831 - Upgrade Hibernate build profile to 5.0.5.
* DATAJPA-830 - NotContaining doesn't work for String properties on query methods.
* DATAJPA-829 - Contains expression on a collection property should result in member of predicate.
* DATAJPA-826 - Add Jacoco agent to test executions explicitly.
* DATAJPA-821 - Upgrade to OpenJPA 2.4.0.
* DATAJPA-820 - New entity merged rather than persisted when ID is not generated and version is in a MappedSuperClass.
* DATAJPA-819 - CrudMethodMetadataPostProcessor does not use bean class loader.
* DATAJPA-817 - Upgrade to EclipseLink 2.6.1.
* DATAJPA-815 - order by qualifyReference when property begins with joinAlias.
* DATAJPA-809 - ParameterBinder should use ParameterAccessor.
* DATAJPA-808 - Add AttributeConverter implementations for ZoneId.
* DATAJPA-805 - Typo in @Modifying documentation.
* DATAJPA-804 - Support projections on repository query methods.
* DATAJPA-798 - Repository query method fails when sorting with Pageable and query with line breaks.
* DATAJPA-796 - Typo in JavaDoc of AbstractPersistable.
* DATAJPA-795 - Upgrade Hibernate 5 build profile to 5.0.1.
* DATAJPA-793 - Spring Boot has broken custom repository methods in Spring Data JPA.
* DATAJPA-765 - Upgrade to Querydsl 4.
* DATAJPA-742 - Java 8 Stream support broken.
* DATAJPA-585 - Incomplete null handling in QueryDslJpaRepository.
* DATAJPA-382 - Provide access to auditor in manually defined queries.


Changes in version 1.9.2.RELEASE (2015-12-18)
---------------------------------------------
* DATAJPA-839 - Thread-bound lookup of CrudMethodMetadata broken.
* DATAJPA-837 - Release 1.9.2 (Gosling).
* DATAJPA-834 - Use a less common bean name for EntityManagerBeanDefinitionRegistrarPostProcessor to avoid collisions.
* DATAJPA-833 - Improve log message in ClasspathScanningPersistenceUnitPostProcessor.
* DATAJPA-831 - Upgrade Hibernate build profile to 5.0.5.
* DATAJPA-830 - NotContaining doesn't work for String properties on query methods.
* DATAJPA-829 - Contains expression on a collection property should result in member of predicate.


Changes in version 1.9.1.RELEASE (2015-11-15)
---------------------------------------------
* DATAJPA-823 - Release 1.9.1 (Gosling).
* DATAJPA-820 - New entity merged rather than persisted when ID is not generated and version is in a MappedSuperClass.
* DATAJPA-819 - CrudMethodMetadataPostProcessor does not use bean class loader.
* DATAJPA-817 - Upgrade to EclipseLink 2.6.1.
* DATAJPA-815 - order by qualifyReference when property begins with joinAlias.
* DATAJPA-805 - Typo in @Modifying documentation.
* DATAJPA-798 - Repository query method fails when sorting with Pageable and query with line breaks.
* DATAJPA-796 - Typo in JavaDoc of AbstractPersistable.
* DATAJPA-795 - Upgrade Hibernate 5 build profile to 5.0.1.
* DATAJPA-793 - Spring Boot has broken custom repository methods in Spring Data JPA.
* DATAJPA-742 - Java 8 Stream support broken.
* DATAJPA-585 - Incomplete null handling in QueryDslJpaRepository.
* DATAJPA-382 - Provide access to auditor in manually defined queries.


Changes in version 1.7.4.RELEASE (2015-10-14)
---------------------------------------------
* DATAJPA-812 - Release 1.7.4 (Evans).


Changes in version 1.9.0.RELEASE (2015-09-01)
---------------------------------------------
* DATAJPA-787 - Release 1.9 GA (Gosling).
* DATAJPA-785 - Make sure the build works with Hibernate 5 GA.
* DATAJPA-779 - Update reference documentation to mention newly introduced JpaContext.
* DATAJPA-775 - Fail fast when entity uses Spring Data Commons @Version instead of JPA @Version.
* DATAJPA-773 - Remove explicit registration of ExposeInvocationInterceptor from CrudMethodMetadataPostProcessor.
* DATAJPA-772 - ParameterMetadataProvider should apply LIKE-expansion only on String properties.


Changes in version 1.9.0.RC1 (2015-08-04)
-----------------------------------------
* DATAJPA-768 - Release 1.9 RC1 (Gosling).
* DATAJPA-763 - Executing a Specification with fetch-joins leads to wrong HQL query.
* DATAJPA-759 - Log names of Class and Mapping Files found during scan to DEBUG level in ClasspathScanningPersistenceUnitPostProcessor.
* DATAJPA-758 - Using Java 8 named parameter prevents positional query parameter binding.
* DATAJPA-743 - Typo in README.
* DATAJPA-736 - Count query creation fails when entity names contain non-ASCII characters.
* DATAJPA-728 - PageImpl : wrong total count.
* DATAJPA-669 - Provide a way to get the injected Entity Manager [Factory] in the custom implementations.


Changes in version 1.8.2.RELEASE (2015-07-28)
---------------------------------------------
* DATAJPA-766 - Release 1.8.2 (Fowler).
* DATAJPA-763 - Executing a Specification with fetch-joins leads to wrong HQL query.
* DATAJPA-759 - Log names of Class and Mapping Files found during scan to DEBUG level in ClasspathScanningPersistenceUnitPostProcessor.
* DATAJPA-758 - Using Java 8 named parameter prevents positional query parameter binding.


Changes in version 1.6.6.RELEASE (2015-07-01)
---------------------------------------------
* DATAJPA-750 - Release 1.6.6 (Dijkstra).
* DATAJPA-743 - Typo in README.
* DATAJPA-736 - Count query creation fails when entity names contain non-ASCII characters.
* DATAJPA-728 - PageImpl : wrong total count.
* DATAJPA-726 - Pageable Always Adding From Entity to Sort Field Name.
* DATAJPA-721 - Enable Slack notifications for Travis build.
* DATAJPA-720 - Remove relative reference to parent POM to make sure the right Spring version is picked up.
* DATAJPA-715 - Fix typo in @EnableJpaRepositories' JavaDoc.
* DATAJPA-702 - Add convenience methods to ease building up a more complex JpaSort.
* DATAJPA-699 - Upgrade to EclipseLink 2.5.2.
* DATAJPA-681 - Execution of derived stored procedures fails if named parameters are used.
* DATAJPA-672 - Activate Spring 4.2 build profile for travis.
* DATAJPA-656 - Count query with group by returns wrong result.
* DATAJPA-633 - Target entity type not considered for associations.


Changes in version 1.7.3.RELEASE (2015-07-01)
---------------------------------------------
* DATAJPA-751 - Release 1.7.3 (Evans).
* DATAJPA-743 - Typo in README.
* DATAJPA-741 - Back-port DATAJPA-608 to Evans.
* DATAJPA-736 - Count query creation fails when entity names contain non-ASCII characters.
* DATAJPA-728 - PageImpl : wrong total count.
* DATAJPA-726 - Pageable Always Adding From Entity to Sort Field Name.
* DATAJPA-721 - Enable Slack notifications for Travis build.
* DATAJPA-720 - Remove relative reference to parent POM to make sure the right Spring version is picked up.
* DATAJPA-715 - Fix typo in @EnableJpaRepositories' JavaDoc.
* DATAJPA-714 - Include new section on Spring Data and Spring Framework dependencies in reference documentation.
* DATAJPA-712 - Bug in binding in clause parameters with SPEL.
* DATAJPA-703 - Assert Hibernate 5 compatibilty.
* DATAJPA-702 - Add convenience methods to ease building up a more complex JpaSort.
* DATAJPA-699 - Upgrade to EclipseLink 2.5.2.
* DATAJPA-689 - Allow @EntityGraph on findOne method of CrudRepository.
* DATAJPA-681 - Execution of derived stored procedures fails if named parameters are used.
* DATAJPA-672 - Activate Spring 4.2 build profile for travis.
* DATAJPA-656 - Count query with group by returns wrong result.
* DATAJPA-633 - Target entity type not considered for associations.


Changes in version 1.8.1.RELEASE (2015-06-30)
---------------------------------------------
* DATAJPA-752 - Release 1.8.1 (Fowler).
* DATAJPA-743 - Typo in README.
* DATAJPA-736 - Count query creation fails when entity names contain non-ASCII characters.
* DATAJPA-728 - PageImpl : wrong total count.
* DATAJPA-726 - Pageable Always Adding From Entity to Sort Field Name.
* DATAJPA-721 - Enable Slack notifications for Travis build.
* DATAJPA-720 - Remove relative reference to parent POM to make sure the right Spring version is picked up.
* DATAJPA-716 - JpaPersistentProperty should consider updatable flag of mapping annotations.
* DATAJPA-715 - Fix typo in @EnableJpaRepositories' JavaDoc.
* DATAJPA-714 - Include new section on Spring Data and Spring Framework dependencies in reference documentation.
* DATAJPA-712 - Bug in binding in clause parameters with SPEL.
* DATAJPA-703 - Assert Hibernate 5 compatibilty.
* DATAJPA-702 - Add convenience methods to ease building up a more complex JpaSort.
* DATAJPA-699 - Upgrade to EclipseLink 2.5.2.
* DATAJPA-695 - Package reference in javadoc wrong in Jsr310JpaConverters, ThreeTenBackPortJpaConverters.


Changes in version 1.9.0.M1 (2015-06-02)
----------------------------------------
* DATAJPA-731 - Donwgrade to Querydsl 3.6.3.
* DATAJPA-730 - Release 1.9 M1 (Gosling).
* DATAJPA-726 - Pageable Always Adding From Entity to Sort Field Name.
* DATAJPA-721 - Enable Slack notifications for Travis build.
* DATAJPA-720 - Remove relative reference to parent POM to make sure the right Spring version is picked up.
* DATAJPA-716 - JpaPersistentProperty should consider updatable flag of mapping annotations.
* DATAJPA-715 - Fix typo in @EnableJpaRepositories' JavaDoc.
* DATAJPA-714 - Include new section on Spring Data and Spring Framework dependencies in reference documentation.
* DATAJPA-712 - Bug in binding in clause parameters with SPEL.
* DATAJPA-710 - Adapt API changes in Spring Data Commons to simplify custom repository base class registration.
* DATAJPA-703 - Assert Hibernate 5 compatibilty.
* DATAJPA-702 - Add convenience methods to ease building up a more complex JpaSort.
* DATAJPA-698 - Upgrade to EclipseLink 2.6.0.
* DATAJPA-696 - Support ad-hoc fetch graph configurations on repository methods.
* DATAJPA-695 - Package reference in javadoc wrong in Jsr310JpaConverters, ThreeTenBackPortJpaConverters.
* DATAJPA-674 - Problems following the documentation for adding custom behaviour to all repositories.


Changes in version 1.8.0.RELEASE (2015-03-23)
---------------------------------------------
* DATAJPA-692 - Release 1.8 GA.
* DATAJPA-689 - Allow @EntityGraph on findOne method of CrudRepository.
* DATAJPA-685 - Allow to disable default transaction handling in @EnableJpaRepositories and XML namespace.


Changes in version 1.8.0.RC1 (2015-03-05)
-----------------------------------------
* DATAJPA-686 - Release 1.8 RC1.
* DATAJPA-681 - Execution of derived stored procedures fails if named parameters are used.
* DATAJPA-679 - Add QueryDslPredicateExecutor.findAll(Predicate, Sort) method.
* DATAJPA-677 - Add support for Java 8 Stream in repository finder methods.
* DATAJPA-672 - Activate Spring 4.2 build profile for travis.
* DATAJPA-665 - Add 'exists' method to QueryDslJpaRepository which accepts a querydsl Predicate.
* DATAJPA-664 - JpaPersistentProperty.getActualType() should consider specialized association type.
* DATAJPA-656 - Count query with group by returns wrong result.
* DATAJPA-655 - Add AttributeConverters for ThreeTen back port library.
* DATAJPA-654 - Enable Spring 4.1 build profile for Travis.
* DATAJPA-653 - Make sure test work with Spring 4.1.
* DATAJPA-652 - Provide support for ParameterMode.REF_CURSOR.
* DATAJPA-651 - Re-enable querydsl-next build profile for Travis.
* DATAJPA-650 - Add AttributeConverters for non-timezoned JSR-310 types.
* DATAJPA-641 - Move to new Travis build infrastructure.
* DATAJPA-546 - Provide a way to customize the domain type managed by a repository.


Changes in version 1.7.2.RELEASE (2015-01-28)
---------------------------------------------
* DATAJPA-661 - Release 1.7.2.
* DATAJPA-654 - Enable Spring 4.1 build profile for Travis.
* DATAJPA-653 - Make sure test work with Spring 4.1.
* DATAJPA-641 - Move to new Travis build infrastructure.
* DATAJPA-639 - Set up Travis to only build with JDK 8.
* DATAJPA-638 - Improve bean definition setup for root configuration element.
* DATAJPA-634 - Switch to HTTPS for repository declarations.
* DATAJPA-632 - Improve SimpleJpaQueryUnitTests that fails after SonarQube improvements in Spring Data Commons.
* DATAJPA-631 - ClasspathScanningPersistenceUnitPostProcessor should forward ResourceLoader configured to the ClassPathScanningCandidateComponentProvider used for the entity type scanning.
* DATAJPA-629 - SPEL expression does not work with #{#entityName} in @Query.
* DATAJPA-628 - Use a shared SpelParser instead of recreating it for queries.
* DATAJPA-627 - Typo in BeanDefinitionNames.
* DATAJPA-624 - Fix SonarQube warnings as far as possible/reasonable.
* DATAJPA-622 - Hibernate thinks AbstractPersistable.isNew() is a property.
* DATAJPA-611 - SimpleJpaRepository.findAll(Iterable<ID ids) not working when for composite primary keys.
* DATAJPA-606 - SQLGrammarException for IN-clause with empty collection as parameter.
* DATAJPA-523 - Specifications should implement Serializable.
* DATAJPA-477 - Avoid query creation in PagedExecution if count query reports no results.


Changes in version 1.6.5.RELEASE (2015-01-27)
---------------------------------------------
* DATAJPA-660 - Release 1.6.5.
* DATAJPA-654 - Enable Spring 4.1 build profile for Travis.
* DATAJPA-653 - Make sure test work with Spring 4.1.
* DATAJPA-641 - Move to new Travis build infrastructure.
* DATAJPA-639 - Set up Travis to only build with JDK 8.
* DATAJPA-634 - Switch to HTTPS for repository declarations.
* DATAJPA-632 - Improve SimpleJpaQueryUnitTests that fails after SonarQube improvements in Spring Data Commons.
* DATAJPA-631 - ClasspathScanningPersistenceUnitPostProcessor should forward ResourceLoader configured to the ClassPathScanningCandidateComponentProvider used for the entity type scanning.
* DATAJPA-627 - Typo in BeanDefinitionNames.
* DATAJPA-622 - Hibernate thinks AbstractPersistable.isNew() is a property.
* DATAJPA-620 - Infinite loop in unsynchronized HashMap in CrudMethodMetadataPostProcessor.
* DATAJPA-611 - SimpleJpaRepository.findAll(Iterable<ID ids) not working when for composite primary keys.
* DATAJPA-608 - JpaPersistentEntityImpl.isEntity() should consider actual type.
* DATAJPA-603 - Update Travis build profiles to build against Spring 4.0 and 4.1 snapshots.
* DATAJPA-588 - Regression in Spring Data Evans metamodel processing with property based JPA annotations.
* DATAJPA-523 - Specifications should implement Serializable.
* DATAJPA-477 - Avoid query creation in PagedExecution if count query reports no results.


Changes in version 1.8.0.M1 (2014-12-01)
----------------------------------------
* DATAJPA-638 - Improve bean definition setup for root configuration element.
* DATAJPA-636 - Release 1.8 M1.
* DATAJPA-635 - Add implementation for new QueryDslPredicateExecutor.findAll(OrderSpecifier<?>... orders).
* DATAJPA-634 - Switch to HTTPS for repository declarations.
* DATAJPA-632 - Improve SimpleJpaQueryUnitTests that fails after SonarQube improvements in Spring Data Commons.
* DATAJPA-631 - ClasspathScanningPersistenceUnitPostProcessor should forward ResourceLoader configured to the ClassPathScanningCandidateComponentProvider used for the entity type scanning.
* DATAJPA-630 - Identifier lookup fails for JPA proxies.
* DATAJPA-629 - SPEL expression does not work with #{#entityName} in @Query.
* DATAJPA-628 - Use a shared SpelParser instead of recreating it for queries.
* DATAJPA-627 - Typo in BeanDefinitionNames.
* DATAJPA-624 - Fix SonarQube warnings as far as possible/reasonable.
* DATAJPA-623 - Remove package cycles accidentally introduced in 1.6.
* DATAJPA-622 - Hibernate thinks AbstractPersistable.isNew() is a property.
* DATAJPA-620 - Infinite loop in unsynchronized HashMap in CrudMethodMetadataPostProcessor.
* DATAJPA-619 - JpaPersistentPropertyImpl should consider JPA access type settings.
* DATAJPA-617 - Named query lookups might cause transaction to be rolled back.
* DATAJPA-615 - The top and first query keyword to statically limit results are missing in documentation.
* DATAJPA-612 - Allow @EntityGraph on a method inherited from JpaRepository.
* DATAJPA-611 - SimpleJpaRepository.findAll(Iterable<ID ids) not working when for composite primary keys.
* DATAJPA-608 - JpaPersistentEntityImpl.isEntity() should consider actual type.
* DATAJPA-606 - SQLGrammarException for IN-clause with empty collection as parameter.
* DATAJPA-605 - JpaPersistentPropertyImpl does not override method isVersionProperty.
* DATAJPA-523 - Specifications should implement Serializable.
* DATAJPA-477 - Avoid query creation in PagedExecution if count query reports no results.


Changes in version 1.7.1.RELEASE (2014-10-30)
---------------------------------------------
* DATAJPA-621 - Release 1.7.1.
* DATAJPA-620 - Infinite loop in unsynchronized HashMap in CrudMethodMetadataPostProcessor.
* DATAJPA-617 - Named query lookups might cause transaction to be rolled back.
* DATAJPA-615 - The top and first query keyword to statically limit results are missing in documentation.
* DATAJPA-612 - Allow @EntityGraph on a method inherited from JpaRepository.


Changes in version 1.7.0.RELEASE (2014-09-05)
---------------------------------------------
* DATAJPA-604 - Release 1.7 GA.
* DATAJPA-603 - Update Travis build profiles to build against Spring 4.0 and 4.1 snapshots.
* DATAJPA-602 - Upgrade to AspectJ Maven Plugin 1.6.
* DATAJPA-597 - Saving detatched entity breaks when moving from Spring Data JPA 1.5.3 to 1.6.2.
* DATAJPA-596 - Allow sorting by querydsl operator expression.
* DATAJPA-594 - Polish Asciidoctor rerefernce documentation.
* DATAJPA-593 - Custom repository implementations are not picked up when using CDI.


Changes in version 1.6.4.RELEASE (2014-08-27)
---------------------------------------------
* DATAJPA-597 - Saving detatched entity breaks when moving from Spring Data JPA 1.5.3 to 1.6.2.
* DATAJPA-596 - Allow sorting by querydsl operator expression.
* DATAJPA-595 - Release 1.6.4.
* DATAJPA-592 - Auditing annotations not working when applied to methods.
* DATAJPA-590 - @Version implementation changes from 1.6.0 to 1.6.2 is a breaking change that doesn't make much sense.
* DATAJPA-582 - JpaMetamodelEntityInformation.isNew(…) throws ClassCastException for @Version of type Calendar.
* DATAJPA-581 - Can no longer call save() with a @Version'ed detached entity in 1.6.2.


Changes in version 1.7.0.RC1 (2014-08-13)
-----------------------------------------
* DATAJPA-587 - Release 1.7 RC1.
* DATAJPA-584 - Add support for custom implementations in CDI.
* DATAJPA-582 - JpaMetamodelEntityInformation.isNew(…) throws ClassCastException for @Version of type Calendar.
* DATAJPA-581 - Can no longer call save() with a @Version'ed detached entity in 1.6.2.
* DATAJPA-580 - Move to Asciidoctor for reference documentation.
* DATAJPA-579 - Adapt to new multi-store configuration detection.
* DATAJPA-576 - Improve JpaMappingContext setup in configuration.
* DATAJPA-575 - Adapt to deprecation removals in Spring Data Commons.
* DATAJPA-574 - Support @QueryHints in implementation of QueryDslPredicateExecutor.
* DATAJPA-362 - Parameter used in % LIKE expression is changed for whole query.


Changes in version 1.6.2.RELEASE (2014-07-28)
---------------------------------------------
* DATAJPA-578 - Release 1.6.2.
* DATAJPA-572 - Remove links to forum.spring.io.
* DATAJPA-568 - Cannot update the new inserted entity which has @Version.
* DATAJPA-567 - OptimisticLock does not work anymore.
* DATAJPA-362 - Parameter used in % LIKE expression is changed for whole query.


Changes in version 1.7.0.M1 (2014-07-10)
----------------------------------------
* DATAJPA-572 - Remove links to forum.spring.io.
* DATAJPA-571 - Release 1.7 M1.
* DATAJPA-568 - Cannot update the new inserted entity which has @Version.
* DATAJPA-567 - OptimisticLock does not work anymore.
* DATAJPA-566 - Adapt to Spring 4 upgrade.
* DATAJPA-564 - Support for SpEL based parameter expressions in repository query methods.
* DATAJPA-561 - Entity with primitive version property invalidly considered not new.
* DATAJPA-558 - StackOverflow error when creating the MappingContext for domain types with generics.
* DATAJPA-551 - Add support for limiting the query result in the query derivation mechanism.
* DATAJPA-548 - Improve documentation on custom namespace attributes for jpa:repositories.
* DATAJPA-545 - Special characters not allowed anymore in named binding parameter.
* DATAJPA-527 - CrudRepository.exists(ID id) fails for entity with @IdClass.
* DATAJPA-519 - ClasspathScanningPersistenceUnitPostProcessor throws NPE.
* DATAJPA-506 - Add support for conversion of Blob values to byte[] in native queries.


Changes in version 1.6.1.RELEASE (2014-06-30)
---------------------------------------------
* DATAJPA-563 - Release 1.6.1.
* DATAJPA-561 - Entity with primitive version property invalidly considered not new.
* DATAJPA-558 - StackOverflow error when creating the MappingContext for domain types with generics.
* DATAJPA-554 - Avoid falling back to query derivation for invalid native queries.
* DATAJPA-548 - Improve documentation on custom namespace attributes for jpa:repositories.
* DATAJPA-545 - Special characters not allowed anymore in named binding parameter.
* DATAJPA-527 - CrudRepository.exists(ID id) fails for entity with @IdClass.
* DATAJPA-519 - ClasspathScanningPersistenceUnitPostProcessor throws NPE.


Changes in version 1.5.3.RELEASE (2014-06-18)
---------------------------------------------
* DATAJPA-557 - Release 1.5.3.
* DATAJPA-554 - Avoid falling back to query derivation for invalid native queries.
* DATAJPA-548 - Improve documentation on custom namespace attributes for jpa:repositories.
* DATAJPA-545 - Special characters not allowed anymore in named binding parameter.
* DATAJPA-527 - CrudRepository.exists(ID id) fails for entity with @IdClass.
* DATAJPA-516 - Missing/Misspelled NamedQuery gives dubious Exception.
* DATAJPA-510 - Regression in 1.5.1 - Sort by property of an associated object doesn't work. Join property and INNER vs OUTER join when sorting.


Changes in version 1.6.0.RELEASE (2014-05-20)
---------------------------------------------
* DATAJPA-544 - Add Travis build configuration to trigger matrix builds.
* DATAJPA-543 - Update reference documentation for JPA 2.1 features.
* DATAJPA-542 - Release 1.6 GA.
* DATAJPA-531 - Add link to Accessing JPA Data With REST from readme.md.
* DATAJPA-525 - JpaMetamodelMappingContext should guard against null Java type returned from ManagedType.
* DATAJPA-510 - Regression in 1.5.1 - Sort by property of an associated object doesn't work. Join property and INNER vs OUTER join when sorting.
* DATAJPA-456 - Add support for specifying only the projection part of a custom count query.


Changes in version 1.6.0.RC1 (2014-05-02)
-----------------------------------------
* DATAJPA-522 - Release 1.6 RC1.
* DATAJPA-517 - Defensively check for null for optional dependency to CrudMethodMetadata in SimpleJpaRepository.
* DATAJPA-516 - Missing/Misspelled NamedQuery gives dubious Exception.
* DATAJPA-513 - Improve error message for missing @Param annotations for query methods with named parameters.
* DATAJPA-509 - Entity name used in 'count' queries ignores the entity name set in ORM mapping file.
* DATAJPA-499 - Paginate with QueryDSL generate "nulls last" illegal syntax when using MSSQL.
* DATAJPA-479 - Oracle Function Type should be supported in Spring Data JPA.
* DATAJPA-455 - Add support for JPA 2.1 stored procedures.
* DATAJPA-438 - JpaRepository: add Optional<T> as alternative to null result.

Changes in version 1.5.2.RELEASE (2014-04-15)
---------------------------------------------
* Sorting for a field in a composite primary key throws "Can't cast to EntityPath". (DATAJPA-497)
* Sorting of Embeddables raises ClassCastException. (DATAJPA-500)
* OSGi version conflict regarding joda-time. (DATAJPA-502)
* Error on QueryDSL after upgrade (DATAJPA-504)
* Blob query result only returns first byte. (DATAJPA-505)
* Entity name used in 'count' queries ignores the entity name set in ORM mapping file. (DATAJPA-509)
* Performance tuning LockModePopulatingMethodIntercceptor.invoke(). (DATAJPA-507)
* Improve error message for missing @Param annotations for query methods with named parameters. (DATAJPA-513)
* Release 1.5.2. (DATAJPA-515)

Changes in version 1.6.0.M1 (2014-03-31)
----------------------------------------
* Can't use pagination (Pageable) with @IdClass entities (spring-data-jpa 1.4.3 & Hibernate 4.1.9). (DATAJPA-472)
* IllegalStateException caused by QueryUtils.toExpressionRecursively(…). (DATAJPA-476)
* Parameter binding detection for in clause broken for parameters surrounded with parentheses. (DATAJPA-483)
* findAll(Iterable<ID>) : Invalid comparation Exception thrown. (DATAJPA-492)
* Missing Imports in Spring Data JPA for Auditing. (DATAJPA-494)
* Query derivation doesn't create joins for in-clauses on element collections. (DATAJPA-496)
* Sorting for a field in a composite primary key throws "Can't cast to EntityPath". (DATAJPA-497)
* Sorting of Embeddables raises ClassCastException. (DATAJPA-500)
* OSGi version conflict regarding joda-time. (DATAJPA-502)
* Error on QueryDSL after upgrade. (DATAJPA-504)
* Blob query result only returns first byte. (DATAJPA-505)
* Support for @QueryHints on CRUD methods. (DATAJPA-173)
* Add support for returning subtypes of Repository Entity in JpaRepository.saveAndFlush. (DATAJPA-464)
* Postpone the construction of AuditorAware in AuditEntityListener. (DATAJPA-478)
* Repositories should expose a single MappingContext instance. (DATAJPA-484)
* Sort by property of an associated object generates left join only for the first level. (DATAJPA-491)
* Update auditing configuration to be able to use accessor annotations. (DATAJPA-501)
* Query creation for deleteBy / removeBy prefix. (DATAJPA-460)
* Add support for lazy loading configuration via JPA 2.1 fetch-/loadgraph. (DATAJPA-466)
* Add support for sliced execution. (DATAJPA-486)
* Upgrade Hibernate build profiles to latest version. (DATAJPA-485)
* Adapt to auditing configuration changes in Spring Data Commons. (DATAJPA-487)
* Adapt to API changes in RepositoryProxyPostProcessor. (DATAJPA-489)
* Upgrade to OpenJPA 2.3.0. (DATAJPA-493)
* Release 1.6 M1. (DATAJPA-481)

Changes in version 1.5.1.RELEASE (2014-03-13)
---------------------------------------------
* Verify that pagination works with entities with @IdClass. (DATAJPA-472)
* Mitigate spec violations in Hibernate for query creation. (DATAJPA-476)
* Fixed parameter binding detection with parentheses. (DATAJPA-483)
* Upgraded to Hibernate profiles to 4.3.4 and 4.2.10. (DATAJPA-485)
* Support order by arbitrarily nested association paths with Querydsl. (DATAJPA-491)
* Altered implementation of findAll(Iterable<ID>) to work around bug in OpenJPA. (DATAJPA-492)
* Fix template.mf to make sure auditing can be used on OSGi. (DATAJPA-494)
* Query derivation doesn't create joins for in-clauses on element collections. (DATAJPA-496)

Changes in version 1.4.5.RELEASE (2014-03-10)
---------------------------------------------
* When used "LIKE expression" and "other expression" at the same time, occurred the QuerySyntaxException of Hibernate. (DATAJPA-473)
* IllegalStateException caused by QueryUtils.toExpressionRecursively(…). (DATAJPA-476)
* findAll(Iterable<ID>) : Invalid comparation Exception thrown. (DATAJPA-492)
* Improve FAQ, C2.1 on the SessionFactory replacement. (DATAJPA-462)
* Documentation on CDI setup is incomplete. (DATAJPA-474)
* Upgrade Hibernate build profiles to latest version. (DATAJPA-485)
* Release 1.4.5. (DATAJPA-488)

Changes in version 1.5.0.RELEASE (2014-02-24)
-----------------------------------------

* Query on byte array fails (DATAJPA-454)
* Regression in parameter binding (DATAJPA-461)
* Nullpointer when using @Query (DATAJPA-465)
* Build fails against Spring 4 (DATAJPA-471)
* When used "LIKE expression" and "other expression" at the same time, occurred the QuerySyntaxException of Hibernate. (DATAJPA-473)
* Improve FAQ, C2.1 on the SessionFactory replacement (DATAJPA-462)
* Documentation on CDI setup is incomplete (DATAJPA-474)
* Add aop.xml to prevent Eclipse from weaving unnecessary aspects (DATAJPA-458)
* Reduce log output for Querydsl APT processor (DATAJPA-459)
* Release 1.5 GA (DATAJPA-469)
* Documentation overhaul (DATAJPA-470)

Changes in version 1.4.4.RELEASE (2014-02-17)
---------------------------------------------
* PersistenceProvider enum should also match org.hibernate.jpa.HibernateEntityManager (DATAJPA-444)
* Query on byte array fails (DATAJPA-454)
* Regression in parameter binding (DATAJPA-461)
* Upgrade Hibernate 4 build to latest versions (DATAJPA-443)
* Add contribution guidelines (DATAJPA-448)
* Release 1.4.4 (DATAJPA-463)

Changes in version 1.5.0.RC1 (2014-01-29)
-----------------------------------------
* Add Sort implementations that use JPA Metamodel API and/or QueryDsl API (DATAJPA-12)
* Enable support to inject EntityManager via constructor (DATAJPA-445)
* <jpa:repositories> needs explicitly given transaction manager even when there is only one tx manager (DATAJPA-410)
* COUNT syntax error when use scalar select (select a.b, a.c from …) with paging (DATAJPA-420)
* Sort by property of an associated object generates inner join instead of left join with QueryDsl and Hibernate (DATAJPA-427)
* Fix incompatibilities with latest Hibernate 4.3 (CR2 currently) (DATAJPA-430)
* Repository initialization causes transaction rollback in CDI environments (DATAJPA-442)
* PersistenceProvider enum should also match org.hibernate.jpa.HibernateEntityManager (DATAJPA-444)
* EntityManagerBeanDefinitionRegistrarPostProcessor doesn't find EntityManagerFactory definitions in BeanFactory hierarchy (DATAJPA-453)
* Upgrade to EclipseLink 2.5.1 (DATAJPA-417)
* Upgrade Hibernate 4 build to latest versions (DATAJPA-443)
* Add contribution guidelines (DATAJPA-448)
* Release Spring Data JPA 1.5.0.RC1 (DATAJPA-450)

Changes in version 1.4.3.RELEASE (2013-12-11)
---------------------------------------------
* Improved documentation on jpa:repository namespace (DATAJPA-410)
* Fixed count projection for manual queries with projections (DATAJPA-420)
* Guard against null predicates on query creation. (DATAJPA-405)
* Fixed alias detection in manually defined queries using SpEL (DATAJPA-424)
* Generate left joins for referenced associations in sort expressions (DATAJPA-427)
* Tweaks to be compatible with Hibernate 4.3 (DATAJPA-430)
* Release 1.4.3 (DATAJPA-434)

Changes in version 1.5.0.M1 (2013-11-19)
----------------------------------------
* CriteriaQuery caching causes problems with Hibernate's alias name generation in multithreaded environments (DATAJPA-396)
* Unnecessary OUTER JOIN are generated for querying one single table (DATAJPA-401)
* Unnecessary OUTER JOIN are generated when sorting on ElementCollection (DATAJPA-403)
* Custom repository method doesn't parse OrderBy clause properly (DATAJPA-405)
* Reference and Javadoc inconsistence - @Modifying.clearAutomatically default value (DATAJPA-406)
* ClasspathScanningPersistenceUnitPostProcessor throws IndexOutOfBoundsException on Windows (DATAJPA-407)
* org.springframework.data.jpa.repository.Query is not supported varargs (DATAJPA-415)
* #entityName SpEL does not match alias regex when detecting alias (DATAJPA-424)
* Make it possible to configure auditing with Java Config (DATAJPA-265)
* Improve execution of native queries (DATAJPA-389)
* Allow turning a CRUD method into a query method by annotating it with @Query (DATAJPA-398)
* Add support for nested repositories (DATAJPA-416)
* Upgrade to Spring Framework 3.2.5 (DATAJPA-418)
* Improve accessibility of methods in SimpleJpaRepository (DATAJPA-426)
* Add getReference method to the SimpleJpaRepository (DATAJPA-83)
* @Lazy for interface defined repositories (DATAJPA-419)

Changes in version 1.4.2.RELEASE (2013-10-25)
---------------------------------------------
* CriteriaQuery caching causes problems with Hibernate's alias name generation in multithreaded environments (DATAJPA-396)
* Unnecessary OUTER JOIN are generated for querying one single table (DATAJPA-401)
* Unnecessary OUTER JOIN are generated when sorting on ElementCollection (DATAJPA-403)
* Reference and Javadoc inconsistence - @Modifying.clearAutomatically default value (DATAJPA-406)
* ClasspathScanningPersistenceUnitPostProcessor throws IndexOutOfBoundsException on Windows (DATAJPA-407)
* Add Java Config example to readme.md (DATAJPA-399)
* Release 1.4.2 (DATAJPA-411)

Changes in version 1.4.1.RELEASE (2013-09-09)
---------------------------------------------
* Update to Spring Data Commons 1.6.1 (DATAJPA-400)

Changes in version 1.4.0.RELEASE (2013-09-09)
---------------------------------------------
* The H2 database needs a quoted name parameter in a @Query annotation (DATAJPA-354)
* Not specifying the ASC clause results in a double ORDER BY clause (DATAJPA-375)
* Undeclared dependency on spring aspectj (DATAJPA-367)
* @Transactional (AspectJ) and @Configurable not weaved for @Entity if this @Entity is managed by a JpaRepository (DATAJPA-298)
* Spring Data ignores alternative EntityManager Producer in CDI environment (DATAJPA-388)
* Typo in package-info.java of utility package (DATAJPA-392)
* Remove Hibernate specific test for DATAJPA-107 in PartTreeJpaQueryIntegrationTests (DATAJPA-381)

Changes in version 1.4.0.RC1 (2012-08-01)
-----------------------------------------
* Sort with aggregate properties results in an invalid alias being generated and added to the query (DATAJPA-148)
* Composite Primary Key Class With Foreign Key using IdClass results in TypeMismatchException (DATAJPA-269)
* Sort by property of an associated object doesn't work. Join type should be LEFT OUTER JOIN for sorting on associated objects. (DATAJPA-346)
* @IdClass of @MappedSuperclass is not supported (DATAJPA-348)
* SimpleJpaQuery validates count queries to aggressively (DATAJPA-352)
* ClasspathScanningPersistenceUnitPostProcessor does not resolve mapping files properly (DATAJPA-353)
* @Lock annotation doesn't work on (at least) findOne method (DATAJPA-359)
* Improve SimpleJpaRepository.delete(ID id) by removing superfluous call to exists(…). (DATAJPA-363)
* NullPointerException for manually defined queries with multiple named like expressions (DATAJPA-373)
* Count queries should be issued without ORDER BY clause (DATAJPA-377)
* JpaRepository.findAll(Iterable<ID> ids) should return List<T>, not Iterable<T> (DATAJPA-357)
* Improve QueryDslJpaRepository.findAll(Predicate, Pageable) (DATAJPA-361)
* Explain usage of SpEL Expression in @Query(...) on Repository methods (DATAJPA-368)
* Problems with entities weaved by EclipseLink (DATAJPA-376)
* Add support for TemporalType parameter in named queries (DATAJPA-107)
* Passing parameters from child classes to the parents' annotations (DATAJPA-170)
* Ref doc - explain about save strategy repositories (DATAJPA-344)
* Add missing package-info.java files (DATAJPA-370)
* Release 1.4 RC1 (DATAJPA-379)

Changes in version 1.3.4.RELEASE (2013-07-24)
---------------------------------------------
* Fixed NullPointerException for manually defined queries with multiple named like expressions (DATAJPA-373)
* Added missing package-info.java files (DATAJPA-370)

Changes in version 1.3.3.RELEASE (2013-07-19)
---------------------------------------------
* Sort with aggregate properties results in an invalid alias being generated and added to the query (DATAJPA-148)
* Repository injection fails if BeanPostProcessor depends on repository (DATAJPA-335)
* New LIKE expression handling breaks backward compatibility (DATAJPA-341)
* Wrong calculation of total number of elements when using DISTINCT clause (DATAJPA-342)
* Subselect bug introduced in the snapshot on 20130507 (DATAJPA-343)
* @IdClass of @MappedSuperclass is not supported (DATAJPA-348)
* Improve query validation by creating explicit EntityManager instance (DATAJPA-350)
* @Lock annotation doesn't work on (at least) findOne method (DATAJPA-359)
* Improve SimpleJpaRepository.delete(ID id) by removing superfluous call to exists(…). (DATAJPA-363)
* Open up OSGi manifest for Spring 4 (DATAJPA-339)
* Impove OSGi manifest to allow usage with Spring 4 and Slf4j 1.6.x (DATAJPA-340)
* @QueryHints can not be used in a meta annotation (DATAJPA-345)
* Improve QueryDslJpaRepository.findAll(Predicate, Pageable) (DATAJPA-361)
* Release 1.3.3 (DATAJPA-369)

Changes in version 1.4.0.M1 (2012-06-04)
----------------------------------------
* Upgrade to Querydsl 3.0.0 (DATAJPA-321, DATAJPA-320)
* Support query internal LIKE expressions (DATAJPA-292, DATAJPA-341)
* Support to 'countBy' query methods. (DATAJPA-231)
* Support for case insensitive sorting in Pageable and Sort (DATAJPA-296, DATAJPA-327)
* Code doesn't compile with JDK 6 due to generic method signature (DATAJPA-304)
* XML Schema Validation errors in STS (DATAJPA-314)
* Allow building against Hibernate 4 (DATAJPA-316)
* JpaRepository interface should have @NoRepositoryBean (DATAJPA-317)
* findAll(ids) throws SQL grammar exception when given an empty collection (DATAJPA-332)
* Repository injection fails if BeanPostProcessor depends on repository (DATAJPA-335)
* Release 1.3.1 breaks auditing (DATAJPA-336)
* Wrong calculation of total number of elements when using DISTINCT clause (DATAJPA-342)
* Subselect bug introduced in the snapshot on 20130507 (DATAJPA-343)
* Improve query validation by creating explicit EntityManager instance (DATAJPA-350)
* JpaEntityInformationSupport could inspect potentially available @Version annotated field for null to discover isNew (DATAJPA-119)
* Specifications must support null as a parent Specification (DATAJPA-300)
* Prevent duplicate registration of PersistenceAnnotationBeanPostProcessor (DATAJPA-308)
* CDI repository beans should be application scoped (DATAJPA-319)
* Invalid class name referenced in readme.md (DATAJPA-334)
* Impove OSGi manifest to allow usage with Spring 4 and Slf4j 1.6.x (DATAJPA-340, DATAJPA-339)
* @QueryHints can now be used in a meta annotation (DATAJPA-345)
* Release 1.4. M1 (DATAJPA-351)

Changes in version 1.3.2.RELEASE (2013-05-02)
---------------------------------------------
* Release 1.3.1 breaks auditing (DATAJPA-336)
* findAll(ids) throws SQL grammar exception when given an empty collection (DATAJPA-332)
* Invalid class name referenced in readme.md (DATAJPA-334)

Changes in version 1.3.1.RELEASE (2013-04-16)
---------------------------------------------
* Support query internal LIKE expressions (DATAJPA-292)
* Code doesn't compile with JDK 6 due to generic method signature (DATAJPA-304)
* XML Schema Validation errors in STS (DATAJPA-314)
* Allow building against Hibernate 4 (DATAJPA-316)
* JpaRepository interface should have @NoRepositoryBean (DATAJPA-317)
* Specifications must support null as a parent Specification (DATAJPA-300)
* Prevent duplicate registration of PersistenceAnnotationBeanPostProcessor (DATAJPA-308)

Changes in version 1.3.0.RELEASE (2012-02-07)
---------------------------------------------
* Added support for annotation based auditing (DATAJPA-116)
* SimpleJpaRepository.exists(ID) works now, if ID is an composite key (DATAJPA-266)
* LockModePopulatingMethodInterceptor does not cause memory leak (DATAJPA-268)
* Pageable sorting by join property excludes null matches with Specifications (DATAJPA-277)
* Switch to Logback for test logging (DATAJPA-271)
* Fixed NullPointerException in unit tests (DATAJPA-280)
* Expose Spring Data Commons mapping metamodel (DATAJPA-274, DATAJPA-284, DATAJPA-283)
* Separated integration tests for different persistence providers (DATAJPA-286, DATAJPA-288)
* Polished documentation (DATAJPA-282, DATAJPA-291)
* Improved build (DATAJPA-285, DATAJPA-271)
* Upgraded to JodaTime 2.1 (DATAJPA-293)

Changes in version 1.2.1.RELEASE (2012-02-07)
---------------------------------------------
* SimpleJpaRepository.exists(ID) fails, if ID is an composite key (DATAJPA-266)
* LockModePopulatingMethodInterceptor leak causes memory leak (DATAJPA-268)
* Pageable sorting by join property excludes null matches with Specifications (DATAJPA-277)
* Switch to Logback for test logging (DATAJPA-271)

Changes in version 1.2.0.RELEASE (2012-10-10)
----------------------------------------
* Resolve conflicts between Spring Data Commons Core, JPA and Mongo (DATAJPA-146)
* Improved reference documentation/JavaDoc (DATAJPA-251, DATAJPA-261, DATAJPA-174, DATAJPA-238)
* Upgrade to latest Querydsl 2.8.0, APT 1.0.4 (DATAJPA-259, DATAJPA-260)
* Improved detection of joins to make sure sorting gets applied correctly (DATAJPA-252)
* Fixed application of Specifications.not(…) (DATAJPA-253)
* Fixed detection of total elements when paging over partitioned entities with EclipseLink (DATAJPA-257)

Changes in version 1.2.0.RC1 (2012-07-24)
-----------------------------------------
* Sorting of paginated results is not working when Querydsl is used (DATAJPA-243)
* Namespace XSDs of current release version should refer to repositories XSD in version 1.0 (DATAJPA-239)
* NamedQuery should defer warning logs until the named query is actually detected (DATAJPA-241)
* JpaClassUtils#isEntityManagerOfType is not safe within an OSGI Environment (DATAJPA-244)
* Make Spring 3.1.2.RELEASE default Spring dependency version (DATAJPA-249)
* List compile time dependencies explicitly (DATAJPA-245)

Changes in version 1.1.2.RELEASE (2012-08-24)
---------------------------------------------
* Namespace XSDs of current release version should refer to repositories XSD in version 1.0 (DATAJPA-239)
* NamedQuery should defer warning logs until the named query is actually detected (DATAJPA-241)
* JpaClassUtils#isEntityManagerOfType is not safe within an OSGI Environment (DATAJPA-244)
* List compile time dependencies explicitly (DATAJPA-245)

Changes in version 1.2.0.M1 (2012-07-23)
----------------------------------------
* Added support for JavaConfig support for repositories (DATAJPA-69)
* Upgraded to Hibernate 3.6.10 and EclipseLink 2.4.0 (DATAJPA-229)
* A round of code improvements (DATAJPA-104)
* Throw more appropriate exception if manually defined query in @Query is invalid (DATAJPA-226)
* SimpleJpaRepository.findAll(Iterable<ID> ids) now works when handing in non-Lists (DATAJPA-232)
* OSGi bundle identifier is now org.springframework.data.jpa (DATAJPA-215)
* We now include JPA metamodel classes for our base domain types (DATAJPA-217)
* Improved documentation of how to use native queries with @Query (DATAJPA-227)

Changes in version 1.1.1.RELEASE (2012-07-23)
---------------------------------------------
* Throw more appropriate exception if manually defined query in @Query is invalid (DATAJPA-226)
* SimpleJpaRepository.findAll(Iterable<ID> ids) now works when handing in non-Lists (DATAJPA-232)
* OSGi bundle identifier is now org.springframework.data.jpa (DATAJPA-215)
* We now include JPA metamodel classes for our base domain types (DATAJPA-217)
* Improved documentation of how to use native queries with @Query (DATAJPA-227)
* Upgraded to Spring Data Commons 1.3.2.RELEASE (DATAJPA-233)

Changes in version 1.1.0.RELEASE (2012-05-16)
---------------------------------------------
* JPA Repository can get typing of @Query annotated findXXX methods wrong (DATAJPA-169)
* SimpleJpaRepository.delete(ID id) should validate entity/record existance prior to issue delete command (DATAJPA-177)
* StackOverflowError during query parsing if repository method does nots match with a property (DATAJPA-179)
* Typos in log message when named query is used with a Pageable (DATAJPA-186)
* MergingPersistenceUnitManager potentially adds classes multiple times (DATAJPA-189)
* JpaQueryExecution started to throw NullPointerException if pageable is null (DATAJPA-201)
* Typo in reference (DATAJPA-153)
* Readme file references old Spring Data JPA version (DATAJPA-155)
* Ability to specify template when create QueryDSL JPAQuery (DATAJPA-181)
* Provide more safety in custom @Queries with named @Params (DATAJPA-185)
* Log level used for message generated with named query with a Pageable parameter lower than the actual severity of the issue (DATAJPA-187)
* MergingPersistenceUnitManager should merge mapping file locations as well (DATAJPA-190)
* Adapt changes in CrudRepository interface (DATAJPA-191)
* @Query(nativeQuery=true) can't return List<Integer> (DATAJPA-207)
* Add support for newly introduced StartingWith, EndingWith and Containing keywords (DATAJPA-180)
* Support Before and After keywords for query creation (DATAJPA-188)
* Minor typos in readme.md (DATAJPA-192)
* Upgrade to Spring Data Commons 1.3.0.RC1 (DATAJPA-193)
* Upgrade to Spring Data Commons 1.3.0.GA (DATAJPA-194)
* Upgrade to Querydsl 2.5.0 (DATAJPA-203)
* Sonar build is failing (DATAJPA-210)
* Document CDI integration (DATAJPA-211)
* Release 1.1 GA (DATAJPA-212)

Changes in version 1.1.0.RC1 (2012-02-03)
-----------------------------------------
* Support for locking (DATAJPA-73)
* Added CDI integration for repositories (DATAJPA-136)
* Support for @IdClass in entities (DATAJPA-50)
* Support for LessThanEqual and GreaterThanEquals keywords in query methods (DATAJPA-108)
* Support for True/False as query keywords (DATAJPA-132)
* Queries derivated from methods now correctly bind null values using IS NULL (DATAJPA-121)
* Added PersistenceUnitPostProcessor to scan a package for JPA entities with Spring 3.0 (DATAJPA-123, DATAJPA-124)
* Improve performance for pagination queries if inexistant page is requested (DATAJPA-124)
* Don't apply query hints to count queries for pagination (DATAJPA-54)
* Fixed count query creation for manually declared queries with "order by" clause (DATAJPA-142)
* @OneToOne mappings in AbstractAuditable should rather be @ManyToOne ones (DATAJPA-120)
* Alias detection now works correctly with entity names containing numbers (DATAJPA-110)
* Added @NativeQuery annotation for repositories to trigger native queries (DATAJPA-117)
* Allow customization of NamedQuery to be used in @Query annotation (DATAJPA-129)
* Let deleteAll() use cascading deletes and introduced deleteAllInBatch() (DATAJPA-137)
* Fixed query creation for Comparable values (DATAJPA-99)
* The SimpleJpaRepository's deleteAll() does not call em.clear() anymore (DATAJPA-111)
* JpaMetamodelEntityInformation now deals with mapped superclasses as well (DATAJPA-141)
* MergingPersistenceUnitManager now works with Spring 3.1.0 DefaultPersistenceUnitManager (DATAJPA-138)
* Provide strategy interface to customize the date being set while auditing (DATAJPA-9)
* Improve exception message when crating a query from a method name fails (DATAJPA-159)
* Imporve injection of the EntityManager in QueryDslRepositorySupport (DATAJPA-135, DATAJPA-113)
* Consolidate Expression creation for property references and sort orders (DATAJPA-103)
* Fixed schema errors appearing in STS (DATAJPA-160)
* Change build system to build against Spring 3.0.x / 3.1 / 3.2 (DATAJPA-127)
* Upgraded dependency versions (Spring 3.0.7, Querydsl 2.3.0, Hibernate 3.6.9, EclipseLink 2.3.2, OpenJPA 2.1.1, AspectJ 1.6.12) (DATAJPA-102, DATAJPA-150, DATAJPA-145, DATAJPA-115)

Changes in version 1.0.3.GA (2012-02-03)
----------------------------------------
* MergingPersistenceUnitManager doesnt work with Spring 3.1.0 DefaultPersistenceUnitManager (DATAJPA-138)
* Spring Data- JPA Schema validation error in STS (DATAJPA-160)
* Improve exception message when crating a query from a method name fails (DATAJPA-159)

Changes in version 1.0.2.GA (2011-12-06)
----------------------------------------
* Fixed query creation for Comparable values (DATAJPA-99)
* Fixed alias detection when entity name contained number (DATAJPA-110)
* SimpleJpaRepository's deleteAll() does not call em.clear() anymore (DATAJPA-111)
* Upgraded to Querydsl 2.2.5 (DATAJPA-102, DATAJPA-115)
* Fixed auditor mappings in AbstractAuditable (DATAJPA-120)
* Consolidate Expression creation for property references and sort orders (DATAJPA-103)
* Fixed dependency injection in QueryDslRepositorySupport (DATAJPA-113)

Changes in version 1.1.0.M1 (2011-09-05)
----------------------------------------
* Added ability to ignore case in derived method names (DATAJPA-92)
* Upgraded dependency to Spring Data Commons 1.2.0.M1

Changes in version 1.0.1.GA (2011-09-05)
----------------------------------------
* Fixed pagination for queries using group-by clause (DATAJPA-86)
* Fixed NoSuchElementException for queries using dynamic sorting (DATAJPA-90)
* JpaEntityInformationSupport.getMetadata(...) throws IllegalArgumentException if entity not found in JPA metamodel (DATAJPA-93)
* Sort handed via Pageable instance is now again regarded by query execution (DATAJPA-94)
* Fixed creating CriteriaQuery instances for some keywords when deriving queries from method names (DATAJPA-96)
* Fixed manifest version reference of javax.annotation (DATAJPA-79)
* Improved performanve of SimpleJpaRepository.exists(…) by not loading the entity (DATAJPA-81)
* Updated reference documentation covering persistence exception translation, custom namespace attributes (DATAJPA-91, DATAJPA-88, DATAJPA-80)
* Reformatted sources with Spring Data Eclipse formatter and added settings to project (DATAJPA-98)

Changes in version 1.0.0.GA (2011-07-21)
----------------------------------------
* Updated to Spring Data Commons 1.1.0.RELEASE - (DATACMNS-53, DATACMNS-49)
* Upgrade to Querydsl 2.2.0 (DATAJPA-78, DATACMNS-53)
* Improved performance by caching CriteriaQuery creation if possible (DATAJPA-71)
* Fixed version range for Spring Data Commons packages in template.mf (DATAJPA-72)
* Fixed count query execution for pagination queries with manually defined query (DATAJPA-77)
* Polished documentation

Changes in version 1.0.0.RC1 (2011-06-21) - https://jira.springsource.org/browse/DATAJPA/fixforversion/11869
-----------------------------------------
* Expose findAll(Specification<T> spec, Sort sort) in JpaSpecificationExecutor (DATAJPA-48)
* Improved target domain class detection for queries returning void and numeric values (DATAJPA-44)
* Improved extensibility of QueryDslJpaRepository (DATAJPA-39)
* Configured Maven build to include Bundlor generated MANIFEST.MF (DATAJPA-42)
* Improved performance of query execution by uhsing method.getAnnotation(…) instead of AnnotationUtils.getAnnotation(…)
* Fixed exception thrown in QueryDslJpaRepository#findAll(Predicate predicate, Pageable pageable) if no result found (DATAJPA-61, DATACMNS-23)
* Improved error message in JpaQueryMethod (DATAJPA-59, DATAJPA-60)
* Querydsl dependency is now optional (DATAJPA-62)
* Improved isNew detection in AuditingEntityListener (DATAJPA-68)
* Documented In and NotIn keywords (DATAJPA-56)
* Updated documentation regarding Specifications usage (DATAJPA-43)
* Added MergingPersistenceUnitManager (DATAJPA-38)
* Added support for searching by Specification and Sort (DATAJPA-48)
* Automatic query creation now uses ParameterExpression binding over literal binding (DATAJPA-64)
* Changed clearAutomatically attribute of @Modifying to default to false (DATAJPA-31)
* JpaPersistableEntityInformation now uses Persistable.isNew(…) (DATAJPA-58)

Changes in version 1.0.0.M2 (2011-03-24) - https://jira.springsource.org/browse/DATAJPA/fixforversion/11800
----------------------------------------
* Dynamic sorting functionality (through Sort query method parameter) works again
* Added support for 'Distinct' (DATACMNS-15)
* Added support for 'In' and 'NotIn' (DATAJPA-30)
* Adapted new metadata API (DATAJPA-32, DATACMNS-17)
* Support for XML based entity mapping (DATAJPA-28)
* @Query annotated queries get validated on Query meta-model creation (DATAJPA-14)
* Fixed dependency scopes and missing repository declarations (DATAJPA-33, DATAJPA-26)
* Adapted meta-model API from Commons module (DATAJPA-32)
* Added support for QueryDsl (DATAJPA-8)

Changes in version 1.0.0.M1 (2011-02-10) - https://jira.springsource.org/browse/DATAJPA/fixforversion/11786
----------------------------------------
* Moved JPA sepcific code from Hades into Spring Data JPA (functional equivalent of Hades 2.0.2) building on common functionality in Spring Data Commons
