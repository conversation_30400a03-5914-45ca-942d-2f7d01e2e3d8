<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<groupId>org.springframework.data</groupId>
	<artifactId>spring-data-commons</artifactId>
	<version>1.12.4.RELEASE</version>

	<name>Spring Data Core</name>

	<parent>
		<groupId>org.springframework.data.build</groupId>
		<artifactId>spring-data-parent</artifactId>
		<version>1.8.4.RELEASE</version>
	</parent>

	<properties>
		<dist.key>DATACMNS</dist.key>
		<scala>2.11.7</scala>
	</properties>

	<dependencies>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-core</artifactId>
			<exclusions>
				<exclusion>
					<groupId>commons-logging</groupId>
					<artifactId>commons-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-beans</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-expression</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-tx</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-oxm</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>${jackson}</version>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-web</artifactId>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<version>3.0.1</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>javax.annotation</groupId>
			<artifactId>jsr250-api</artifactId>
			<version>1.0</version>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>joda-time</groupId>
			<artifactId>joda-time</artifactId>
			<version>${jodatime}</version>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.threeten</groupId>
			<artifactId>threetenbp</artifactId>
			<version>${threetenbp}</version>
			<optional>true</optional>
		</dependency>

		<!--  Querydsl -->

		<dependency>
			<groupId>com.querydsl</groupId>
			<artifactId>querydsl-core</artifactId>
			<version>${querydsl}</version>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>com.querydsl</groupId>
			<artifactId>querydsl-apt</artifactId>
			<version>${querydsl}</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>com.querydsl</groupId>
			<artifactId>querydsl-collections</artifactId>
			<version>${querydsl}</version>
			<optional>true</optional>
		</dependency>

		<!-- EJB Transactions -->
		<dependency>
			<groupId>javax.ejb</groupId>
			<artifactId>ejb-api</artifactId>
			<version>3.0</version>
			<optional>true</optional>
		</dependency>

		<!-- CDI -->
		<dependency>
			<groupId>javax.enterprise</groupId>
			<artifactId>cdi-api</artifactId>
			<version>${cdi}</version>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>${guava}</version>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>javax.el</groupId>
			<artifactId>el-api</artifactId>
			<version>${cdi}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.openwebbeans.test</groupId>
			<artifactId>cditest-owb</artifactId>
			<version>${webbeans}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework.hateoas</groupId>
			<artifactId>spring-hateoas</artifactId>
			<version>${spring-hateoas}</version>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-webmvc</artifactId>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>com.sun.xml.bind</groupId>
			<artifactId>jaxb-impl</artifactId>
			<version>2.2.3U1</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>xmlunit</groupId>
			<artifactId>xmlunit</artifactId>
			<version>1.6</version>
			<scope>test</scope>
		</dependency>

		<!-- Groovy -->
		<dependency>
			<groupId>org.codehaus.groovy</groupId>
			<artifactId>groovy-all</artifactId>
			<version>2.4.4</version>
			<scope>test</scope>
		</dependency>

		<!-- Scala -->
		<dependency>
			<groupId>org.scala-lang</groupId>
			<artifactId>scala-library</artifactId>
			<version>${scala}</version>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>javax.transaction</groupId>
			<artifactId>javax.transaction-api</artifactId>
			<version>1.2</version>
			<scope>test</scope>
		</dependency>
		
		<!-- JSONPath -->
		
		<dependency>
			<groupId>com.jayway.jsonpath</groupId>
			<artifactId>json-path</artifactId>
			<version>2.0.0</version>
			<scope>test</scope>
		</dependency>

	</dependencies>

	<build>
		<plugins>

			<plugin>
				<groupId>com.mysema.maven</groupId>
				<artifactId>apt-maven-plugin</artifactId>
				<version>${apt}</version>
				<executions>
					<execution>
						<phase>generate-test-sources</phase>
						<goals>
							<goal>test-process</goal>
						</goals>
						<configuration>
							<outputDirectory>${project.build.directory}/generated-sources/test-annotations</outputDirectory>
							<processor>com.querydsl.apt.QuerydslAnnotationProcessor</processor>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-enforcer-plugin</artifactId>
				<version>1.3.1</version>
				<executions>
					<execution>
						<id>enforce-rules</id>
						<goals>
							<goal>enforce</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<rules>
						<requireJavaVersion>
							<version>[1.8,1.9)</version>
						</requireJavaVersion>
					</rules>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-assembly-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>wagon-maven-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>org.asciidoctor</groupId>
				<artifactId>asciidoctor-maven-plugin</artifactId>
			</plugin>

		</plugins>
	</build>

	<profiles>
		<profile>
			<id>release</id>
			<build>
				<plugins>
					<plugin>
						<groupId>org.jfrog.buildinfo</groupId>
						<artifactId>artifactory-maven-plugin</artifactId>
						<inherited>false</inherited>
					</plugin>
				</plugins>
			</build>
		</profile>
	</profiles>

	<repositories>
		<repository>
			<id>spring-libs-release</id>
			<url>https://repo.spring.io/libs-release</url>
		</repository>
	</repositories>

	<pluginRepositories>
		<pluginRepository>
			<id>spring-plugins-release</id>
			<url>https://repo.spring.io/plugins-release</url>
		</pluginRepository>
	</pluginRepositories>

</project>
