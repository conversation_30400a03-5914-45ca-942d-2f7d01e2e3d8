<?xml version="1.0" encoding="UTF-8" standalone="no"?>

<xsd:schema xmlns="http://www.springframework.org/schema/cache"
		xmlns:xsd="http://www.w3.org/2001/XMLSchema"
		xmlns:beans="http://www.springframework.org/schema/beans"
		xmlns:tool="http://www.springframework.org/schema/tool"
		targetNamespace="http://www.springframework.org/schema/cache"
		elementFormDefault="qualified"
		attributeFormDefault="unqualified">

	<xsd:import namespace="http://www.springframework.org/schema/beans" schemaLocation="http://www.springframework.org/schema/beans/spring-beans-4.0.xsd"/>
	<xsd:import namespace="http://www.springframework.org/schema/tool" schemaLocation="http://www.springframework.org/schema/tool/spring-tool-4.0.xsd"/>

	<xsd:annotation>
		<xsd:documentation><![CDATA[
	Defines the elements used in the Spring Framework's declarative
	cache management infrastructure.
		]]></xsd:documentation>
	</xsd:annotation>

	<xsd:element name="annotation-driven">
		<xsd:complexType>
			<xsd:annotation>
				<xsd:documentation source="java:org.springframework.cache.annotation.AnnotationCacheOperationDefinitionSource"><![CDATA[
	Indicates that cache configuration is defined by Java 5
	annotations on bean classes, and that proxies are automatically
	to be created for the relevant annotated beans.

	The default annotations supported are Spring's @Cacheable and @CacheEvict.

	See org.springframework.cache.annotation.EnableCaching Javadoc
	for information on code-based alternatives to this XML element.
				]]></xsd:documentation>
			</xsd:annotation>
			<xsd:attribute name="cache-manager" type="xsd:string" default="cacheManager">
				<xsd:annotation>
					<xsd:documentation source="java:org.springframework.cache.CacheManager"><![CDATA[
	The bean name of the CacheManager that is to be used to retrieve the backing caches.

	This attribute is not required, and only needs to be specified
	explicitly if the bean name of the desired CacheManager
	is not 'cacheManager'.
					]]></xsd:documentation>
					<xsd:appinfo>
						<tool:annotation kind="ref">
							<tool:expected-type type="org.springframework.cache.CacheManager"/>
						</tool:annotation>
					</xsd:appinfo>
				</xsd:annotation>
			</xsd:attribute>
			<xsd:attribute name="key-generator" type="xsd:string">
				<xsd:annotation>
					<xsd:documentation source="java:org.springframework.cache.interceptor.KeyGenerator"><![CDATA[
	The bean name of the KeyGenerator that is to be used to retrieve the backing caches.

	This attribute is not required, and only needs to be specified
	explicitly if the default strategy (DefaultKeyGenerator) is not sufficient.
					]]></xsd:documentation>
					<xsd:appinfo>
						<tool:annotation kind="ref">
							<tool:expected-type type="org.springframework.cache.interceptor.KeyGenerator"/>
						</tool:annotation>
					</xsd:appinfo>
				</xsd:annotation>
			</xsd:attribute>
			<xsd:attribute name="mode" default="proxy">
				<xsd:annotation>
					<xsd:documentation><![CDATA[
	Should annotated beans be proxied using Spring's AOP framework,
	or should they rather be weaved with an AspectJ transaction aspect?

	AspectJ weaving requires spring-aspects.jar on the classpath,
	as well as load-time weaving (or compile-time weaving) enabled.

	Note: The weaving-based aspect requires the @Cacheable and @CacheInvalidate
	annotations to be defined on the concrete class. Annotations in interfaces
	will not work in that case (they will rather only work with interface-based proxies)!
					]]></xsd:documentation>
				</xsd:annotation>
				<xsd:simpleType>
					<xsd:restriction base="xsd:string">
						<xsd:enumeration value="proxy"/>
						<xsd:enumeration value="aspectj"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:attribute>
			<xsd:attribute name="proxy-target-class" type="xsd:boolean" default="false">
				<xsd:annotation>
					<xsd:documentation><![CDATA[
	Are class-based (CGLIB) proxies to be created? By default, standard
	Java interface-based proxies are created.

	Note: Class-based proxies require the @Cacheable and @CacheInvalidate annotations
	to be defined on the concrete class. Annotations in interfaces will not work
	in that case (they will rather only work with interface-based proxies)!
					]]></xsd:documentation>
				</xsd:annotation>
			</xsd:attribute>
			<xsd:attribute name="order" type="xsd:token">
				<xsd:annotation>
					<xsd:documentation source="java:org.springframework.core.Ordered"><![CDATA[
	Controls the ordering of the execution of the cache advisor
	when multiple advice executes at a specific joinpoint.
					]]></xsd:documentation>
				</xsd:annotation>
			</xsd:attribute>
		</xsd:complexType>
	</xsd:element>

	<xsd:element name="advice">
		<xsd:complexType>
			<xsd:annotation>
				<xsd:documentation source="java:org.springframework.cache.interceptor.CacheInterceptor"><![CDATA[
	Defines the cache semantics of the AOP advice that is to be
	executed.

	That is, this advice element is where the cacheable semantics of
	any	number of methods are defined (where cacheable semantics
	includes the backing cache(s), the key, cache condition rules, and suchlike).
				]]></xsd:documentation>
				<xsd:appinfo>
					<tool:annotation>
						<tool:exports type="java:org.springframework.cache.interceptor.CacheInterceptor"/>
					</tool:annotation>
				</xsd:appinfo>
			</xsd:annotation>
			<xsd:complexContent>
				<xsd:extension base="beans:identifiedType">
					<xsd:sequence>
						<xsd:element name="caching" type="definitionsType" minOccurs="0" maxOccurs="unbounded"/>
					</xsd:sequence>
					<xsd:attribute name="cache-manager" type="xsd:string" default="cacheManager">
						<xsd:annotation>
							<xsd:documentation source="java:org.springframework.cache.CacheManager"><![CDATA[
	The bean name of the CacheManager that is to be used
	for storing and retrieving data.

	This attribute is not required, and only needs to be specified
	explicitly if the bean name of the desired CacheManager
	is not 'cacheManager'.
							]]></xsd:documentation>
							<xsd:appinfo>
								<tool:annotation kind="ref">
									<tool:expected-type type="org.springframework.cache.CacheManager"/>
								</tool:annotation>
							</xsd:appinfo>
						</xsd:annotation>
					</xsd:attribute>
					<xsd:attribute name="key-generator" type="xsd:string">
						<xsd:annotation>
							<xsd:documentation source="java:org.springframework.cache.interceptor.KeyGenerator"><![CDATA[
	The bean name of the KeyGenerator that is to be used to retrieve the backing caches.

	This attribute is not required, and only needs to be specified
	explicitly if the default strategy (DefaultKeyGenerator) is not sufficient.
							]]></xsd:documentation>
							<xsd:appinfo>
								<tool:annotation kind="ref">
									<tool:expected-type type="org.springframework.cache.interceptor.KeyGenerator"/>
								</tool:annotation>
							</xsd:appinfo>
						</xsd:annotation>
					</xsd:attribute>
				</xsd:extension>
			</xsd:complexContent>
		</xsd:complexType>
	</xsd:element>

	<xsd:complexType name="basedefinitionType">
		<xsd:attribute name="cache" type="xsd:string" use="optional">
			<xsd:annotation>
				<xsd:documentation><![CDATA[
	The name of the backing cache(s). Multiple caches can be specified by separating them using comma: 'orders, books']]></xsd:documentation>
			</xsd:annotation>
		</xsd:attribute>
		<xsd:attribute name="key" type="xsd:string" use="optional">
			<xsd:annotation>
				<xsd:documentation><![CDATA[
	The SpEL expression used for computing the cache key, mutually exclusive with the key-generator parameter.]]></xsd:documentation>
			</xsd:annotation>
		</xsd:attribute>
        <xsd:attribute name="key-generator" type="xsd:string" use="optional">
            <xsd:annotation>
                <xsd:documentation><![CDATA[
	The name of the KeyGenerator bean responsible to compute the key, mutually exclusive with the key parameter.]]></xsd:documentation>
            </xsd:annotation>
        </xsd:attribute>
		<xsd:attribute name="cache-manager" type="xsd:string" use="optional">
			<xsd:annotation>
				<xsd:documentation><![CDATA[
	The name of the CacheManager bean responsible to manage the operation.]]></xsd:documentation>
			</xsd:annotation>
		</xsd:attribute>
		<xsd:attribute name="condition" type="xsd:string" use="optional">
			<xsd:annotation>
				<xsd:documentation><![CDATA[
	The SpEL expression used for conditioning the method caching.]]></xsd:documentation>
			</xsd:annotation>
		</xsd:attribute>
		<xsd:attribute name="method" type="xsd:string" use="optional">
			<xsd:annotation>
				<xsd:documentation><![CDATA[
	The method name(s) with which the cache attributes are to be
	associated. The wildcard (*) character can be used to associate the
	same cache attribute settings with a number of methods; for
	example, 'get*', 'handle*', '*Order', 'on*Event', etc.]]></xsd:documentation>
			</xsd:annotation>
		</xsd:attribute>

	</xsd:complexType>

	<xsd:complexType name="definitionsType">
		<xsd:complexContent>
			<xsd:extension base="basedefinitionType">
				<xsd:sequence>
				  <xsd:choice minOccurs="0" maxOccurs="unbounded">
					<xsd:element name="cacheable" minOccurs="0" maxOccurs="unbounded">
						<xsd:complexType>
							<xsd:complexContent>
								<xsd:extension base="basedefinitionType">
									<xsd:attribute name="unless" type="xsd:string" use="optional">
										<xsd:annotation>
											<xsd:documentation><![CDATA[
	The SpEL expression used to veto the method caching.]]></xsd:documentation>
										</xsd:annotation>
									</xsd:attribute>
								</xsd:extension>
							</xsd:complexContent>
						</xsd:complexType>
					</xsd:element>
					<xsd:element name="cache-put" minOccurs="0" maxOccurs="unbounded">
						<xsd:complexType>
							<xsd:complexContent>
								<xsd:extension base="basedefinitionType">
									<xsd:attribute name="unless" type="xsd:string" use="optional">
										<xsd:annotation>
											<xsd:documentation><![CDATA[
	The SpEL expression used to veto the method caching.]]></xsd:documentation>
										</xsd:annotation>
									</xsd:attribute>
								</xsd:extension>
							</xsd:complexContent>
						</xsd:complexType>
					</xsd:element>
					<xsd:element name="cache-evict" minOccurs="0" maxOccurs="unbounded">
						<xsd:complexType>
							<xsd:complexContent>
								<xsd:extension base="basedefinitionType">
									<xsd:attribute name="all-entries" type="xsd:boolean" use="optional">
										<xsd:annotation>
											<xsd:documentation><![CDATA[
	Whether all the entries should be evicted.]]></xsd:documentation>
										</xsd:annotation>
									</xsd:attribute>
									<xsd:attribute name="before-invocation" type="xsd:boolean" use="optional">
										<xsd:annotation>
											<xsd:documentation><![CDATA[
	Whether the eviction should occur after the method is successfully
	invoked (default) or before.]]></xsd:documentation>
										</xsd:annotation>
									</xsd:attribute>

								</xsd:extension>
							</xsd:complexContent>
						</xsd:complexType>
					</xsd:element>
				  </xsd:choice>
				</xsd:sequence>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

</xsd:schema>
