﻿--------------------------------------------------------
本站资源均为测试用，请于下载后24小时内删除！
禁止用于商业用途！！！！
我们只保证测试进游戏，游戏内内容细节等，有偿提供帮助
资源搜集自互联网，如果侵权，请联系马上删除
QQ:**********	www.ruankor.com		
Q群：*********（失效请从网站相关位置加入）

****架设软件QQ群文件内或论坛顶部架设工具自行免费下载****
--------------------------------------------------------

centos 7.X
安装宝塔
yum install -y wget && wget -O install.sh http://download.bt.cn/install/install_6.0.sh && sh install.sh



Nginx1.18
MySQL5.7
PHP5.4  
mongodb


放行端口 1:65535
关闭防火墙
systemctl stop firewalld.service  
systemctl disable firewalld.service


安装java
yum install java-1.8.0-openjdk* -y



上传hwfz.zip到根目录解压

cd /
unzip hwfz.zip

chmod -R 777 /home/<USER>
chmod -R 777 /www/wwwroot/game


修改数据库密码123456

创建数据库accounts，导入accounts.sql

Mongodb设置

执行
mongo
use hwfz
db.createUser({user:"admin",pwd:"admin",roles:[{role:"root",db:"admin"}]})
exit

创建网站 端口 8001
目录 /www/wwwroot/game



修改 目录下 \www\wwwroot\game\gameserverlist.txt文件



启动
cd /home/<USER>
sh start.sh

关闭
cd /home/<USER>
sh stop.sh


授权物品后台  IP:8001/gm/gm.php

玩家后台   IP:8001/gm/
GM码 ruankor.com

安卓客户端修改
\assets\version.conf
\assets\conf\gameconfig   解密，改好加密保存替换
苹果客户端修改
\Payload\slg.app\Data\Raw\version.conf
\Payload\slg.app\Data\Raw\conf\gameconfig



----------------------------------------------------------------
海量源码资源网https://www.ruankor.com

最好的导航站：https://hao.ruankor.com

小游戏中心：http://game.ruankor.com

----------------------------------------------------------------
【百度一下】 软壳源码

客服Q：**********
QQ交流群：*********(失效请从网站相关入口加入)
----------------------------------------------------------------
附：
   根据二○○二年一月一日《计算机软件保护条例》规定：为了学习和
   研究软件内含的设计思想和原理，通过安装、显示、传输或者存储软
   件等方式使用软件的，可以不经软件著作权人许可，不向其支付报酬
   鉴于此，也希望大家按此说明研究软件

本站所有源码都来源于网络收集修改或者交换!如果侵犯了您的权益,请及时告知我们,我们即刻删除

