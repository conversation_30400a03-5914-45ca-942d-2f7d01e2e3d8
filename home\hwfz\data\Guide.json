[{"dRPF": "开始引导", "nextId": 2, "nextIndex": 0, "id": 1}, {"dRPF": "", "nextId": 3, "nextIndex": 0, "id": 2}, {"dRPF": "", "nextId": 4, "nextIndex": 0, "id": 3}, {"dRPF": "", "nextId": 5, "nextIndex": 0, "id": 4}, {"dRPF": "建造农田", "nextId": 6, "nextIndex": 6, "id": 5}, {"dRPF": "", "nextId": 7, "nextIndex": 0, "id": 6}, {"dRPF": "步兵营", "nextId": 8, "nextIndex": 0, "id": 7}, {"dRPF": "", "nextId": 9, "nextIndex": 9, "id": 8}, {"dRPF": "弓兵营", "nextId": 10, "nextIndex": 0, "id": 9}, {"dRPF": "", "nextId": 11, "nextIndex": 11, "id": 10}, {"dRPF": "骑兵营", "nextId": 12, "nextIndex": 0, "id": 11}, {"dRPF": "", "nextId": 13, "nextIndex": 13, "id": 12}, {"dRPF": "升级主殿", "nextId": 14, "nextIndex": 0, "id": 13}, {"dRPF": "", "nextId": 15, "nextIndex": 0, "id": 14}, {"dRPF": "", "nextId": 16, "nextIndex": 0, "id": 15}, {"dRPF": "引导结束", "nextId": 17, "nextIndex": 50, "id": 16}, {"dRPF": "", "nextId": 50, "nextIndex": 0, "id": 17}, {"dRPF": "", "nextId": 51, "nextIndex": 0, "id": 50}, {"dRPF": "", "nextId": 52, "nextIndex": 0, "id": 51}, {"dRPF": "", "nextId": 53, "nextIndex": 0, "id": 52}, {"dRPF": "", "nextId": 54, "nextIndex": 0, "id": 53}, {"dRPF": "", "nextId": 55, "nextIndex": 0, "id": 54}, {"dRPF": "", "nextId": 56, "nextIndex": 0, "id": 55}, {"dRPF": "", "nextId": 93, "nextIndex": 0, "id": 56}, {"dRPF": "", "nextId": 57, "nextIndex": 0, "id": 93}, {"dRPF": "", "nextId": 58, "nextIndex": 0, "id": 57}, {"dRPF": "使用招募令", "nextId": 48, "nextIndex": 0, "id": 58}, {"dRPF": "", "nextId": 47, "nextIndex": 0, "id": 48}, {"dRPF": "", "nextId": 60, "nextIndex": 0, "id": 47}, {"dRPF": "答题", "nextId": 61, "nextIndex": 0, "id": 60}, {"dRPF": "", "nextId": 62, "nextIndex": 0, "id": 61}, {"dRPF": "", "nextId": 63, "nextIndex": 64, "id": 62}, {"dRPF": "", "nextId": 64, "nextIndex": 0, "id": 63}, {"dRPF": "", "nextId": 65, "nextIndex": 0, "id": 64}, {"dRPF": "", "nextId": 46, "nextIndex": -1, "id": 65}, {"dRPF": "", "nextId": 66, "nextIndex": 0, "id": 46}, {"dRPF": "", "nextId": 67, "nextIndex": 0, "id": 66}, {"dRPF": "", "nextId": 68, "nextIndex": 0, "id": 67}, {"dRPF": "", "nextId": 69, "nextIndex": 0, "id": 68}, {"dRPF": "", "nextId": 70, "nextIndex": 0, "id": 69}, {"dRPF": "", "nextId": 71, "nextIndex": 0, "id": 70}, {"dRPF": "", "nextId": 72, "nextIndex": 0, "id": 71}, {"dRPF": "", "nextId": 0, "nextIndex": 0, "id": 72}, {"dRPF": "", "nextId": 201, "nextIndex": 0, "id": 200}, {"dRPF": "", "nextId": 202, "nextIndex": 0, "id": 201}, {"dRPF": "", "nextId": 203, "nextIndex": 0, "id": 202}, {"dRPF": "", "nextId": 204, "nextIndex": 0, "id": 203}, {"dRPF": "", "nextId": 205, "nextIndex": 0, "id": 204}, {"dRPF": "", "nextId": 206, "nextIndex": 0, "id": 205}, {"dRPF": "", "nextId": 207, "nextIndex": 0, "id": 206}, {"dRPF": "", "nextId": 0, "nextIndex": 0, "id": 207}, {"dRPF": "", "nextId": 261, "nextIndex": 0, "id": 260}, {"dRPF": "", "nextId": 0, "nextIndex": 0, "id": 261}, {"dRPF": "", "nextId": 301, "nextIndex": 0, "id": 300}, {"dRPF": "", "nextId": 302, "nextIndex": 0, "id": 301}, {"dRPF": "", "nextId": 303, "nextIndex": 0, "id": 302}, {"dRPF": "", "nextId": 0, "nextIndex": 0, "id": 303}, {"dRPF": "", "nextId": 401, "nextIndex": 0, "id": 400}, {"dRPF": "", "nextId": 402, "nextIndex": 0, "id": 401}, {"dRPF": "", "nextId": 403, "nextIndex": 0, "id": 402}, {"dRPF": "", "nextId": 404, "nextIndex": 0, "id": 403}, {"dRPF": "", "nextId": 405, "nextIndex": 0, "id": 404}, {"dRPF": "", "nextId": 406, "nextIndex": 0, "id": 405}, {"dRPF": "", "nextId": 0, "nextIndex": 0, "id": 406}, {"dRPF": "", "nextId": 451, "nextIndex": 0, "id": 450}, {"dRPF": "", "nextId": 0, "nextIndex": 0, "id": 451}, {"dRPF": "", "nextId": 501, "nextIndex": 0, "id": 500}, {"dRPF": "", "nextId": 502, "nextIndex": 0, "id": 501}, {"dRPF": "", "nextId": 503, "nextIndex": 0, "id": 502}, {"dRPF": "", "nextId": 0, "nextIndex": 0, "id": 503}, {"dRPF": "", "nextId": 0, "nextIndex": 0, "id": 600}, {"dRPF": "", "nextId": 701, "nextIndex": 0, "id": 700}, {"dRPF": "", "nextId": 0, "nextIndex": 0, "id": 701}, {"dRPF": "", "nextId": 0, "nextIndex": 0, "id": 750}, {"dRPF": "", "nextId": 801, "nextIndex": 0, "id": 800}, {"dRPF": "", "nextId": 802, "nextIndex": 0, "id": 801}, {"dRPF": "", "nextId": 803, "nextIndex": 0, "id": 802}, {"dRPF": "", "nextId": 0, "nextIndex": 0, "id": 803}, {"dRPF": "", "nextId": 901, "nextIndex": 0, "id": 900}, {"dRPF": "", "nextId": 902, "nextIndex": 0, "id": 901}, {"dRPF": "", "nextId": 903, "nextIndex": 0, "id": 902}, {"dRPF": "", "nextId": 0, "nextIndex": 0, "id": 903}, {"dRPF": "", "nextId": 1001, "nextIndex": 0, "id": 1000}, {"dRPF": "", "nextId": 1002, "nextIndex": 0, "id": 1001}, {"dRPF": "", "nextId": 1003, "nextIndex": 0, "id": 1002}, {"dRPF": "", "nextId": 1004, "nextIndex": 0, "id": 1003}, {"dRPF": "", "nextId": 1005, "nextIndex": 0, "id": 1004}, {"dRPF": "", "nextId": 1006, "nextIndex": 0, "id": 1005}, {"dRPF": "", "nextId": 1007, "nextIndex": 0, "id": 1006}, {"dRPF": "", "nextId": 0, "nextIndex": 0, "id": 1007}, {"dRPF": "", "nextId": 1101, "nextIndex": 0, "id": 1100}, {"dRPF": "", "nextId": 1102, "nextIndex": 0, "id": 1101}, {"dRPF": "", "nextId": 1103, "nextIndex": 0, "id": 1102}, {"dRPF": "", "nextId": 1104, "nextIndex": 0, "id": 1103}, {"dRPF": "", "nextId": 0, "nextIndex": 0, "id": 1104}, {"dRPF": "", "nextId": 86, "nextIndex": 0, "id": 85}, {"dRPF": "", "nextId": 87, "nextIndex": 0, "id": 86}, {"dRPF": "", "nextId": 88, "nextIndex": 0, "id": 87}, {"dRPF": "", "nextId": 89, "nextIndex": -1, "id": 88}, {"dRPF": "", "nextId": 0, "nextIndex": 0, "id": 89}, {"dRPF": "", "nextId": 1201, "nextIndex": 0, "id": 1200}, {"dRPF": "", "nextId": 1202, "nextIndex": 0, "id": 1201}, {"dRPF": "", "nextId": 1203, "nextIndex": 0, "id": 1202}, {"dRPF": "", "nextId": 1204, "nextIndex": 0, "id": 1203}, {"dRPF": "", "nextId": 1205, "nextIndex": 0, "id": 1204}, {"dRPF": "", "nextId": 0, "nextIndex": 0, "id": 1205}, {"dRPF": "", "nextId": 1301, "nextIndex": 0, "id": 1300}, {"dRPF": "", "nextId": 1302, "nextIndex": 0, "id": 1301}, {"dRPF": "", "nextId": 1303, "nextIndex": 0, "id": 1302}, {"dRPF": "", "nextId": 0, "nextIndex": 0, "id": 1303}, {"dRPF": "", "nextId": 1401, "nextIndex": 0, "id": 1400}, {"dRPF": "", "nextId": 1402, "nextIndex": 0, "id": 1401}, {"dRPF": "", "nextId": 0, "nextIndex": 0, "id": 1402}, {"dRPF": "", "nextId": 1501, "nextIndex": 0, "id": 1500}, {"dRPF": "", "nextId": 1502, "nextIndex": 0, "id": 1501}, {"dRPF": "", "nextId": 0, "nextIndex": 0, "id": 1502}]