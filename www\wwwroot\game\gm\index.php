<?php
$t = time ();
?>
<!DOCTYPE html>
<html>   
<?php include 'head.php';?>    
<body>
 <div class="container">
   <br>
   <div class="row">
     <div class="container-fluid">
  <div class="modal-dialog">
    <div class="modal-content">
      <ul class="breadcrumb">				
				<li>
					 <b>汉王玩家后台</b>
				</li>				
			</ul>
      <div class="modal-body">
   <div class="form-horizontal" role="form">
                <div class="form-group">
                    <div class="col-sm-10">
                        <input type="password" id="pwd" name="pwd" class="form-control" maxlength="16" value="" placeholder="输入授权密码" required>
                    </div>
                </div>
				<div class="form-group">
                    <div class="col-sm-10">
                        <select id="qu" name="qu" class="form-control selectpicker" data-size="5" title="请选择区服" required>
						<?php
						include_once './user/config.php';
						foreach($quarr as $key=>$value){
							if($value['hidde']!=true){
								echo '<option value="'.$key.'">'.$value['name'].'</option>';
						}
						}
						?>
                        </select>
                    </div>
                </div>
				<div class="form-group">
                    <div class="col-sm-10">
                        <input type="text" id="uid" name="uid" class="form-control" value="" placeholder="请输入角色名" required>
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-sm-10">					
                   	<input id="chargenum" name="chargenum" class="form-control selectpicker" data-size="5" value="" placeholder="请填写金额" required>
                    			
                    </div>
                </div>		
                <div class="form-group">
                    <div class=" col-sm-10">						
						<button type="submit" class="btn btn-danger btn-block" onclick="chargebtn()">角色充值</button>					
                    </div>					
                </div>				
				<div class="form-group">
				<div class="col-sm-10">	
				<select id="type" name="type" class="form-control selectpicker" data-size="5" title="请选择邮件类型" required>
            <option value="1">资源</option>
            <option value="2">道具</option>
				</select>
				</div>	
				<div class="clear"></div>
				</div>
				<div hidden="hidden" class="form-group resource">
				<div class="col-sm-10">		
				<select id="resourceid" name="resourceid" class="form-control selectpicker" data-size="5" title="选资源" required>
				<?php
                       $file = fopen("user/goods.txt", "r");
                       while(!feof($file))
                       {
                       $line=fgets($file);
                       $txts=explode(';',$line);
                       echo '<option value="'.$txts[0].'">'.$txts[1].'</option>';
                       }
                       fclose($file);
                       ?>						
                        </select>
				</div>	
				<div class="clear"></div>
				</div>
				
                <div hidden="hidden" class="form-group daoju">
                     <div class="col-sm-10">
                        <select id="mailid" name="mailid" class="selectpicker show-tick form-control" data-live-search="true" data-size="5" title="选物品">
                        <?php
                       $file = fopen("user/item.txt", "r");
                       while(!feof($file))
                       {
                       $line=fgets($file);
                       $txts=explode(';',$line);
                       echo '<option value="'.$txts[0].'">'.$txts[1].'</option>';
                       }
                       fclose($file);
                       ?>						
                        </select>
                    </div>
                </div>
				<div class="form-group">
                    <div class="col-sm-10">					    
                        <input type="text" id="mailnum" name="mailnum" class="form-control" min="0" max="9999" value="" placeholder="数量" required>
                    </div>
                </div>				
                <div class="form-group">
                    <div class="col-sm-10">						
						<button type="submit" class="btn btn-warning" onclick="send_resource()">发送资源</button>
						
						<button type="submit" class="btn btn-warning" onclick="send_mail()">发送道具</button>                                                
                        <button type="submit" class="btn btn-info" onclick="shuoming()">使用必看</button> 						
                    </div>					
                </div>  
                <div class="form-group">
                    <div class="col-sm-10">
                        <select id="hero" name="hero" class="form-control selectpicker" data-size="5" title="请选择武将" required>
                         <?php
                       $file = fopen("user/hero.txt", "r");
                       while(!feof($file))
                       {
                       $line=fgets($file);
                       $txts=explode(';',$line);
                       echo '<option value="'.$txts[0].'">'.$txts[1].'</option>';
                       }
                       fclose($file);
                       ?>	
                        </select>
                    </div>
                </div>				
                <div class="form-group">
                    <div class="col-sm-10">						
						<button type="submit" class="btn btn-success" onclick="hero()">发送武将</button>                                                			
                    </div>					
                </div> 				
            </div>
      </div>
    </div>
  </div>
     </div>
   </div>
 </div>
 <script src="js/playermsg.js?v=<?php echo $t;?>"></script>
</body>
</html>