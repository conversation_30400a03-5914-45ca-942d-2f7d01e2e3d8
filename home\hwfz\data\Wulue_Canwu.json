[{"extra_prize": "13140010,2;13140020,2;13140030,2;13140040,2;13140050,2;13140060,2;13140070,2;13140080,2;13140090,2;13140100,2;13140110,2;13140120,2;13140130,2;13140140,2;13140150,2;13140160,2;13140170,2;13140180,2;13140190,2;13140200,2;13140210,2;13140220,2;13140240,2;13140250,2;13140260,2;13140270,2;13140280,2;13140230,2", "show_get": "13140010,13140020,13140030,13140040,13140050,13140060,13140070,13140080,13140090,13140100,13140110,13140120,13140130,13140140,13140150,13140160,13140170,13140180,13140190,13140200,13140210,13140220,13140240,13140250,13140260,13140270,13140280,13140230,13130010,13130020,13130030,13130040,13130050,13130060,13130070,13130080,13130090,13130100,13130110,13130120,13130130,13130140,13130150,13130160,13130170,13130180,13130190,13130200,13130210,13130220,13130240,13130250,13130260,13130270,13130280,13130230,13120010,13120020,13120030,13120040,13120050,13120060,13120070,13120080,13120090,13120100,13120110,13120120,13120130,13120140,13120150,13120160,13120170,13120180,13120190,13120200,13120210,13120220,13120240,13120250,13120260,13120270,13120280,13120230,13110010,13110020,13110030,13110040,13110050,13110060,13110070,13110080,13110090,13110100,13110110,13110120,13110130,13110140,13110150,13110160,13110170,13110180,13110190,13110200,13110210,13110220,13110240,13110250,13110260,13110270,13110280,13110230", "refresh_time": 300, "max": 40, "pro": "13140010,2;13140020,2;13140030,2;13140040,2;13140050,2;13140060,2;13140070,2;13140080,2;13140090,2;13140100,2;13140110,2;13140120,2;13140130,2;13140140,2;13140150,2;13140160,2;13140170,2;13140180,2;13140190,2;13140200,2;13140210,2;13140220,2;13140240,2;13140250,2;13140260,2;13140270,2;13140280,2;13140230,2;13130010,50;13130020,50;13130030,50;13130040,50;13130050,50;13130060,50;13130070,50;13130080,50;13130090,50;13130100,50;13130110,50;13130120,50;13130130,50;13130140,50;13130150,50;13130160,50;13130170,50;13130180,50;13130190,50;13130200,50;13130210,50;13130220,50;13130240,50;13130250,50;13130260,50;13130270,50;13130280,50;13130230,50;13120010,200;13120020,200;13120030,200;13120040,200;13120050,200;13120060,200;13120070,200;13120080,200;13120090,200;13120100,200;13120110,200;13120120,200;13120130,200;13120140,200;13120150,200;13120160,200;13120170,200;13120180,200;13120190,200;13120200,200;13120210,200;13120220,200;13120240,200;13120250,200;13120260,200;13120270,200;13120280,200;13120230,200;13110010,250;13110020,250;13110030,250;13110040,250;13110050,250;13110060,250;13110070,250;13110080,250;13110090,250;13110100,250;13110110,250;13110120,250;13110130,250;13110140,250;13110150,250;13110160,250;13110170,250;13110180,250;13110190,250;13110200,250;13110210,250;13110220,250;13110240,250;13110250,250;13110260,250;13110270,250;13110280,250;13110230,250", "ten_cost": "1000005,100000", "cost": "1000005,10000", "free_time": 5, "extra_time": 300, "id": 1}, {"extra_prize": "13150010,2;13150020,2;13150030,2;13150040,2;13150050,2;13150060,2;13150070,2;13150080,2;13150090,2;13150100,2;13150110,2;13150120,2;13150130,2;13150140,2;13150150,2;13150160,2;13150170,2;13150180,2;13150190,2;13150200,2;13150210,2;13150220,2;13150240,2;13150250,2;13150260,2;13150270,2;13150280,2;13150230,2;13140010,18;13140020,18;13140030,18;13140040,18;13140050,18;13140060,18;13140070,18;13140080,18;13140090,18;13140100,18;13140110,18;13140120,18;13140130,18;13140140,18;13140150,18;13140160,18;13140170,18;13140180,18;13140190,18;13140200,18;13140210,18;13140220,18;13140240,18;13140250,18;13140260,18;13140270,18;13140280,18;13140230,18", "show_get": "13150010,13150020,13150030,13150040,13150050,13150060,13150070,13150080,13150090,13150100,13150110,13150120,13150130,13150140,13150150,13150160,13150170,13150180,13150190,13150200,13150210,13150220,13150240,13150250,13150260,13150270,13150280,13150230,13140010,13140020,13140030,13140040,13140050,13140060,13140070,13140080,13140090,13140100,13140110,13140120,13140130,13140140,13140150,13140160,13140170,13140180,13140190,13140200,13140210,13140220,13140240,13140250,13140260,13140270,13140280,13140230,13130010,13130020,13130030,13130040,13130050,13130060,13130070,13130080,13130090,13130100,13130110,13130120,13130130,13130140,13130150,13130160,13130170,13130180,13130190,13130200,13130210,13130220,13130240,13130250,13130260,13130270,13130280,13130230,13120010,13120020,13120030,13120040,13120050,13120060,13120070,13120080,13120090,13120100,13120110,13120120,13120130,13120140,13120150,13120160,13120170,13120180,13120190,13120200,13120210,13120220,13120240,13120250,13120260,13120270,13120280,13120230,13110010,13110020,13110030,13110040,13110050,13110060,13110070,13110080,13110090,13110100,13110110,13110120,13110130,13110140,13110150,13110160,13110170,13110180,13110190,13110200,13110210,13110220,13110240,13110250,13110260,13110270,13110280,13110230", "refresh_time": 0, "max": 500, "pro": "13150010,2;13150020,2;13150030,2;13150040,2;13150050,2;13150060,2;13150070,2;13150080,2;13150090,2;13150100,2;13150110,2;13150120,2;13150130,2;13150140,2;13150150,2;13150160,2;13150170,2;13150180,2;13150190,2;13150200,2;13150210,2;13150220,2;13150240,2;13150250,2;13150260,2;13150270,2;13150280,2;13150230,2;13140010,18;13140020,18;13140030,18;13140040,18;13140050,18;13140060,18;13140070,18;13140080,18;13140090,18;13140100,18;13140110,18;13140120,18;13140130,18;13140140,18;13140150,18;13140160,18;13140170,18;13140180,18;13140190,18;13140200,18;13140210,18;13140220,18;13140240,18;13140250,18;13140260,18;13140270,18;13140280,18;13140230,18;13130010,300;13130020,300;13130030,300;13130040,300;13130050,300;13130060,300;13130070,300;13130080,300;13130090,300;13130100,300;13130110,300;13130120,300;13130130,300;13130140,300;13130150,300;13130160,300;13130170,300;13130180,300;13130190,300;13130200,300;13130210,300;13130220,300;13130240,300;13130250,300;13130260,300;13130270,300;13130280,300;13130230,300;13120010,150;13120020,150;13120030,150;13120040,150;13120050,150;13120060,150;13120070,150;13120080,150;13120090,150;13120100,150;13120110,150;13120120,150;13120130,150;13120140,150;13120150,150;13120160,150;13120170,150;13120180,150;13120190,150;13120200,150;13120210,150;13120220,150;13120240,150;13120250,150;13120260,150;13120270,150;13120280,150;13120230,150;13110010,30;13110020,30;13110030,30;13110040,30;13110050,30;13110060,30;13110070,30;13110080,30;13110090,30;13110100,30;13110110,30;13110120,30;13110130,30;13110140,30;13110150,30;13110160,30;13110170,30;13110180,30;13110190,30;13110200,30;13110210,30;13110220,30;13110240,30;13110250,30;13110260,30;13110270,30;13110280,30;13110230,30", "ten_cost": "82005000,10", "cost": "82005000,1", "free_time": 0, "extra_time": 10, "id": 2}]