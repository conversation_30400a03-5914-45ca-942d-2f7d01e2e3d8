<!--

     Licensed to the Apache Software Foundation (ASF) under one
     or more contributor license agreements.  See the NOTICE file
     distributed with this work for additional information
     regarding copyright ownership.  The ASF licenses this file
     to you under the Apache License, Version 2.0 (the
     "License"); you may not use this file except in compliance
     with the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing,
     software distributed under the License is distributed on an
     "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
     KIND, either express or implied.  See the License for the
     specific language governing permissions and limitations
     under the License.

-->
<html>
  <head>
    <title>package groovy.time.*</title>
  </head>
  <body>
    <p>
      Classes for easily manipulating Dates and times.  While 
      <code>java.util.Date</code> has GDK methods for adding or subtracting days,
      this is not so useful for different durations of time.  
      {@link groovy.time.TimeCategory TimeCategory} creates a simple internal DSL
      for manipulating dates and times in a clean and precise fashion.  
    </p>
    <h3>Examples</h3>
    <pre>
  use ( TimeCategory ) {
  	// application on numbers:
  	println 1.minute.from.now
  	println 10.days.ago
  
  	// application on dates
  	def someDate = new Date()
  	println someDate - 3.months 
  }</pre>
     
    @see groovy.time.TimeCategory
  </body>
</html>
