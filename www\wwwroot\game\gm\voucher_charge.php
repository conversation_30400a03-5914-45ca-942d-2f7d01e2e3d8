<?php
include 'user/config.php';

if ($_POST) {
    $checknum = trim($_POST['checknum']);
    $quid = trim($_POST['qu']);
    $qu = $quarr[$quid];
    $uid = trim($_POST['uid']);
    $pay_id = trim($_POST['pay_id']);
    $url = $qu['url'];
    
    if ($checknum == $gmcode) {
        if ($quid >= 1) {
            if ($uid != '') {
                // 读取充值配置
                $pay_config = json_decode(file_get_contents('../../home/<USER>/data/Pay.json'), true);
                $selected_pay = null;
                
                foreach ($pay_config as $pay_item) {
                    if ($pay_item['id'] == $pay_id) {
                        $selected_pay = $pay_item;
                        break;
                    }
                }
                
                if (!$selected_pay) {
                    exit('充值配置错误');
                }
                
                // 检查玩家是否有足够的代金券
                $voucher_id = 82006051; // 代金券ID
                $voucher_needed = isset($selected_pay['cost_item_num']) ? $selected_pay['cost_item_num'] : $selected_pay['paynum'];
                
                // 先检查玩家代金券数量
                $check_pot = 'gmhandler?func=get_player_item&args=' . $uid . ';;' . $voucher_id;
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $url . $check_pot);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                $check_result = curl_exec($ch);
                curl_close($ch);
                
                $player_vouchers = json_decode($check_result, true);
                $current_vouchers = isset($player_vouchers['count']) ? $player_vouchers['count'] : 0;
                
                if ($current_vouchers < $voucher_needed) {
                    exit('代金券不足，需要' . $voucher_needed . '张，当前只有' . $current_vouchers . '张');
                }
                
                // 扣除代金券
                $deduct_pot = 'gmhandler?func=remove_player_item&args=' . $uid . ';;' . $voucher_id . ';;' . $voucher_needed . ';;' . urlencode('充值消耗代金券');
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $url . $deduct_pot);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                $deduct_result = curl_exec($ch);
                curl_close($ch);
                
                // 增加元宝/玉石
                $money_to_add = $selected_pay['money'] + $selected_pay['moneyextra'];
                if ($selected_pay['firstplus'] > 0) {
                    $money_to_add += $selected_pay['firstplus']; // 首充奖励
                }
                
                $charge_pot = 'gmhandler?func=change_role_diamond&args=' . $uid . ';;' . $money_to_add . ';;' . urlencode('代金券充值') . ';;' . urlencode('使用代金券充值获得' . $money_to_add . '元宝');
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $url . $charge_pot);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                $charge_result = curl_exec($ch);
                curl_close($ch);
                
                $result = json_decode($charge_result, true);
                if (isset($result['massage']) && $result['massage'] == 0) {
                    exit('代金券充值成功！消耗' . $voucher_needed . '张代金券，获得' . $money_to_add . '元宝');
                } else {
                    // 充值失败，返还代金券
                    $refund_pot = 'gmhandler?func=add_player_item&args=' . $uid . ';;' . $voucher_id . ';;' . $voucher_needed . ';;' . urlencode('充值失败返还');
                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL, $url . $refund_pot);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                    curl_exec($ch);
                    curl_close($ch);
                    
                    exit('充值失败，已返还代金券');
                }
            } else {
                exit('角色ID不能为空');
            }
        } else {
            exit('请选择区服');
        }
    } else {
        exit('GM校验码错误');
    }
} else {
    exit('请求方式错误');
}
?>
