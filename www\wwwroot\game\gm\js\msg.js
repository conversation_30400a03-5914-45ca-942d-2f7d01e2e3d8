var checknum = '';
var uid = '';
var qu = $('#qu').val();
$('#checknum').change(function() {
    checknum = $(this).val();
});
  $('#uid').change(function(){
	  uid=$.trim($(this).val());
  });
  $('#qu').change(function(){
	  qu=$.trim($(this).val());
  });
$(".selectpicker").selectpicker({
    header:'请选择',
    showIcon:true,
    multipleSeparator:'#',
    maxOptions:4,
    maxOptionsText:'最多选4个',
});
$('#type').change(function(){
		typeid=$.trim($(this).val());
		console.log(typeid)
		if(typeid == "1"){
			$(".resource").show();
			$(".daoju").hide();
		}else if(typeid == "2"){
			$(".daoju").show();
			$(".resource").hide();
		}
	});
/**
角色充值
*/
function chargebtn() {
	if (checknum == '') {
        layer.msg('请输入GM校验码');
        return false;
    }

	var str = uid;
    var r = /^[0-9]{5,8}$/; // 正整数
    if (r.test(str)) {
        console.log("dui----" + str);
    } else {
        layer.msg('角色ID只能是5-8位数字');
		return false;
    }
	  if(uid==''){
		  layer.msg('角色ID不能为空');
		  return false;
	  }
	  
	  var chargenum=$('#chargenum').val();
	  if(chargenum==''){
		  layer.msg('充值数量不能为空');
		  return false;
	  }	 
	$.ajaxSetup({
		contentType: "application/x-www-form-urlencoded; charset=utf-8"
	});
	$.post("user/gmquery.php", {
		type:'charge',uid:uid,num:chargenum,qu:qu,checknum:checknum		
	},
	function(data) {
		//console.log('data',data);
		layer.msg(data);
		
	});
}
function shouquanbtn() {
	if (checknum == '') {
        layer.msg('请输入GM校验码');
        return false;
    }
	  if(uid==''){
		  layer.msg('角色ID不能为空');
		  return false;
	  }
	  var pwd = $('#pwd').val();
	  if(pwd==''){
		  layer.msg('玩家后台的授权密码不能为空');
		  return false;
	  }	 
	$.ajaxSetup({
		contentType: "application/x-www-form-urlencoded; charset=utf-8"
	});
	$.post("user/gmquery.php", {
		type:'addczvip',uid:uid,pwd:pwd,qu:qu,checknum:checknum		
	},
	function(data) {
		//console.log('data',data);
		layer.msg(data);
		
	});
}
/**
发道具邮件
*/
function send_mail() {
	if (typeid == '1') {
        layer.msg('类型不选对,你给你爹发你妈呢?');
        return false;
    }		
	if (checknum == '') {
        layer.msg('请输入GM校验码');
        return false;
    }
	var str = uid;
    var r = /^[\u2E80-\u9FFF]+$/; // 汉字
    if (r.test(str)) {
        console.log("dui----" + str);
    } else {
        layer.msg('发送道具时角色名只能是汉字');
		return false;
    }
	  if(uid==''){
		  layer.msg('角色ID不能为空');
		  return false;
	  }
	if(typeid == "2"){
		var mailid=$('#mailid').val();
		//console.log(itemid2)
		var mailnum=$('#mailnum').val();
		if(mailid=='' || mailid=='0'){
		  alert('请选择物品。');
		  return false;
		}	  
	  if(mailnum<1 || mailnum>200000000){
		  layer.msg('道具数量范围:1-200000000');
		  return false;
	  }
	$.ajaxSetup({
		contentType: "application/x-www-form-urlencoded; charset=utf-8"
	});
	$.post("user/gmquery.php", {
		type:'daoju',uid:uid,item:mailid,num:mailnum,qu:qu,checknum:checknum		
	},
	function(data) {
		layer.msg(data);
		
	});
	
}
}
/**
发资源邮件
*/
function send_resource() {
	if (typeid == '2') {
        layer.msg('类型不选对,你给你爹发你妈呢?');
        return false;
    }	
	if (checknum == '') {
        layer.msg('请输入GM校验码');
        return false;
    }
	var str = uid;
    var r = /^[\u2E80-\u9FFF]+$/; // 汉字
    if (r.test(str)) {
        console.log("dui----" + str);
    } else {
        layer.msg('发送资源时角色名只能是汉字');
		return false;
    }
	
	  if(uid==''){
		  layer.msg('角色ID不能为空');
		  return false;
	  }
	if(typeid == "1"){
		var resourceid=$('#resourceid').val();
		//console.log(itemid2)
		var mailnum=$('#mailnum').val();
		if(resourceid=='' || resourceid=='0'){
		  alert('请选择资源。');
		  return false;
		}	  
	  if(mailnum<1 || mailnum>200000000){
		  layer.msg('道具数量范围:1-200000000');
		  return false;
	  }
	$.ajaxSetup({
		contentType: "application/x-www-form-urlencoded; charset=utf-8"
	});
	$.post("user/gmquery.php", {
		type:'resource',uid:uid,resource:resourceid,num:mailnum,qu:qu,checknum:checknum		
	},
	function(data) {
		layer.msg(data);
		
	});
	
}
}
//激活武将
function hero() {
	if (checknum == '') {
        layer.msg('请输入GM校验码');
        return false;
    }

	var str = uid;
    var r = /^\+?[1-9][0-9]*$/; // 正整数
    if (r.test(str) && (str <= 3000000)) {
        console.log("dui----" + str);
    } else {
        layer.msg('激活武将时角色ID只能是数字');
		return false;
    }
	  if(uid==''){
		  layer.msg('角色ID不能为空');
		  return false;
	  }
	  
	  var hero=$('#hero').val();
	  if(hero==''){
		  layer.msg('武将ID不能为空');
		  return false;
	  }	 
	$.ajaxSetup({
		contentType: "application/x-www-form-urlencoded; charset=utf-8"
	});
	$.post("user/gmquery.php", {
		type:'hero',uid:uid,hero:hero,qu:qu,checknum:checknum		
	},
	function(data) {
		//console.log('data',data);
		layer.msg(data);
		
	});
}
//武将结束
function shouquanbtn() {
	if (checknum == '') {
        layer.msg('请输入GM校验码');
        return false;
    }
	  if(uid==''){
		  layer.msg('角色ID不能为空');
		  return false;
	  }
	  var pwd = $('#pwd').val();
	  if(pwd==''){
		  layer.msg('玩家后台的授权密码不能为空');
		  return false;
	  }	 
	$.ajaxSetup({
		contentType: "application/x-www-form-urlencoded; charset=utf-8"
	});
	$.post("user/gmquery.php", {
		type:'addczvip',uid:uid,pwd:pwd,qu:qu,checknum:checknum		
	},
	function(data) {
		//console.log('data',data);
		layer.msg(data);
		
	});
}
//资源
function shouquanbtn1() {
	if (checknum == '') {
        layer.msg('请输入GM校验码');
        return false;
    }
	  if(uid==''){
		  layer.msg('角色ID不能为空');
		  return false;
	  }
	  var pwd = $('#pwd').val();
	  if(pwd==''){
		  layer.msg('玩家后台的授权密码不能为空');
		  return false;
	  }	 
	$.ajaxSetup({
		contentType: "application/x-www-form-urlencoded; charset=utf-8"
	});
	$.post("user/gmquery.php", {
		type:'addzyvip',uid:uid,pwd:pwd,qu:qu,checknum:checknum		
	},
	function(data) {
		//console.log('data',data);
		layer.msg(data);
		
	});
}
//道具
function shouquanbtn2() {
	if (checknum == '') {
        layer.msg('请输入GM校验码');
        return false;
    }
	  if(uid==''){
		  layer.msg('角色ID不能为空');
		  return false;
	  }
	  var pwd = $('#pwd').val();
	  if(pwd==''){
		  layer.msg('玩家后台的授权密码不能为空');
		  return false;
	  }	 
	$.ajaxSetup({
		contentType: "application/x-www-form-urlencoded; charset=utf-8"
	});
	$.post("user/gmquery.php", {
		type:'addvip',uid:uid,pwd:pwd,qu:qu,checknum:checknum		
	},
	function(data) {
		//console.log('data',data);
		layer.msg(data);
		
	});
}
function unshouquan() {
	if (checknum == '') {
        layer.msg('请输入GM校验码');
        return false;
    }
	  if(uid==''){
		  layer.msg('角色ID不能为空');
		  return false;
	  }
	$.ajaxSetup({
		contentType: "application/x-www-form-urlencoded; charset=utf-8"
	});
	$.post("user/gmquery.php", {
		type:'quxiaovip',uid:uid,qu:qu,checknum:checknum		
	},
	function(data) {
		//console.log('data',data);
		layer.msg(data);
		
	});
}
function shuoming() {
    layer.open({
    content: '后台说明：</br>1.充值无法领取充值奖励 </br> 2.充值请使用角色ID[纯数字]</br> 3.资源和道具请使用角色名[纯汉字]</br> 4.代金券充值需要玩家背包有足够代金券'
    ,btn: '我知道了'
  });
}

/**
代金券充值
*/
function voucherChargebtn() {
	if (checknum == '') {
        layer.msg('请输入GM校验码');
        return false;
    }

	var str = uid;
    var r = /^[0-9]{5,8}$/; // 正整数
    if (r.test(str)) {
        console.log("dui----" + str);
    } else {
        layer.msg('角色ID只能是5-8位数字');
		return false;
    }
	  if(uid==''){
		  layer.msg('角色ID不能为空');
		  return false;
	  }

	  var pay_package=$('#pay_package').val();
	  if(pay_package==''){
		  layer.msg('请选择充值套餐');
		  return false;
	  }
	$.ajaxSetup({
		contentType: "application/x-www-form-urlencoded; charset=utf-8"
	});
	$.post("voucher_charge.php", {
		uid:uid,pay_id:pay_package,qu:qu,checknum:checknum
	},
	function(data) {
		//console.log('data',data);
		layer.msg(data);

	});
}