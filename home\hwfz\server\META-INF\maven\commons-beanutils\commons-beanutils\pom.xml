<?xml version="1.0"?>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<project
    xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <groupId>org.apache.commons</groupId>
    <artifactId>commons-parent</artifactId>
    <version>33</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <groupId>commons-beanutils</groupId>
  <artifactId>commons-beanutils</artifactId>
  <version>1.9.2</version>
  <name>Apache Commons BeanUtils</name>

  <inceptionYear>2000</inceptionYear>
  <description>Apache Commons BeanUtils provides an easy-to-use but flexible wrapper around reflection and introspection.</description>

  <url>http://commons.apache.org/proper/commons-beanutils/</url>

  <issueManagement>
    <system>jira</system>
    <url>http://issues.apache.org/jira/browse/BEANUTILS</url>
  </issueManagement>

  <scm>
    <connection>scm:svn:http://svn.apache.org/repos/asf/commons/proper/beanutils/tags/BEANUTILS_1_9_2/</connection>
    <developerConnection>scm:svn:https://svn.apache.org/repos/asf/commons/proper/beanutils/tags/BEANUTILS_1_9_2/</developerConnection>
    <url>http://svn.apache.org/viewvc/commons/proper/beanutils/tags/BEANUTILS_1_9_2/</url>
  </scm>

  <distributionManagement>
    <site>
      <id>apache.website</id>
      <name>Apache Commons Beanutils Site</name>
      <url>scm:svn:https://svn.apache.org/repos/infra/websites/production/commons/content/proper/commons-beanutils</url>
    </site>
  </distributionManagement>

  <developers>
    <developer>
      <name>Robert Burrell Donkin</name>
      <id>rdonkin</id>
      <email><EMAIL></email>
      <organization>The Apache Software Foundation</organization>
    </developer>
    <developer>
      <name>Dion Gillard</name>
      <id>dion</id>
      <email><EMAIL></email>
      <organization>The Apache Software Foundation</organization>
    </developer>
    <developer>
      <name>Craig McClanahan</name>
      <id>craigmcc</id>
      <email><EMAIL></email>
      <organization>The Apache Software Foundation</organization>
    </developer>
    <developer>
      <name>Geir Magnusson Jr.</name>
      <id>geirm</id>
      <email><EMAIL></email>
      <organization>The Apache Software Foundation</organization>
    </developer>
    <developer>
      <name>Scott Sanders</name>
      <id>sanders</id>
      <email><EMAIL></email>
      <organization>The Apache Software Foundation</organization>
    </developer>
    <developer>
      <name>James Strachan</name>
      <id>jstrachan</id>
      <email><EMAIL></email>
      <organization>The Apache Software Foundation</organization>
    </developer>
    <developer>
      <name>Rodney Waldhoff</name>
      <id>rwaldhoff</id>
      <email><EMAIL></email>
      <organization>The Apache Software Foundation</organization>
    </developer>
    <developer>
      <name>Martin van den Bemt</name>
      <id>mvdb</id>
      <email><EMAIL></email>
      <organization>The Apache Software Foundation</organization>
    </developer>
    <developer>
      <name>Yoav Shapira</name>
      <id>yoavs</id>
      <email><EMAIL></email>
      <organization>The Apache Software Foundation</organization>
    </developer>
    <developer>
      <name>Niall Pemberton</name>
      <id>niallp</id>
      <email><EMAIL></email>
      <organization>The Apache Software Foundation</organization>
    </developer>
    <developer>
      <name>Simon Kitching</name>
      <id>skitching</id>
      <email><EMAIL></email>
      <organization>The Apache Software Foundation</organization>
    </developer>
    <developer>
      <name>James Carman</name>
      <id>jcarman</id>
      <email><EMAIL></email>
      <organization>The Apache Software Foundation</organization>
    </developer>
    <developer>
      <name>Benedikt Ritter</name>
      <id>britter</id>
      <email><EMAIL></email>
      <organization>The Apache Software Foundation</organization>
    </developer>
    <developer>
      <name>Tim O'Brien</name>
      <id>tobrien</id>
      <email><EMAIL></email>
      <organization>The Apache Software Foundation</organization>
    </developer>
    <developer>
      <name>David Eric Pugh</name>
      <id>epugh</id>
      <email><EMAIL></email>
      <organization>The Apache Software Foundation</organization>
    </developer>
    <developer>
      <name>Rodney Waldhoff</name>
      <id>rwaldhoff</id>
      <email><EMAIL></email>
      <organization>The Apache Software Foundation</organization>
    </developer>
    <developer>
      <name>Morgan James Delagrange</name>
      <id>morgand</id>
      <email><EMAIL></email>
      <organization>The Apache Software Foundation</organization>
    </developer>
    <developer>
      <name>John E. Conlon</name>
      <id>jconlon</id>
      <email><EMAIL></email>
      <organization>The Apache Software Foundation</organization>
    </developer>
    <developer>
      <name>Stephen Colebourne</name>
      <id>scolebourne</id>
      <email><EMAIL></email>
      <organization>The Apache Software Foundation</organization>
    </developer>
  </developers>

  <contributors>
    <contributor>
      <name>Paul Jack</name>
      <email></email>
    </contributor>
    <contributor>
      <name>Stephen Colebourne</name>
      <email></email>
    </contributor>
    <contributor>
      <name>Berin Loritsch</name>
      <email></email>
    </contributor>    
    <contributor>
      <name>Alex Crown</name>
      <email></email>
    </contributor>    
    <contributor>
      <name>Marcus Zander</name>
      <email></email>
    </contributor>
    <contributor>
      <name>Paul Hamamnt</name>
      <email></email>
    </contributor>
    <contributor>
      <name>Rune Johannesen</name>
      <email></email>
    </contributor>
    <contributor>
      <name>Clebert Suconic</name>
      <email></email>
    </contributor>
    <contributor>
      <name>Norm Deane</name>
      <email></email>
    </contributor>
    <contributor>
      <name>Ralph Schaer</name>
      <email></email>
    </contributor>
    <contributor>
      <name>Chris Audley</name>
      <email></email>
    </contributor>
    <contributor>
      <name>Rey Fran&#231;ois</name>
      <email></email>
    </contributor>
    <contributor>
      <name>Gregor Ra&#253;man</name>
      <email></email>
    </contributor>
    <contributor>
      <name>Jan Sorensen</name>
      <email></email>
    </contributor>
    <contributor>
      <name>Eric Pabst</name>
      <email></email>
    </contributor>
    <contributor>
      <name>Paulo Gaspar</name>
      <email></email>
    </contributor>
    <contributor>
      <name>Michael Smith</name>
      <email></email>
    </contributor>
    <contributor>
      <name>George Franciscus</name>
      <email></email>
    </contributor>
    <contributor>
      <name>Erik Meade</name>
      <email></email>
    </contributor>
    <contributor>
      <name>Tomas Viberg</name>
      <email></email>
    </contributor>
    <contributor>
      <name>Yauheny Mikulski</name>
      <email></email>
    </contributor>
    <contributor>
      <name>Michael Szlapa</name>
      <email></email>
    </contributor>
    <contributor>
      <name>Juozas Baliuka</name>
      <email></email>
    </contributor>
  </contributors>

  <dependencies>
    <dependency>
      <groupId>commons-logging</groupId>
      <artifactId>commons-logging</artifactId>
      <version>1.1.1</version>
    </dependency>
    <dependency>
      <groupId>commons-collections</groupId>
      <artifactId>commons-collections</artifactId> 
      <version>3.2.1</version> 
    </dependency>
    <dependency>
      <groupId>commons-collections</groupId>
      <artifactId>commons-collections-testframework</artifactId> 
      <version>3.2.1</version> 
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>3.8.1</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
              <forkMode>pertest</forkMode>
              <!-- limit memory size see BEANUTILS-291 -->
              <argLine>${surefire.argLine}</argLine>
              <includes>
                <include>**/*TestCase.java</include>
              </includes>
              <excludes>
                <!-- This test case is known to fail, and there isn't any proposed fix
                  -  so we will just exclude it until someone comes up with a solution.
                -->
                <exclude>**/*MemoryTestCase.java</exclude>
              </excludes>

              <!-- Configure Logging -->
              <redirectTestOutputToFile>true</redirectTestOutputToFile>
              <systemPropertyVariables>
                  <org.apache.commons.logging.LogFactory>org.apache.commons.logging.impl.LogFactoryImpl</org.apache.commons.logging.LogFactory>
                  <org.apache.commons.logging.Log>org.apache.commons.logging.impl.SimpleLog</org.apache.commons.logging.Log>>
                  <org.apache.commons.logging.simplelog.defaultlog>WARN</org.apache.commons.logging.simplelog.defaultlog>
              </systemPropertyVariables>

          </configuration>
        </plugin>
        <plugin>
            <artifactId>maven-assembly-plugin</artifactId>
            <configuration>
                <descriptors>
                    <descriptor>src/main/assembly/bin.xml</descriptor>
                    <descriptor>src/main/assembly/src.xml</descriptor>
                </descriptors>
                <tarLongFileMode>gnu</tarLongFileMode>
            </configuration>
        </plugin>
      </plugins>
      <pluginManagement>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-scm-publish-plugin</artifactId>
            <configuration>
              <!-- when moving to svnpubsub links to previous releases must be fixed -->
              <!-- ignore paths manual content -->
              <ignorePathsToDelete>
                <ignorePathToDelete>javadocs**</ignorePathToDelete>
                <ignorePathToDelete>release-notes**</ignorePathToDelete>
              </ignorePathsToDelete>
            </configuration>
          </plugin>
        </plugins>
      </pluginManagement>
    </build>

    <properties>
        <maven.compiler.source>1.5</maven.compiler.source>
        <maven.compiler.target>1.5</maven.compiler.target>
        <commons.componentid>beanutils</commons.componentid>
        <commons.release.version>1.9.2</commons.release.version>
        <commons.jira.id>BEANUTILS</commons.jira.id>
        <commons.jira.pid>12310460</commons.jira.pid>
        <!-- limit memory size see BEANUTILS-291; allow command-line override -->
        <!-- Originally 25M, increased to 50M to get round Continuum CI build failures -->
        <surefire.argLine>-Xmx50M</surefire.argLine>
    </properties> 

    <reporting>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>2.6</version>
                <configuration>
                    <configLocation>${basedir}/checkstyle.xml</configLocation>
                    <enableRulesSummary>false</enableRulesSummary>
                    <headerLocation>${basedir}/license-header.txt</headerLocation>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <configuration>
                    <linksource>true</linksource>
                    <links>
                        <link>http://download.oracle.com/javase/1.4.2/docs/api/</link>
                        <link>http://commons.apache.org/collections/api-release/</link>
                    </links>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-changes-plugin</artifactId>
                <version>2.9</version>
                <configuration>
                    <issueLinkTemplatePerSystem>
                        <default>%URL%/%ISSUE%</default>
                    </issueLinkTemplatePerSystem>
                </configuration>
                <reportSets>
                    <reportSet>
                        <reports>
                            <report>changes-report</report>
                        </reports>
                    </reportSet>
                </reportSets>
            </plugin>
        </plugins>
    </reporting>

</project>
