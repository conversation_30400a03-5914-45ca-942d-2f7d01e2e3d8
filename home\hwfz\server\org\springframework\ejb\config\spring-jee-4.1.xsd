<?xml version="1.0" encoding="UTF-8" standalone="no"?>

<xsd:schema xmlns="http://www.springframework.org/schema/jee"
		xmlns:xsd="http://www.w3.org/2001/XMLSchema"
		xmlns:beans="http://www.springframework.org/schema/beans"
		xmlns:tool="http://www.springframework.org/schema/tool"
		targetNamespace="http://www.springframework.org/schema/jee"
		elementFormDefault="qualified"
		attributeFormDefault="unqualified">

	<xsd:import namespace="http://www.springframework.org/schema/beans" schemaLocation="http://www.springframework.org/schema/beans/spring-beans-4.1.xsd"/>
	<xsd:import namespace="http://www.springframework.org/schema/tool" schemaLocation="http://www.springframework.org/schema/tool/spring-tool-4.1.xsd"/>

	<xsd:annotation>
		<xsd:documentation><![CDATA[
	Defines configuration elements for access to traditional Java EE components
	such as JNDI resources and EJB session beans.
		]]></xsd:documentation>
	</xsd:annotation>

	<xsd:element name="jndi-lookup">
		<xsd:annotation>
			<xsd:documentation source="java:org.springframework.jndi.JndiObjectFactoryBean"><![CDATA[
	Exposes an object reference via a JNDI lookup.
			]]></xsd:documentation>
		</xsd:annotation>
		<xsd:complexType>
			<xsd:complexContent>
				<xsd:extension base="jndiLocatingType">
					<xsd:attribute name="cache" type="xsd:boolean" default="true">
						<xsd:annotation>
							<xsd:documentation><![CDATA[
	Controls whether the object returned from the JNDI lookup is cached
	after the first lookup.
							]]></xsd:documentation>
						</xsd:annotation>
					</xsd:attribute>
					<xsd:attribute name="expected-type" type="xsd:string">
						<xsd:annotation>
							<xsd:documentation source="java:java.lang.Class"><![CDATA[
	The type that the located JNDI object is supposed to be assignable
	to, if indeed any.
							]]></xsd:documentation>
						</xsd:annotation>
					</xsd:attribute>
					<xsd:attribute name="lookup-on-startup" type="xsd:boolean" default="true">
						<xsd:annotation>
							<xsd:documentation><![CDATA[
	Controls whether the JNDI lookup is performed immediately on startup
	(if true, the default), or on first access (if false).
							]]></xsd:documentation>
						</xsd:annotation>
					</xsd:attribute>
					<xsd:attribute name="proxy-interface" type="xsd:string">
						<xsd:annotation>
							<xsd:documentation source="java:java.lang.Class"><![CDATA[
	The proxy interface to use for the JNDI object.

	Needs to be specified because the actual JNDI object type is not
	known in advance in case of a lazy lookup.

	Typically used in conjunction with "lookupOnStartup"=false and/or
	"cache"=false.
							]]></xsd:documentation>
							<xsd:appinfo>
								<tool:annotation>
									<tool:expected-type type="java.lang.Class"/>
								</tool:annotation>
							</xsd:appinfo>
						</xsd:annotation>
					</xsd:attribute>
					<xsd:attribute name="default-value" type="xsd:string">
						<xsd:annotation>
							<xsd:documentation><![CDATA[
	Specify a default literal value to fall back to if the JNDI lookup fails.
	This is typically used for literal values in scenarios where the JNDI environment
	might define specific config settings but those are not required to be present.

	Default is none. Note: This is only supported for lookup on startup.
							]]></xsd:documentation>
						</xsd:annotation>
					</xsd:attribute>
					<xsd:attribute name="default-ref" type="xsd:string">
						<xsd:annotation>
							<xsd:documentation><![CDATA[
	Specify a default bean reference to fall back to if the JNDI lookup fails.
	This might for example point to a local fallback DataSource.

	Default is none. Note: This is only supported for lookup on startup.
							]]></xsd:documentation>
						</xsd:annotation>
					</xsd:attribute>
				</xsd:extension>
			</xsd:complexContent>
		</xsd:complexType>
	</xsd:element>

	<xsd:element name="local-slsb" type="ejbType">
		<xsd:annotation>
			<xsd:documentation source="java:org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean"><![CDATA[
	Exposes a reference to a local EJB Stateless SessionBean.
			]]></xsd:documentation>
		</xsd:annotation>
	</xsd:element>

	<xsd:element name="remote-slsb">
		<xsd:annotation>
			<xsd:documentation source="java:org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean"><![CDATA[
	Exposes a reference to a remote EJB Stateless SessionBean.
			]]></xsd:documentation>
		</xsd:annotation>
		<xsd:complexType>
			<xsd:complexContent>
				<xsd:extension base="ejbType">
					<xsd:attribute name="home-interface" type="xsd:string">
						<xsd:annotation>
							<xsd:documentation source="java:java.lang.Class"><![CDATA[
	The home interface that will be narrowed to before performing
	the parameterless SLSB create() call that returns the actual
	SLSB proxy.
							]]></xsd:documentation>
						</xsd:annotation>
					</xsd:attribute>
					<xsd:attribute name="refresh-home-on-connect-failure" type="xsd:boolean" default="false">
						<xsd:annotation>
							<xsd:documentation><![CDATA[
	Controls whether to refresh the EJB home on connect failure.

	Can be turned on to allow for hot restart of the EJB server.
	If a cached EJB home throws an RMI exception that indicates a
	remote connect failure, a fresh home will be fetched and the
	invocation will be retried.
							]]></xsd:documentation>
						</xsd:annotation>
					</xsd:attribute>
					<xsd:attribute name="cache-session-bean" type="xsd:boolean" default="false">
						<xsd:annotation>
							<xsd:documentation><![CDATA[
	Controls whether to cache the actual session bean object.

	Off by default for standard EJB compliance. Turn this flag
	on to optimize session bean access for servers that are
	known to allow for caching the actual session bean object.
							]]></xsd:documentation>
						</xsd:annotation>
					</xsd:attribute>
				</xsd:extension>
			</xsd:complexContent>
		</xsd:complexType>
	</xsd:element>

	<!-- base types -->
	<xsd:complexType name="jndiLocatingType" abstract="true">
		<xsd:complexContent>
			<xsd:extension base="beans:identifiedType">
				<xsd:sequence>
					<xsd:element name="environment" minOccurs="0" maxOccurs="1">
						<xsd:annotation>
							<xsd:documentation><![CDATA[
	The newline-separated, key-value pairs for the JNDI environment
	(in standard Properties format, namely 'key=value' pairs)
						 ]]></xsd:documentation>
						</xsd:annotation>
						<xsd:simpleType>
							<xsd:restriction base="xsd:string"/>
						</xsd:simpleType>
					</xsd:element>
				</xsd:sequence>
				<xsd:attribute name="environment-ref" type="environmentRefType">
					<xsd:annotation>
						<xsd:documentation><![CDATA[
	A reference to JNDI environment properties, indicating the name of a
	shared bean of type [java.util.Properties}.
						]]></xsd:documentation>
					</xsd:annotation>
				</xsd:attribute>
				<xsd:attribute name="jndi-name" type="xsd:string" use="required">
					<xsd:annotation>
						<xsd:documentation><![CDATA[
	The JNDI name to look up. This may be a fully-qualified JNDI path
	or a local Java EE environment naming context path in which case the
	prefix "java:comp/env/" will be prepended if applicable.
						]]></xsd:documentation>
					</xsd:annotation>
				</xsd:attribute>
				<xsd:attribute name="resource-ref" type="xsd:boolean" default="true">
					<xsd:annotation>
						<xsd:documentation><![CDATA[
	Controls whether the lookup occurs in a Java EE container, i.e. if the
	prefix "java:comp/env/" needs to be added if the JNDI name doesn't
	already contain it. Default is "true" (since Spring 2.5).
						]]></xsd:documentation>
					</xsd:annotation>
				</xsd:attribute>
				<xsd:attribute name="expose-access-context" type="xsd:boolean" default="false">
					<xsd:annotation>
						<xsd:documentation><![CDATA[
	Set whether to expose the JNDI environment context for all access to the target
	EJB, i.e. for all method invocations on the exposed object reference.
	Default is "false", i.e. to only expose the JNDI context for object lookup.

	Switch this flag to "true" in order to expose the JNDI environment (including
	the authorization context) for each EJB invocation, as needed by WebLogic
	for EJBs with authorization requirements.
						]]></xsd:documentation>
					</xsd:annotation>
				</xsd:attribute>
				<xsd:attribute name="lazy-init" default="default" type="beans:defaultable-boolean">
					<xsd:annotation>
						<xsd:documentation><![CDATA[
	Indicates whether or not this bean is to be lazily initialized.
	If false, it will be instantiated on startup by bean factories
	that perform eager initialization of singletons. The default is
	"false".
	
	Note: This attribute will not be inherited by child bean definitions.
	Hence, it needs to be specified per concrete bean definition.
	    				]]></xsd:documentation>
					</xsd:annotation>
				</xsd:attribute>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:complexType name="ejbType">
		<xsd:complexContent>
			<xsd:extension base="jndiLocatingType">
				<xsd:attribute name="lookup-home-on-startup" type="xsd:boolean" default="true">
					<xsd:annotation>
						<xsd:documentation><![CDATA[
	Controls whether the lookup of the EJB home object is performed
	immediately on startup (if true, the default), or on first access
	(if false).
						]]></xsd:documentation>
					</xsd:annotation>
				</xsd:attribute>
				<xsd:attribute name="cache-home" type="xsd:boolean" default="true">
					<xsd:annotation>
						<xsd:documentation><![CDATA[
	Controls whether the EJB home object is cached once it has been located.
	On by default; turn this flag off to always reobtain fresh home objects.
						]]></xsd:documentation>
					</xsd:annotation>
				</xsd:attribute>
				<xsd:attribute name="business-interface" type="xsd:string" use="required">
					<xsd:annotation>
						<xsd:documentation source="java:java.lang.Class"><![CDATA[
	The business interface of the EJB being proxied.
						]]></xsd:documentation>
					</xsd:annotation>
				</xsd:attribute>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:simpleType name="environmentRefType">
		<xsd:annotation>
			<xsd:appinfo>
				<tool:annotation kind="ref">
					<tool:expected-type type="java.util.Properties"/>
				</tool:annotation>
			</xsd:appinfo>
		</xsd:annotation>
		<xsd:union memberTypes="xsd:string"/>
	</xsd:simpleType>

</xsd:schema>
