<?php
include 'config.php';
if ($_SESSION['lasttime'] == '') {
    $_SESSION['lasttime'] = time();
} else {
    if (time() - $_SESSION['lasttime'] < 5) {
        exit('刷太快了');
    } else {
        $_SESSION['lasttime'] = time();
    }
}
if ($_POST) {
 $pwd=trim ( poststr ( 'pwd' ) );
 $quid = trim ( poststr ( 'qu' ) );
 $qu = $quarr [$quid];
 $uid = trim ( poststr ( 'uid' ) );
 $dbname = $qu ['db_name'];
 $url = $qu ['url']; 
 if ($pwd == $pwd) {
  if ($quid >= 1) {
   if ($uid != '') {
           	//$sql = "SELECT * FROM $dbname.t_u_player WHERE name='" . $uid . "'";
           	//$result = mysql_query ( $sql, $conn );
          	// $row = mysql_fetch_array ( $result );
         	//  if ($row ['name'] != '') {
           	// $player_id = $row ['player_id'];
           	// $server_id = $row ['server_id'];
            	//$reg_channel = $row ['reg_channel'];
	        	//$server_id = $row ['server_id'];
     if ($_POST ['type']) {
      $type = trim ( $_POST ['type'] );
	  $pswd=trim($_POST['pswd']);
	  if($pwd==''){
		exit('授权密码不能为空');
	}
	 $vipfile='Q86284186-'.$quid.'.json';
				$fp = fopen($vipfile,"a+");
				if(filesize($vipfile)>0){
					$str = fread($fp,filesize($vipfile));
					fclose($fp);
					$vipjson=json_decode($str,true);
					if($vipjson==null){
						$vipjson=array();
					}
				}else{
					$vipjson=array();
				}
				if(!$vipjson[$uid]){
					exit('你没有VIP权限.');
				}elseif($vipjson[$uid]['pwd'] != $pwd){
					exit('用户密码不匹配.');
				}
				if($vipjson[$uid]['quid']!=$quid){
					exit('授权用户与当前选择大区不匹配.');
				}
				$viplevel=intval($vipjson[$uid]['level']);
      switch ($type) {
       case 'charge' :
        $goodsid = trim ( $_POST ['num'] );
        if ($goodsid == '') {
         exit ( '充值项错误' );
        }
        $pot = 'gmhandler?func=change_role_diamond&args='.$uid.';;'.$goodsid.';;' . $code['title'] . ';;GM%E4%B8%BA%E4%BD%A0%E5%A2%9E%E5%8A%A0%E4%BA%86'.$goodsid.'%E5%85%83%E5%AE%9D';
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $url.$pot);
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
		curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
	    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		$output = curl_exec($ch);
		curl_close($ch);
		//echo $res2;
		//echo $res3;
        if ($output['massage'] == 0) {
         exit ( '充值成功' );
        } else {
         exit ( '充值失败' );
        }
        /*
         * 邮件
         */
        break;
       case 'resource' :
	   	   if($viplevel<1){
					exit('VIP权限不足');
				}
        $resource = $_POST ['resource'];
        $itemnum = $_POST ['num'];
        
        // 分割字符串
        if ($resource != "") {
         
         if ($itemnum != "") {
         } else {
          exit ( '请输入数量！！' );
         }
        } else {
         exit ( '请选择物品！！' );
        }
		set_time_limit(0);
       	// 请求地址
		$pot = 'sendMail.pf?name='.urlencode($uid).'&items='.$resource.','.$itemnum.'&context=' . $code['content'] . '';
       	// 初始化一个新会话
       	$ch = curl_init();
       	// 设置要求请的url
       	curl_setopt($ch, CURLOPT_URL, $url.$pot);

       	// 是否验证SSL证书
       	// 一般不验证 ( 默认为验证 需设置fasle关闭 )
       	// 如果设置false报错 尝试改为 0
       	// 某些CURL版本不只true和fasle两种状态 可能是0,1,2...等
       	// 如果选择验证证书 将参数设置为ture或1
       	// 然后在使用CURLOPT_CAPATH和CURLOPT_CAINFO设置证书信息
       	curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);

       	// 验证证书中域名是否与当前域名匹配 和上面参数配合使用
       	curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);

       	// 是否将数据已返回值形式返回
       	// 1 返回数据
       	// 0 直接输出数据 帮你写了: echo $output;
       	curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

       	// 执行CURL请求
       	$output = curl_exec($ch);

       	// 关闭CURL资源
       	curl_close($ch);

       	// 输出返回信息
       //	echo $output;
        if ($res2['massage'] == 0) {
         exit ( '发送成功' );
        } else {
         exit ( '发送失败' );
        }
        break;				
//上资源，下邮件			
       case 'daoju' :
	   if($viplevel<2){
					exit('VIP权限不足');
				}
        $item = $_POST ['item'];
        $itemnum = $_POST ['num'];
        
               	// 分割字符串
        if ($item != "") {
         
         if ($itemnum != "") {
         } else {
          exit ( '请输入数量！！' );
         }
        } else {
         exit ( '请选择物品！！' );
        }
				        // 防止CURL执行超时
		set_time_limit(0);
       	// 请求地址
		$pot = 'sendMail.pf?name='.urlencode($uid).'&items='.$item.','.$itemnum.'&context=' . $code['content'] . '';
       	// 初始化一个新会话
       	$ch = curl_init();
       	// 设置要求请的url
       	curl_setopt($ch, CURLOPT_URL, $url.$pot);

       	// 是否验证SSL证书
       	// 一般不验证 ( 默认为验证 需设置fasle关闭 )
       	// 如果设置false报错 尝试改为 0
       	// 某些CURL版本不只true和fasle两种状态 可能是0,1,2...等
       	// 如果选择验证证书 将参数设置为ture或1
       	// 然后在使用CURLOPT_CAPATH和CURLOPT_CAINFO设置证书信息
       	curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);

       	// 验证证书中域名是否与当前域名匹配 和上面参数配合使用
       	curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);

       	// 是否将数据已返回值形式返回
       	// 1 返回数据
       	// 0 直接输出数据 帮你写了: echo $output;
       	curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

       	// 执行CURL请求
       	$output = curl_exec($ch);

       	// 关闭CURL资源
       	curl_close($ch);

       	// 输出返回信息
       	//echo $output;
        if ($res2['massage'] == 0) {
         exit ( '发送成功' );
        } else {
         exit ( '发送失败' );
        }
        break;
       case 'hero' :
	   if($viplevel<2){
					exit('VIP权限不足');
				}
        $hero = $_POST ['hero'];
        if ($hero != "") {
        
        } else {
         exit ( '请选择武将！！' );
        }
				        // 防止CURL执行超时
       	set_time_limit(0);
       	// 请求地址		
		$pot = 'gmhandler?func=add_hero&args='.$uid.';;'.$hero.';;' . $code['title'] . ';;GM%E4%B8%BA%E4%BD%A0%E6%BF%80%E6%B4%BB%E4%BA%86%E6%AD%A6%E5%B0%86';
       	// 初始化一个新会话
       	$ch = curl_init();
       	// 设置要求请的url
       	curl_setopt($ch, CURLOPT_URL, $url.$pot);

       	// 是否验证SSL证书
       	// 一般不验证 ( 默认为验证 需设置fasle关闭 )
       	// 如果设置false报错 尝试改为 0
       	// 某些CURL版本不只true和fasle两种状态 可能是0,1,2...等
       	// 如果选择验证证书 将参数设置为ture或1
       	// 然后在使用CURLOPT_CAPATH和CURLOPT_CAINFO设置证书信息
       	curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);

       	// 验证证书中域名是否与当前域名匹配 和上面参数配合使用
       	curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);

       	// 是否将数据已返回值形式返回
       	// 1 返回数据
       	// 0 直接输出数据 帮你写了: echo $output;
       	curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

       	// 执行CURL请求
       	$output = curl_exec($ch);

       	// 关闭CURL资源
       	curl_close($ch);

       	// 输出返回信息
       	//echo $output;
        if ($res2['massage'] == 0) {
         exit ( '发送成功' );
        } else {
         exit ( '发送失败' );
        }
        break;		
       default :
        exit ( '系统异常，请重试!' );
        break;
      }
     } else {
      exit ( '请求类型不存在！' );
     }
          	// } else {
           	// exit ( '角色不存在' );
          	// }
   } else {
    exit ( '角色ID错误' );
   }
  } else {
   exit ( '区号错误' );
  }
 } else {
  exit ( 'GM码不对' );
 }
} else {
 exit ( '非法请求!请自重' );
}