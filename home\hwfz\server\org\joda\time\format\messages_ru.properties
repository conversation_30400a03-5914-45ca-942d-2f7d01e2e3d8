PeriodFormat.space=\u0020
PeriodFormat.comma=,
PeriodFormat.commandand=,\u0438
PeriodFormat.commaspaceand=, \u0438
PeriodFormat.commaspace=,\u0020
PeriodFormat.spaceandspace=\ \u0438\u0020
PeriodFormat.regex.separator=%
PeriodFormat.years.regex=^1$%[0-9]*(?<!1)[2-4]$%[0-9]*
PeriodFormat.years.list=\ \u0433\u043E\u0434%\ \u0433\u043E\u0434\u0430%\ \u043B\u0435\u0442
PeriodFormat.months.regex=^1$%[0-9]*(?<!1)[2-4]$%[0-9]*
PeriodFormat.months.list=\ \u043C\u0435\u0441\u044F\u0446%\ \u043C\u0435\u0441\u044F\u0446\u0430%\ \u043C\u0435\u0441\u044F\u0446\u0435\u0432
PeriodFormat.weeks.regex=^1$%[0-9]*(?<!1)[2-4]$%[0-9]*
PeriodFormat.weeks.list=\ \u043D\u0435\u0434\u0435\u043B\u044F%\ \u043D\u0435\u0434\u0435\u043B\u0438%\ \u043D\u0435\u0434\u0435\u043B\u044C
PeriodFormat.days.regex=^1$%[0-9]*(?<!1)[2-4]$%[0-9]*
PeriodFormat.days.list=\ \u0434\u0435\u043D\u044C%\ \u0434\u043D\u044F%\ \u0434\u043D\u0435\u0439
PeriodFormat.hours.regex=^1$%[0-9]*(?<!1)[2-4]$%[0-9]*
PeriodFormat.hours.list=\ \u0447\u0430\u0441%\ \u0447\u0430\u0441\u0430%\ \u0447\u0430\u0441\u043E\u0432
PeriodFormat.minutes.regex=^1$%[0-9]*(?<!1)[2-4]$%[0-9]*
PeriodFormat.minutes.list=\ \u043C\u0438\u043D\u0443\u0442\u0430%\ \u043C\u0438\u043D\u0443\u0442\u044B%\ \u043C\u0438\u043D\u0443\u0442
PeriodFormat.seconds.regex=^1$%[0-9]*(?<!1)[2-4]$%[0-9]*
PeriodFormat.seconds.list=\ \u0441\u0435\u043A\u0443\u043D\u0434\u0430%\ \u0441\u0435\u043A\u0443\u043D\u0434\u044B%\ \u0441\u0435\u043A\u0443\u043D\u0434
PeriodFormat.milliseconds.regex=^1$%[0-9]*(?<!1)[2-4]$%[0-9]*
PeriodFormat.milliseconds.list=\ \u043C\u0438\u043B\u043B\u0438\u0441\u0435\u043A\u0443\u043D\u0434\u0430%\ \u043C\u0438\u043B\u043B\u0438\u0441\u0435\u043A\u0443\u043D\u0434\u044B%\ \u043C\u0438\u043B\u043B\u0438\u0441\u0435\u043A\u0443\u043D\u0434
