<?php

include("AccountsApplication.php");

include("Server_list.php");

$action = $_GET["action"];

$accoutnApp = new AccountsApplication();
if($action == "test")
{
	$ip = getIP();

	echo $ip;
	echo ' -------- ';
	echo $is_in_white_list;
	echo ' -------- ';
	$aaa = in_array(strtok('*************', '.'), array('10', '127', '168', '192'));
	echo $aaa;
	echo ' -------- ';
	$white_list = array("**************");

	$is_in_white_list = in_array(strtok($ip, '.'), array('10', '127', '168', '192'));
	$is_in_white_list = $is_in_white_list || in_array($ip, $white_list);
    // Ĭ?false???????Ÿ??׵???
	$send_server_list = array();
	foreach ($server_list as $server) {
		if($is_in_white_list && $server['is_QA_open'] == '1')
		{
			unset($server['is_QA_open']);
			array_push($send_server_list, $server);
		}
	}

	echo(json_encode($send_server_list));
}
else if($action == "server_list")
{
 	//if(isset($_GET['pt']))
		//{
      //$pt = $_GET['pt'];
      //if($pt == "Android")
      //{
        //echo "-1";
  			//exit();
      //}
    //}

	if(isset($_GET['version']))
	{
		$version = $_GET['version'];

		$ip = getIP();
		$white_list = array("**************");
		$is_in_white_list = in_array(strtok($ip, '.'), array('10', '127', '168', '192'));
		$is_in_white_list = $is_in_white_list || in_array($ip, $white_list);
		$send_server_list = array();
		foreach ($server_list as $server) {
			if($server['is_QA_open'] != '1')
			{
				unset($server['is_QA_open']);
				array_push($send_server_list, $server);
			}
			else if($is_in_white_list && $server['is_QA_open'] == '1')
			{
				unset($server['is_QA_open']);
				array_push($send_server_list, $server);
			}  
		}

		//$accoutnApp->server_list($list_version , $server_list, $recommend , $version);
		$accoutnApp->server_list($list_version , $send_server_list, $recommend , $version);
	}
	else
	{
		echo "";
		exit();
	}
}
else if($action == "regist")
{
	$account_name = $_GET["user"];
	$account_pwd = $_GET["pwd"];
	$resultCode = $accoutnApp->regist($account_name , $account_pwd);
	$array = array();
	$array['result'] = $resultCode;
	if( $resultCode == 200 ){
		$array['user_name'] = $account_name;
		$array['user_pass'] = $account_pwd;
	}
	echo json_encode( $array );
	//$url='http://127.0.0.1/slg/Accounts.php?action=regist&user='.$account_name.'&pwd='.$account_pwd.'&server_id=1';
	//$html = file_get_contents($url);
	//echo $html;
	//return;
}
else if($action == "login"){
	$account_name = $_GET["user"];
	$account_pwd = $_GET["pwd"];
	//$server_id = $_GET["server_id"];
	//if($server_id == 6001){
		//$url='http://127.0.0.1/slg/Accounts.php?action=login&user='.$account_name.'&pwd='.$account_pwd.'&server_id=1';
	//	$html = file_get_contents($url);
	//	echo $html;
	//	return;
	//}
	$host = $server_list[0]['host'];
	//echo($host);
	$loginport = $server_list[0]['loginport'];
	//echo($loginport);
	//测试
	if($host == "127.0.0.1"){ 
		$host=$_SERVER["REMOTE_ADDR"];
	}else{
	} 
	$result = $accoutnApp->login($account_name, $account_pwd, $host, $loginport);
	echo $result;
}
else{
	// donothing;
}


function getIP()
{
	static $realip;
	if (isset($_SERVER)){
		if (isset($_SERVER["HTTP_X_FORWARDED_FOR"])){
			$realip = $_SERVER["HTTP_X_FORWARDED_FOR"];
		} else if (isset($_SERVER["HTTP_CLIENT_IP"])) {
			$realip = $_SERVER["HTTP_CLIENT_IP"];
		} else {
			$realip = $_SERVER["REMOTE_ADDR"];
		}
	} else {
		if (getenv("HTTP_X_FORWARDED_FOR")){
			$realip = getenv("HTTP_X_FORWARDED_FOR");
		} else if (getenv("HTTP_CLIENT_IP")) {
			$realip = getenv("HTTP_CLIENT_IP");
		} else {
			$realip = getenv("REMOTE_ADDR");
		}
	}
	return $realip;
}
?>