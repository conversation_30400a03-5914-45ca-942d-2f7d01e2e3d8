[{"distance": 7, "talent_skill": "-1", "postlevel": -1, "science_error": -1, "help_error": -1, "taret": -1, "num": 1, "hero_info": -1, "isshowzhenxing": -1, "defent_error": 100, "trap_error": -1, "id": 533}, {"distance": 8, "talent_skill": "-1", "postlevel": -1, "science_error": -1, "help_error": -1, "taret": -1, "num": 1, "hero_info": -1, "isshowzhenxing": -1, "defent_error": 95, "trap_error": -1, "id": 534}, {"distance": 10, "talent_skill": "-1", "postlevel": -1, "science_error": -1, "help_error": -1, "taret": -1, "num": 2, "hero_info": -1, "isshowzhenxing": -1, "defent_error": 90, "trap_error": -1, "id": 535}, {"distance": 12, "talent_skill": "-1", "postlevel": -1, "science_error": -1, "help_error": -1, "taret": -1, "num": 2, "hero_info": -1, "isshowzhenxing": -1, "defent_error": 85, "trap_error": -1, "id": 536}, {"distance": 14, "talent_skill": "-1", "postlevel": -1, "science_error": -1, "help_error": -1, "taret": -1, "num": 3, "hero_info": -1, "isshowzhenxing": -1, "defent_error": 80, "trap_error": -1, "id": 537}, {"distance": 16, "talent_skill": "-1", "postlevel": -1, "science_error": -1, "help_error": -1, "taret": -1, "num": 3, "hero_info": -1, "isshowzhenxing": -1, "defent_error": 75, "trap_error": -1, "id": 538}, {"distance": 18, "talent_skill": "-1", "postlevel": -1, "science_error": -1, "help_error": -1, "taret": -1, "num": 3, "hero_info": -1, "isshowzhenxing": -1, "defent_error": 70, "trap_error": -1, "id": 539}, {"distance": 20, "talent_skill": "-1", "postlevel": -1, "science_error": -1, "help_error": -1, "taret": -1, "num": 4, "hero_info": -1, "isshowzhenxing": -1, "defent_error": 65, "trap_error": -1, "id": 540}, {"distance": 22, "talent_skill": "-1", "postlevel": -1, "science_error": -1, "help_error": -1, "taret": -1, "num": 4, "hero_info": -1, "isshowzhenxing": -1, "defent_error": 60, "trap_error": -1, "id": 541}, {"distance": 24, "talent_skill": "-1", "postlevel": -1, "science_error": -1, "help_error": -1, "taret": -1, "num": 4, "hero_info": -1, "isshowzhenxing": -1, "defent_error": 55, "trap_error": -1, "id": 542}, {"distance": 26, "talent_skill": "-1", "postlevel": -1, "science_error": -1, "help_error": 100, "taret": -1, "num": 4, "hero_info": -1, "isshowzhenxing": -1, "defent_error": 50, "trap_error": 90, "id": 543}, {"distance": 28, "talent_skill": "-1", "postlevel": -1, "science_error": -1, "help_error": 90, "taret": -1, "num": 4, "hero_info": 0, "isshowzhenxing": -1, "defent_error": 45, "trap_error": 85, "id": 544}, {"distance": 30, "talent_skill": "-1", "postlevel": -1, "science_error": -1, "help_error": 80, "taret": -1, "num": 5, "hero_info": 0, "isshowzhenxing": -1, "defent_error": 40, "trap_error": 80, "id": 545}, {"distance": 32, "talent_skill": "-1", "postlevel": -1, "science_error": -1, "help_error": 70, "taret": -1, "num": 5, "hero_info": 0, "isshowzhenxing": 0, "defent_error": 35, "trap_error": 75, "id": 546}, {"distance": 34, "talent_skill": "-1", "postlevel": -1, "science_error": -1, "help_error": 60, "taret": -1, "num": 5, "hero_info": 0, "isshowzhenxing": 0, "defent_error": 30, "trap_error": 70, "id": 547}, {"distance": 36, "talent_skill": "-1", "postlevel": -1, "science_error": 90, "help_error": 50, "taret": -1, "num": 10, "hero_info": 0, "isshowzhenxing": 0, "defent_error": 25, "trap_error": 65, "id": 548}, {"distance": 38, "talent_skill": "-1", "postlevel": -1, "science_error": 80, "help_error": 40, "taret": -1, "num": 10, "hero_info": 0, "isshowzhenxing": 0, "defent_error": 20, "trap_error": 60, "id": 549}, {"distance": 40, "talent_skill": "-1", "postlevel": -1, "science_error": 70, "help_error": 30, "taret": -1, "num": 10, "hero_info": 0, "isshowzhenxing": 0, "defent_error": 15, "trap_error": 55, "id": 550}, {"distance": 42, "talent_skill": "-1", "postlevel": -1, "science_error": 60, "help_error": 20, "taret": -1, "num": 10, "hero_info": 0, "isshowzhenxing": 0, "defent_error": 10, "trap_error": 50, "id": 551}, {"distance": 44, "talent_skill": "-1", "postlevel": -1, "science_error": 50, "help_error": 10, "taret": -1, "num": 10, "hero_info": 0, "isshowzhenxing": 0, "defent_error": 5, "trap_error": 45, "id": 552}, {"distance": 46, "talent_skill": "-1", "postlevel": -1, "science_error": 40, "help_error": 9, "taret": -1, "num": 15, "hero_info": 0, "isshowzhenxing": 0, "defent_error": 0, "trap_error": 40, "id": 553}, {"distance": 48, "talent_skill": "20000037,20000038,20000039,20000040,20000053,20000054,20000065,20000066,20000073,20000074,20000078", "postlevel": -1, "science_error": 30, "help_error": 8, "taret": -1, "num": 15, "hero_info": 0, "isshowzhenxing": 0, "defent_error": 0, "trap_error": 35, "id": 554}, {"distance": 50, "talent_skill": "20000037,20000038,20000039,20000040,20000053,20000054,20000065,20000066,20000073,20000074,20000078", "postlevel": -1, "science_error": 20, "help_error": 7, "taret": -1, "num": 15, "hero_info": 0, "isshowzhenxing": 0, "defent_error": 0, "trap_error": 30, "id": 555}, {"distance": 52, "talent_skill": "20000037,20000038,20000039,20000040,20000053,20000054,20000065,20000066,20000073,20000074,20000078", "postlevel": -1, "science_error": 10, "help_error": 6, "taret": -1, "num": 15, "hero_info": 0, "isshowzhenxing": 0, "defent_error": 0, "trap_error": 25, "id": 556}, {"distance": 54, "talent_skill": "20000037,20000038,20000039,20000040,20000053,20000054,20000065,20000066,20000073,20000074,20000078", "postlevel": -1, "science_error": 0, "help_error": 5, "taret": -1, "num": 15, "hero_info": 0, "isshowzhenxing": 0, "defent_error": 0, "trap_error": 20, "id": 557}, {"distance": 56, "talent_skill": "20000037,20000038,20000039,20000040,20000053,20000054,20000065,20000066,20000073,20000074,20000078", "postlevel": -1, "science_error": 0, "help_error": 4, "taret": -1, "num": 20, "hero_info": 0, "isshowzhenxing": 0, "defent_error": 0, "trap_error": 15, "id": 558}, {"distance": 58, "talent_skill": "20000037,20000038,20000039,20000040,20000053,20000054,20000065,20000066,20000073,20000074,20000078", "postlevel": -1, "science_error": 0, "help_error": 3, "taret": -1, "num": 20, "hero_info": 0, "isshowzhenxing": 0, "defent_error": 0, "trap_error": 10, "id": 559}, {"distance": 60, "talent_skill": "20000037,20000038,20000039,20000040,20000053,20000054,20000065,20000066,20000073,20000074,20000078", "postlevel": -1, "science_error": 0, "help_error": 2, "taret": -1, "num": 25, "hero_info": 0, "isshowzhenxing": 0, "defent_error": 0, "trap_error": 5, "id": 560}, {"distance": 62, "talent_skill": "20000037,20000038,20000039,20000040,20000053,20000054,20000065,20000066,20000073,20000074,20000078", "postlevel": -1, "science_error": 0, "help_error": 1, "taret": -1, "num": 30, "hero_info": 0, "isshowzhenxing": 0, "defent_error": 0, "trap_error": 0, "id": 561}, {"distance": 64, "talent_skill": "20000037,20000038,20000039,20000040,20000053,20000054,20000065,20000066,20000073,20000074,20000078", "postlevel": -1, "science_error": 0, "help_error": 0, "taret": -1, "num": 60, "hero_info": 0, "isshowzhenxing": 0, "defent_error": 0, "trap_error": 0, "id": 562}]