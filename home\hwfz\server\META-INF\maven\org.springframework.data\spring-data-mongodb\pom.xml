<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<artifactId>spring-data-mongodb</artifactId>

	<name>Spring Data MongoDB - Core</name>
	<description>MongoDB support for Spring Data</description>

	<parent>
		<groupId>org.springframework.data</groupId>
		<artifactId>spring-data-mongodb-parent</artifactId>
		<version>1.9.4.RELEASE</version>
		<relativePath>../pom.xml</relativePath>
	</parent>

	<properties>
		<validation>1.0.0.GA</validation>
		<objenesis>1.3</objenesis>
		<equalsverifier>1.5</equalsverifier>
	</properties>

	<dependencies>

		<!-- Spring -->
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-tx</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-beans</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-core</artifactId>
			<exclusions>
				<exclusion>
					<groupId>commons-logging</groupId>
					<artifactId>commons-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-expression</artifactId>
		</dependency>

		<!-- Spring Data -->
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>spring-data-commons</artifactId>
			<version>${springdata.commons}</version>
		</dependency>

		<dependency>
			<groupId>com.querydsl</groupId>
			<artifactId>querydsl-mongodb</artifactId>
			<version>${querydsl}</version>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>com.querydsl</groupId>
			<artifactId>querydsl-apt</artifactId>
			<version>${querydsl}</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>javax.annotation</groupId>
			<artifactId>jsr250-api</artifactId>
			<version>1.0</version>
			<optional>true</optional>
		</dependency>

		<!-- CDI -->
		<dependency>
			<groupId>javax.enterprise</groupId>
			<artifactId>cdi-api</artifactId>
			<version>${cdi}</version>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>javax.el</groupId>
			<artifactId>el-api</artifactId>
			<version>${cdi}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.openwebbeans.test</groupId>
			<artifactId>cditest-owb</artifactId>
			<version>${webbeans}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>servlet-api</artifactId>
			<version>2.5</version>
			<scope>test</scope>
		</dependency>

		<!-- JSR 303 Validation -->
		<dependency>
			<groupId>javax.validation</groupId>
			<artifactId>validation-api</artifactId>
			<version>${validation}</version>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.objenesis</groupId>
			<artifactId>objenesis</artifactId>
			<version>${objenesis}</version>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-validator</artifactId>
			<version>4.2.0.Final</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>joda-time</groupId>
			<artifactId>joda-time</artifactId>
			<version>${jodatime}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.threeten</groupId>
			<artifactId>threetenbp</artifactId>
			<version>${threetenbp}</version>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>${jackson}</version>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>jul-to-slf4j</artifactId>
			<version>${slf4j}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>nl.jqno.equalsverifier</groupId>
			<artifactId>equalsverifier</artifactId>
			<version>${equalsverifier}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-webmvc</artifactId>
			<scope>test</scope>
		</dependency>

	</dependencies>

	<build>
		<plugins>

			<plugin>
				<groupId>com.mysema.maven</groupId>
				<artifactId>apt-maven-plugin</artifactId>
				<version>${apt}</version>
				<dependencies>
					<dependency>
						<groupId>com.querydsl</groupId>
						<artifactId>querydsl-apt</artifactId>
						<version>${querydsl}</version>
					</dependency>
				</dependencies>
				<executions>
					<execution>
						<phase>generate-test-sources</phase>
						<goals>
							<goal>test-process</goal>
						</goals>
						<configuration>
							<outputDirectory>target/generated-test-sources</outputDirectory>
							<processor>org.springframework.data.mongodb.repository.support.MongoAnnotationProcessor</processor>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>2.12</version>
				<configuration>
					<useFile>false</useFile>
					<includes>
						<include>**/*Tests.java</include>
					</includes>
					<excludes>
						<exclude>**/PerformanceTests.java</exclude>
					</excludes>
					<systemPropertyVariables>
						<java.util.logging.config.file>src/test/resources/logging.properties</java.util.logging.config.file>
					</systemPropertyVariables>
					<properties>
						<property>
							<name>listener</name>
							<value>org.springframework.data.mongodb.test.util.CleanMongoDBJunitRunListener</value>
						</property>
					</properties>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>
