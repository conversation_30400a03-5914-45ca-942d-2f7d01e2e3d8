# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
# standard ant tasks
ant=org.apache.tools.ant.taskdefs.Ant
antcall=org.apache.tools.ant.taskdefs.CallTarget
antstructure=org.apache.tools.ant.taskdefs.AntStructure
antversion=org.apache.tools.ant.taskdefs.condition.AntVersion
apply=org.apache.tools.ant.taskdefs.Transform
apt=org.apache.tools.ant.taskdefs.Apt
attributenamespacedef=org.apache.tools.ant.taskdefs.AttributeNamespaceDef
augment=org.apache.tools.ant.taskdefs.AugmentReference
available=org.apache.tools.ant.taskdefs.Available
basename=org.apache.tools.ant.taskdefs.Basename
bindtargets=org.apache.tools.ant.taskdefs.BindTargets
buildnumber=org.apache.tools.ant.taskdefs.BuildNumber
bunzip2=org.apache.tools.ant.taskdefs.BUnzip2
bzip2=org.apache.tools.ant.taskdefs.BZip2
checksum=org.apache.tools.ant.taskdefs.Checksum
chmod=org.apache.tools.ant.taskdefs.Chmod
classloader=org.apache.tools.ant.taskdefs.Classloader
commandlauncher=org.apache.tools.ant.taskdefs.CommandLauncherTask
componentdef=org.apache.tools.ant.taskdefs.Componentdef
concat=org.apache.tools.ant.taskdefs.Concat
condition=org.apache.tools.ant.taskdefs.ConditionTask
copy=org.apache.tools.ant.taskdefs.Copy
cvs=org.apache.tools.ant.taskdefs.Cvs
cvschangelog=org.apache.tools.ant.taskdefs.cvslib.ChangeLogTask
cvspass=org.apache.tools.ant.taskdefs.CVSPass
cvstagdiff=org.apache.tools.ant.taskdefs.cvslib.CvsTagDiff
cvsversion=org.apache.tools.ant.taskdefs.cvslib.CvsVersion
defaultexcludes=org.apache.tools.ant.taskdefs.DefaultExcludes
delete=org.apache.tools.ant.taskdefs.Delete
dependset=org.apache.tools.ant.taskdefs.DependSet
diagnostics=org.apache.tools.ant.taskdefs.DiagnosticsTask
dirname=org.apache.tools.ant.taskdefs.Dirname
ear=org.apache.tools.ant.taskdefs.Ear
echo=org.apache.tools.ant.taskdefs.Echo
echoproperties=org.apache.tools.ant.taskdefs.optional.EchoProperties
echoxml=org.apache.tools.ant.taskdefs.EchoXML
exec=org.apache.tools.ant.taskdefs.ExecTask
fail=org.apache.tools.ant.taskdefs.Exit
filter=org.apache.tools.ant.taskdefs.Filter
fixcrlf=org.apache.tools.ant.taskdefs.FixCRLF
#funtest=org.apache.tools.ant.taskdefs.optional.testing.Funtest
genkey=org.apache.tools.ant.taskdefs.GenerateKey
get=org.apache.tools.ant.taskdefs.Get
gunzip=org.apache.tools.ant.taskdefs.GUnzip
gzip=org.apache.tools.ant.taskdefs.GZip
hostinfo=org.apache.tools.ant.taskdefs.HostInfo
import=org.apache.tools.ant.taskdefs.ImportTask
include=org.apache.tools.ant.taskdefs.ImportTask
input=org.apache.tools.ant.taskdefs.Input
jar=org.apache.tools.ant.taskdefs.Jar
java=org.apache.tools.ant.taskdefs.Java
javac=org.apache.tools.ant.taskdefs.Javac
javadoc=org.apache.tools.ant.taskdefs.Javadoc
length=org.apache.tools.ant.taskdefs.Length
loadfile=org.apache.tools.ant.taskdefs.LoadFile
loadproperties=org.apache.tools.ant.taskdefs.LoadProperties
loadresource=org.apache.tools.ant.taskdefs.LoadResource
local=org.apache.tools.ant.taskdefs.Local
macrodef=org.apache.tools.ant.taskdefs.MacroDef
mail=org.apache.tools.ant.taskdefs.email.EmailTask
makeurl=org.apache.tools.ant.taskdefs.MakeUrl
manifest=org.apache.tools.ant.taskdefs.ManifestTask
manifestclasspath=org.apache.tools.ant.taskdefs.ManifestClassPath
mkdir=org.apache.tools.ant.taskdefs.Mkdir
move=org.apache.tools.ant.taskdefs.Move
nice=org.apache.tools.ant.taskdefs.Nice
parallel=org.apache.tools.ant.taskdefs.Parallel
patch=org.apache.tools.ant.taskdefs.Patch
pathconvert=org.apache.tools.ant.taskdefs.PathConvert
presetdef=org.apache.tools.ant.taskdefs.PreSetDef
projecthelper=org.apache.tools.ant.taskdefs.ProjectHelperTask
property=org.apache.tools.ant.taskdefs.Property
propertyhelper=org.apache.tools.ant.taskdefs.PropertyHelperTask
record=org.apache.tools.ant.taskdefs.Recorder
replace=org.apache.tools.ant.taskdefs.Replace
resourcecount=org.apache.tools.ant.taskdefs.ResourceCount
retry=org.apache.tools.ant.taskdefs.Retry
rmic=org.apache.tools.ant.taskdefs.Rmic
sequential=org.apache.tools.ant.taskdefs.Sequential
signjar=org.apache.tools.ant.taskdefs.SignJar
sleep=org.apache.tools.ant.taskdefs.Sleep
sql=org.apache.tools.ant.taskdefs.SQLExec
subant=org.apache.tools.ant.taskdefs.SubAnt
sync=org.apache.tools.ant.taskdefs.Sync
tar=org.apache.tools.ant.taskdefs.Tar
taskdef=org.apache.tools.ant.taskdefs.Taskdef
tempfile=org.apache.tools.ant.taskdefs.TempFile
touch=org.apache.tools.ant.taskdefs.Touch
tstamp=org.apache.tools.ant.taskdefs.Tstamp
truncate=org.apache.tools.ant.taskdefs.Truncate
typedef=org.apache.tools.ant.taskdefs.Typedef
unjar=org.apache.tools.ant.taskdefs.Expand
untar=org.apache.tools.ant.taskdefs.Untar
unwar=org.apache.tools.ant.taskdefs.Expand
unzip=org.apache.tools.ant.taskdefs.Expand
uptodate=org.apache.tools.ant.taskdefs.UpToDate
waitfor=org.apache.tools.ant.taskdefs.WaitFor
war=org.apache.tools.ant.taskdefs.War
whichresource=org.apache.tools.ant.taskdefs.WhichResource
xmlproperty=org.apache.tools.ant.taskdefs.XmlProperty
xslt=org.apache.tools.ant.taskdefs.XSLTProcess
zip=org.apache.tools.ant.taskdefs.Zip

# optional tasks
antlr=org.apache.tools.ant.taskdefs.optional.ANTLR
attrib=org.apache.tools.ant.taskdefs.optional.windows.Attrib
blgenclient=org.apache.tools.ant.taskdefs.optional.ejb.BorlandGenerateClient
cab=org.apache.tools.ant.taskdefs.optional.Cab
cccheckin=org.apache.tools.ant.taskdefs.optional.clearcase.CCCheckin
cccheckout=org.apache.tools.ant.taskdefs.optional.clearcase.CCCheckout
cclock=org.apache.tools.ant.taskdefs.optional.clearcase.CCLock
ccmcheckin=org.apache.tools.ant.taskdefs.optional.ccm.CCMCheckin
ccmcheckintask=org.apache.tools.ant.taskdefs.optional.ccm.CCMCheckinDefault
ccmcheckout=org.apache.tools.ant.taskdefs.optional.ccm.CCMCheckout
ccmcreatetask=org.apache.tools.ant.taskdefs.optional.ccm.CCMCreateTask
ccmkattr=org.apache.tools.ant.taskdefs.optional.clearcase.CCMkattr
ccmkbl=org.apache.tools.ant.taskdefs.optional.clearcase.CCMkbl
ccmkdir=org.apache.tools.ant.taskdefs.optional.clearcase.CCMkdir
ccmkelem=org.apache.tools.ant.taskdefs.optional.clearcase.CCMkelem
ccmklabel=org.apache.tools.ant.taskdefs.optional.clearcase.CCMklabel
ccmklbtype=org.apache.tools.ant.taskdefs.optional.clearcase.CCMklbtype
ccmreconfigure=org.apache.tools.ant.taskdefs.optional.ccm.CCMReconfigure
ccrmtype=org.apache.tools.ant.taskdefs.optional.clearcase.CCRmtype
ccuncheckout=org.apache.tools.ant.taskdefs.optional.clearcase.CCUnCheckout
ccunlock=org.apache.tools.ant.taskdefs.optional.clearcase.CCUnlock
ccupdate=org.apache.tools.ant.taskdefs.optional.clearcase.CCUpdate
chgrp=org.apache.tools.ant.taskdefs.optional.unix.Chgrp
chown=org.apache.tools.ant.taskdefs.optional.unix.Chown
depend=org.apache.tools.ant.taskdefs.optional.depend.Depend
ejbjar=org.apache.tools.ant.taskdefs.optional.ejb.EjbJar
ftp=org.apache.tools.ant.taskdefs.optional.net.FTP
image=org.apache.tools.ant.taskdefs.optional.image.Image
iplanet-ejbc=org.apache.tools.ant.taskdefs.optional.ejb.IPlanetEjbcTask
jarlib-available=org.apache.tools.ant.taskdefs.optional.extension.JarLibAvailableTask
jarlib-display=org.apache.tools.ant.taskdefs.optional.extension.JarLibDisplayTask
jarlib-manifest=org.apache.tools.ant.taskdefs.optional.extension.JarLibManifestTask
jarlib-resolve=org.apache.tools.ant.taskdefs.optional.extension.JarLibResolveTask
javacc=org.apache.tools.ant.taskdefs.optional.javacc.JavaCC
javah=org.apache.tools.ant.taskdefs.optional.Javah
jdepend=org.apache.tools.ant.taskdefs.optional.jdepend.JDependTask
jjdoc=org.apache.tools.ant.taskdefs.optional.javacc.JJDoc
jjtree=org.apache.tools.ant.taskdefs.optional.javacc.JJTree
junit=org.apache.tools.ant.taskdefs.optional.junit.JUnitTask
junitreport=org.apache.tools.ant.taskdefs.optional.junit.XMLResultAggregator
native2ascii=org.apache.tools.ant.taskdefs.optional.Native2Ascii
netrexxc=org.apache.tools.ant.taskdefs.optional.NetRexxC
propertyfile=org.apache.tools.ant.taskdefs.optional.PropertyFile
pvcs=org.apache.tools.ant.taskdefs.optional.pvcs.Pvcs
replaceregexp=org.apache.tools.ant.taskdefs.optional.ReplaceRegExp
rexec=org.apache.tools.ant.taskdefs.optional.net.RExecTask
rpm=org.apache.tools.ant.taskdefs.optional.Rpm
schemavalidate=org.apache.tools.ant.taskdefs.optional.SchemaValidate
scp=org.apache.tools.ant.taskdefs.optional.ssh.Scp
script=org.apache.tools.ant.taskdefs.optional.Script
scriptdef=org.apache.tools.ant.taskdefs.optional.script.ScriptDef
serverdeploy=org.apache.tools.ant.taskdefs.optional.j2ee.ServerDeploy
setproxy=org.apache.tools.ant.taskdefs.optional.net.SetProxy
soscheckin=org.apache.tools.ant.taskdefs.optional.sos.SOSCheckin
soscheckout=org.apache.tools.ant.taskdefs.optional.sos.SOSCheckout
sosget=org.apache.tools.ant.taskdefs.optional.sos.SOSGet
soslabel=org.apache.tools.ant.taskdefs.optional.sos.SOSLabel
sound=org.apache.tools.ant.taskdefs.optional.sound.SoundTask
splash=org.apache.tools.ant.taskdefs.optional.splash.SplashTask
sshexec=org.apache.tools.ant.taskdefs.optional.ssh.SSHExec
sshsession=org.apache.tools.ant.taskdefs.optional.ssh.SSHSession
symlink=org.apache.tools.ant.taskdefs.optional.unix.Symlink
telnet=org.apache.tools.ant.taskdefs.optional.net.TelnetTask
translate=org.apache.tools.ant.taskdefs.optional.i18n.Translate
verifyjar=org.apache.tools.ant.taskdefs.VerifyJar
vssadd=org.apache.tools.ant.taskdefs.optional.vss.MSVSSADD
vsscheckin=org.apache.tools.ant.taskdefs.optional.vss.MSVSSCHECKIN
vsscheckout=org.apache.tools.ant.taskdefs.optional.vss.MSVSSCHECKOUT
vsscp=org.apache.tools.ant.taskdefs.optional.vss.MSVSSCP
vsscreate=org.apache.tools.ant.taskdefs.optional.vss.MSVSSCREATE
vssget=org.apache.tools.ant.taskdefs.optional.vss.MSVSSGET
vsshistory=org.apache.tools.ant.taskdefs.optional.vss.MSVSSHISTORY
vsslabel=org.apache.tools.ant.taskdefs.optional.vss.MSVSSLABEL
wljspc=org.apache.tools.ant.taskdefs.optional.jsp.WLJspc
xmlvalidate=org.apache.tools.ant.taskdefs.optional.XMLValidateTask


# deprecated ant tasks (kept for back compatibility)
copydir=org.apache.tools.ant.taskdefs.Copydir
copyfile=org.apache.tools.ant.taskdefs.Copyfile
copypath=org.apache.tools.ant.taskdefs.CopyPath
deltree=org.apache.tools.ant.taskdefs.Deltree
execon=org.apache.tools.ant.taskdefs.ExecuteOn
javadoc2=org.apache.tools.ant.taskdefs.Javadoc
jlink=org.apache.tools.ant.taskdefs.optional.jlink.JlinkTask
jspc=org.apache.tools.ant.taskdefs.optional.jsp.JspC
mimemail=org.apache.tools.ant.taskdefs.optional.net.MimeMail
rename=org.apache.tools.ant.taskdefs.Rename
renameext=org.apache.tools.ant.taskdefs.optional.RenameExtensions
style=org.apache.tools.ant.taskdefs.XSLTProcess
