package common.protobuf;
// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Arena.pb

public final class ArenaPb {
  private ArenaPb() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface PBArenaChallengeInfoOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // repeated .PBArenaChallengePlayerInfo playerInfos = 1;
    /**
     * <code>repeated .PBArenaChallengePlayerInfo playerInfos = 1;</code>
     *
     * <pre>
     *挑战玩家数据
     * </pre>
     */
    java.util.List<ArenaPb.PBArenaChallengePlayerInfo> 
        getPlayerInfosList();
    /**
     * <code>repeated .PBArenaChallengePlayerInfo playerInfos = 1;</code>
     *
     * <pre>
     *挑战玩家数据
     * </pre>
     */
    ArenaPb.PBArenaChallengePlayerInfo getPlayerInfos(int index);
    /**
     * <code>repeated .PBArenaChallengePlayerInfo playerInfos = 1;</code>
     *
     * <pre>
     *挑战玩家数据
     * </pre>
     */
    int getPlayerInfosCount();
    /**
     * <code>repeated .PBArenaChallengePlayerInfo playerInfos = 1;</code>
     *
     * <pre>
     *挑战玩家数据
     * </pre>
     */
    java.util.List<? extends ArenaPb.PBArenaChallengePlayerInfoOrBuilder> 
        getPlayerInfosOrBuilderList();
    /**
     * <code>repeated .PBArenaChallengePlayerInfo playerInfos = 1;</code>
     *
     * <pre>
     *挑战玩家数据
     * </pre>
     */
    ArenaPb.PBArenaChallengePlayerInfoOrBuilder getPlayerInfosOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code PBArenaChallengeInfo}
   *
   * <pre>
   *竞技场玩家获取被挑战排名等数据列表
   * </pre>
   */
  public static final class PBArenaChallengeInfo extends
      com.google.protobuf.GeneratedMessage
      implements PBArenaChallengeInfoOrBuilder {
    // Use PBArenaChallengeInfo.newBuilder() to construct.
    private PBArenaChallengeInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private PBArenaChallengeInfo(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final PBArenaChallengeInfo defaultInstance;
    public static PBArenaChallengeInfo getDefaultInstance() {
      return defaultInstance;
    }

    public PBArenaChallengeInfo getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private PBArenaChallengeInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                playerInfos_ = new java.util.ArrayList<ArenaPb.PBArenaChallengePlayerInfo>();
                mutable_bitField0_ |= 0x00000001;
              }
              playerInfos_.add(input.readMessage(ArenaPb.PBArenaChallengePlayerInfo.PARSER, extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          playerInfos_ = java.util.Collections.unmodifiableList(playerInfos_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ArenaPb.internal_static_PBArenaChallengeInfo_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ArenaPb.internal_static_PBArenaChallengeInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ArenaPb.PBArenaChallengeInfo.class, ArenaPb.PBArenaChallengeInfo.Builder.class);
    }

    public static com.google.protobuf.Parser<PBArenaChallengeInfo> PARSER =
        new com.google.protobuf.AbstractParser<PBArenaChallengeInfo>() {
      public PBArenaChallengeInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PBArenaChallengeInfo(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<PBArenaChallengeInfo> getParserForType() {
      return PARSER;
    }

    // repeated .PBArenaChallengePlayerInfo playerInfos = 1;
    public static final int PLAYERINFOS_FIELD_NUMBER = 1;
    private java.util.List<ArenaPb.PBArenaChallengePlayerInfo> playerInfos_;
    /**
     * <code>repeated .PBArenaChallengePlayerInfo playerInfos = 1;</code>
     *
     * <pre>
     *挑战玩家数据
     * </pre>
     */
    public java.util.List<ArenaPb.PBArenaChallengePlayerInfo> getPlayerInfosList() {
      return playerInfos_;
    }
    /**
     * <code>repeated .PBArenaChallengePlayerInfo playerInfos = 1;</code>
     *
     * <pre>
     *挑战玩家数据
     * </pre>
     */
    public java.util.List<? extends ArenaPb.PBArenaChallengePlayerInfoOrBuilder> 
        getPlayerInfosOrBuilderList() {
      return playerInfos_;
    }
    /**
     * <code>repeated .PBArenaChallengePlayerInfo playerInfos = 1;</code>
     *
     * <pre>
     *挑战玩家数据
     * </pre>
     */
    public int getPlayerInfosCount() {
      return playerInfos_.size();
    }
    /**
     * <code>repeated .PBArenaChallengePlayerInfo playerInfos = 1;</code>
     *
     * <pre>
     *挑战玩家数据
     * </pre>
     */
    public ArenaPb.PBArenaChallengePlayerInfo getPlayerInfos(int index) {
      return playerInfos_.get(index);
    }
    /**
     * <code>repeated .PBArenaChallengePlayerInfo playerInfos = 1;</code>
     *
     * <pre>
     *挑战玩家数据
     * </pre>
     */
    public ArenaPb.PBArenaChallengePlayerInfoOrBuilder getPlayerInfosOrBuilder(
        int index) {
      return playerInfos_.get(index);
    }

    private void initFields() {
      playerInfos_ = java.util.Collections.emptyList();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      for (int i = 0; i < getPlayerInfosCount(); i++) {
        if (!getPlayerInfos(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      for (int i = 0; i < playerInfos_.size(); i++) {
        output.writeMessage(1, playerInfos_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < playerInfos_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, playerInfos_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static ArenaPb.PBArenaChallengeInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ArenaPb.PBArenaChallengeInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ArenaPb.PBArenaChallengeInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ArenaPb.PBArenaChallengeInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ArenaPb.PBArenaChallengeInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static ArenaPb.PBArenaChallengeInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static ArenaPb.PBArenaChallengeInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static ArenaPb.PBArenaChallengeInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static ArenaPb.PBArenaChallengeInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static ArenaPb.PBArenaChallengeInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(ArenaPb.PBArenaChallengeInfo prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code PBArenaChallengeInfo}
     *
     * <pre>
     *竞技场玩家获取被挑战排名等数据列表
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements ArenaPb.PBArenaChallengeInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ArenaPb.internal_static_PBArenaChallengeInfo_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ArenaPb.internal_static_PBArenaChallengeInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ArenaPb.PBArenaChallengeInfo.class, ArenaPb.PBArenaChallengeInfo.Builder.class);
      }

      // Construct using ArenaPb.PBArenaChallengeInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getPlayerInfosFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (playerInfosBuilder_ == null) {
          playerInfos_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          playerInfosBuilder_.clear();
        }
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ArenaPb.internal_static_PBArenaChallengeInfo_descriptor;
      }

      public ArenaPb.PBArenaChallengeInfo getDefaultInstanceForType() {
        return ArenaPb.PBArenaChallengeInfo.getDefaultInstance();
      }

      public ArenaPb.PBArenaChallengeInfo build() {
        ArenaPb.PBArenaChallengeInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public ArenaPb.PBArenaChallengeInfo buildPartial() {
        ArenaPb.PBArenaChallengeInfo result = new ArenaPb.PBArenaChallengeInfo(this);
        int from_bitField0_ = bitField0_;
        if (playerInfosBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001)) {
            playerInfos_ = java.util.Collections.unmodifiableList(playerInfos_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.playerInfos_ = playerInfos_;
        } else {
          result.playerInfos_ = playerInfosBuilder_.build();
        }
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof ArenaPb.PBArenaChallengeInfo) {
          return mergeFrom((ArenaPb.PBArenaChallengeInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(ArenaPb.PBArenaChallengeInfo other) {
        if (other == ArenaPb.PBArenaChallengeInfo.getDefaultInstance()) return this;
        if (playerInfosBuilder_ == null) {
          if (!other.playerInfos_.isEmpty()) {
            if (playerInfos_.isEmpty()) {
              playerInfos_ = other.playerInfos_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensurePlayerInfosIsMutable();
              playerInfos_.addAll(other.playerInfos_);
            }
            onChanged();
          }
        } else {
          if (!other.playerInfos_.isEmpty()) {
            if (playerInfosBuilder_.isEmpty()) {
              playerInfosBuilder_.dispose();
              playerInfosBuilder_ = null;
              playerInfos_ = other.playerInfos_;
              bitField0_ = (bitField0_ & ~0x00000001);
              playerInfosBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getPlayerInfosFieldBuilder() : null;
            } else {
              playerInfosBuilder_.addAllMessages(other.playerInfos_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        for (int i = 0; i < getPlayerInfosCount(); i++) {
          if (!getPlayerInfos(i).isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        ArenaPb.PBArenaChallengeInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (ArenaPb.PBArenaChallengeInfo) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // repeated .PBArenaChallengePlayerInfo playerInfos = 1;
      private java.util.List<ArenaPb.PBArenaChallengePlayerInfo> playerInfos_ =
        java.util.Collections.emptyList();
      private void ensurePlayerInfosIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          playerInfos_ = new java.util.ArrayList<ArenaPb.PBArenaChallengePlayerInfo>(playerInfos_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          ArenaPb.PBArenaChallengePlayerInfo, ArenaPb.PBArenaChallengePlayerInfo.Builder, ArenaPb.PBArenaChallengePlayerInfoOrBuilder> playerInfosBuilder_;

      /**
       * <code>repeated .PBArenaChallengePlayerInfo playerInfos = 1;</code>
       *
       * <pre>
       *挑战玩家数据
       * </pre>
       */
      public java.util.List<ArenaPb.PBArenaChallengePlayerInfo> getPlayerInfosList() {
        if (playerInfosBuilder_ == null) {
          return java.util.Collections.unmodifiableList(playerInfos_);
        } else {
          return playerInfosBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .PBArenaChallengePlayerInfo playerInfos = 1;</code>
       *
       * <pre>
       *挑战玩家数据
       * </pre>
       */
      public int getPlayerInfosCount() {
        if (playerInfosBuilder_ == null) {
          return playerInfos_.size();
        } else {
          return playerInfosBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .PBArenaChallengePlayerInfo playerInfos = 1;</code>
       *
       * <pre>
       *挑战玩家数据
       * </pre>
       */
      public ArenaPb.PBArenaChallengePlayerInfo getPlayerInfos(int index) {
        if (playerInfosBuilder_ == null) {
          return playerInfos_.get(index);
        } else {
          return playerInfosBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .PBArenaChallengePlayerInfo playerInfos = 1;</code>
       *
       * <pre>
       *挑战玩家数据
       * </pre>
       */
      public Builder setPlayerInfos(
          int index, ArenaPb.PBArenaChallengePlayerInfo value) {
        if (playerInfosBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePlayerInfosIsMutable();
          playerInfos_.set(index, value);
          onChanged();
        } else {
          playerInfosBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .PBArenaChallengePlayerInfo playerInfos = 1;</code>
       *
       * <pre>
       *挑战玩家数据
       * </pre>
       */
      public Builder setPlayerInfos(
          int index, ArenaPb.PBArenaChallengePlayerInfo.Builder builderForValue) {
        if (playerInfosBuilder_ == null) {
          ensurePlayerInfosIsMutable();
          playerInfos_.set(index, builderForValue.build());
          onChanged();
        } else {
          playerInfosBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .PBArenaChallengePlayerInfo playerInfos = 1;</code>
       *
       * <pre>
       *挑战玩家数据
       * </pre>
       */
      public Builder addPlayerInfos(ArenaPb.PBArenaChallengePlayerInfo value) {
        if (playerInfosBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePlayerInfosIsMutable();
          playerInfos_.add(value);
          onChanged();
        } else {
          playerInfosBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .PBArenaChallengePlayerInfo playerInfos = 1;</code>
       *
       * <pre>
       *挑战玩家数据
       * </pre>
       */
      public Builder addPlayerInfos(
          int index, ArenaPb.PBArenaChallengePlayerInfo value) {
        if (playerInfosBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePlayerInfosIsMutable();
          playerInfos_.add(index, value);
          onChanged();
        } else {
          playerInfosBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .PBArenaChallengePlayerInfo playerInfos = 1;</code>
       *
       * <pre>
       *挑战玩家数据
       * </pre>
       */
      public Builder addPlayerInfos(
          ArenaPb.PBArenaChallengePlayerInfo.Builder builderForValue) {
        if (playerInfosBuilder_ == null) {
          ensurePlayerInfosIsMutable();
          playerInfos_.add(builderForValue.build());
          onChanged();
        } else {
          playerInfosBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .PBArenaChallengePlayerInfo playerInfos = 1;</code>
       *
       * <pre>
       *挑战玩家数据
       * </pre>
       */
      public Builder addPlayerInfos(
          int index, ArenaPb.PBArenaChallengePlayerInfo.Builder builderForValue) {
        if (playerInfosBuilder_ == null) {
          ensurePlayerInfosIsMutable();
          playerInfos_.add(index, builderForValue.build());
          onChanged();
        } else {
          playerInfosBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .PBArenaChallengePlayerInfo playerInfos = 1;</code>
       *
       * <pre>
       *挑战玩家数据
       * </pre>
       */
      public Builder addAllPlayerInfos(
          java.lang.Iterable<? extends ArenaPb.PBArenaChallengePlayerInfo> values) {
        if (playerInfosBuilder_ == null) {
          ensurePlayerInfosIsMutable();
          super.addAll(values, playerInfos_);
          onChanged();
        } else {
          playerInfosBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .PBArenaChallengePlayerInfo playerInfos = 1;</code>
       *
       * <pre>
       *挑战玩家数据
       * </pre>
       */
      public Builder clearPlayerInfos() {
        if (playerInfosBuilder_ == null) {
          playerInfos_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          playerInfosBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .PBArenaChallengePlayerInfo playerInfos = 1;</code>
       *
       * <pre>
       *挑战玩家数据
       * </pre>
       */
      public Builder removePlayerInfos(int index) {
        if (playerInfosBuilder_ == null) {
          ensurePlayerInfosIsMutable();
          playerInfos_.remove(index);
          onChanged();
        } else {
          playerInfosBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .PBArenaChallengePlayerInfo playerInfos = 1;</code>
       *
       * <pre>
       *挑战玩家数据
       * </pre>
       */
      public ArenaPb.PBArenaChallengePlayerInfo.Builder getPlayerInfosBuilder(
          int index) {
        return getPlayerInfosFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .PBArenaChallengePlayerInfo playerInfos = 1;</code>
       *
       * <pre>
       *挑战玩家数据
       * </pre>
       */
      public ArenaPb.PBArenaChallengePlayerInfoOrBuilder getPlayerInfosOrBuilder(
          int index) {
        if (playerInfosBuilder_ == null) {
          return playerInfos_.get(index);  } else {
          return playerInfosBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .PBArenaChallengePlayerInfo playerInfos = 1;</code>
       *
       * <pre>
       *挑战玩家数据
       * </pre>
       */
      public java.util.List<? extends ArenaPb.PBArenaChallengePlayerInfoOrBuilder> 
           getPlayerInfosOrBuilderList() {
        if (playerInfosBuilder_ != null) {
          return playerInfosBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(playerInfos_);
        }
      }
      /**
       * <code>repeated .PBArenaChallengePlayerInfo playerInfos = 1;</code>
       *
       * <pre>
       *挑战玩家数据
       * </pre>
       */
      public ArenaPb.PBArenaChallengePlayerInfo.Builder addPlayerInfosBuilder() {
        return getPlayerInfosFieldBuilder().addBuilder(
            ArenaPb.PBArenaChallengePlayerInfo.getDefaultInstance());
      }
      /**
       * <code>repeated .PBArenaChallengePlayerInfo playerInfos = 1;</code>
       *
       * <pre>
       *挑战玩家数据
       * </pre>
       */
      public ArenaPb.PBArenaChallengePlayerInfo.Builder addPlayerInfosBuilder(
          int index) {
        return getPlayerInfosFieldBuilder().addBuilder(
            index, ArenaPb.PBArenaChallengePlayerInfo.getDefaultInstance());
      }
      /**
       * <code>repeated .PBArenaChallengePlayerInfo playerInfos = 1;</code>
       *
       * <pre>
       *挑战玩家数据
       * </pre>
       */
      public java.util.List<ArenaPb.PBArenaChallengePlayerInfo.Builder> 
           getPlayerInfosBuilderList() {
        return getPlayerInfosFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          ArenaPb.PBArenaChallengePlayerInfo, ArenaPb.PBArenaChallengePlayerInfo.Builder, ArenaPb.PBArenaChallengePlayerInfoOrBuilder> 
          getPlayerInfosFieldBuilder() {
        if (playerInfosBuilder_ == null) {
          playerInfosBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              ArenaPb.PBArenaChallengePlayerInfo, ArenaPb.PBArenaChallengePlayerInfo.Builder, ArenaPb.PBArenaChallengePlayerInfoOrBuilder>(
                  playerInfos_,
                  ((bitField0_ & 0x00000001) == 0x00000001),
                  getParentForChildren(),
                  isClean());
          playerInfos_ = null;
        }
        return playerInfosBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:PBArenaChallengeInfo)
    }

    static {
      defaultInstance = new PBArenaChallengeInfo(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:PBArenaChallengeInfo)
  }

  public interface PBArenaChallengePlayerInfoOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int64 guid = 1;
    /**
     * <code>required int64 guid = 1;</code>
     *
     * <pre>
     *玩家guid
     * </pre>
     */
    boolean hasGuid();
    /**
     * <code>required int64 guid = 1;</code>
     *
     * <pre>
     *玩家guid
     * </pre>
     */
    long getGuid();

    // optional string name = 2;
    /**
     * <code>optional string name = 2;</code>
     *
     * <pre>
     *玩家名字
     * </pre>
     */
    boolean hasName();
    /**
     * <code>optional string name = 2;</code>
     *
     * <pre>
     *玩家名字
     * </pre>
     */
    java.lang.String getName();
    /**
     * <code>optional string name = 2;</code>
     *
     * <pre>
     *玩家名字
     * </pre>
     */
    com.google.protobuf.ByteString
        getNameBytes();

    // optional int32 icon = 3;
    /**
     * <code>optional int32 icon = 3;</code>
     *
     * <pre>
     *玩家头像
     * </pre>
     */
    boolean hasIcon();
    /**
     * <code>optional int32 icon = 3;</code>
     *
     * <pre>
     *玩家头像
     * </pre>
     */
    int getIcon();

    // optional int32 level = 4;
    /**
     * <code>optional int32 level = 4;</code>
     *
     * <pre>
     *玩家等级
     * </pre>
     */
    boolean hasLevel();
    /**
     * <code>optional int32 level = 4;</code>
     *
     * <pre>
     *玩家等级
     * </pre>
     */
    int getLevel();

    // optional int32 rank = 5;
    /**
     * <code>optional int32 rank = 5;</code>
     *
     * <pre>
     *玩家排名
     * </pre>
     */
    boolean hasRank();
    /**
     * <code>optional int32 rank = 5;</code>
     *
     * <pre>
     *玩家排名
     * </pre>
     */
    int getRank();

    // optional int64 fight = 6;
    /**
     * <code>optional int64 fight = 6;</code>
     *
     * <pre>
     *玩家战斗力
     * </pre>
     */
    boolean hasFight();
    /**
     * <code>optional int64 fight = 6;</code>
     *
     * <pre>
     *玩家战斗力
     * </pre>
     */
    long getFight();

    // optional int64 score = 7;
    /**
     * <code>optional int64 score = 7;</code>
     *
     * <pre>
     *玩家积分
     * </pre>
     */
    boolean hasScore();
    /**
     * <code>optional int64 score = 7;</code>
     *
     * <pre>
     *玩家积分
     * </pre>
     */
    long getScore();
  }
  /**
   * Protobuf type {@code PBArenaChallengePlayerInfo}
   *
   * <pre>
   *竞技场玩家获取被挑战玩家的详细排名等数据
   * </pre>
   */
  public static final class PBArenaChallengePlayerInfo extends
      com.google.protobuf.GeneratedMessage
      implements PBArenaChallengePlayerInfoOrBuilder {
    // Use PBArenaChallengePlayerInfo.newBuilder() to construct.
    private PBArenaChallengePlayerInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private PBArenaChallengePlayerInfo(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final PBArenaChallengePlayerInfo defaultInstance;
    public static PBArenaChallengePlayerInfo getDefaultInstance() {
      return defaultInstance;
    }

    public PBArenaChallengePlayerInfo getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private PBArenaChallengePlayerInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              guid_ = input.readInt64();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              name_ = input.readBytes();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              icon_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              level_ = input.readInt32();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              rank_ = input.readInt32();
              break;
            }
            case 48: {
              bitField0_ |= 0x00000020;
              fight_ = input.readInt64();
              break;
            }
            case 56: {
              bitField0_ |= 0x00000040;
              score_ = input.readInt64();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ArenaPb.internal_static_PBArenaChallengePlayerInfo_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ArenaPb.internal_static_PBArenaChallengePlayerInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ArenaPb.PBArenaChallengePlayerInfo.class, ArenaPb.PBArenaChallengePlayerInfo.Builder.class);
    }

    public static com.google.protobuf.Parser<PBArenaChallengePlayerInfo> PARSER =
        new com.google.protobuf.AbstractParser<PBArenaChallengePlayerInfo>() {
      public PBArenaChallengePlayerInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PBArenaChallengePlayerInfo(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<PBArenaChallengePlayerInfo> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int64 guid = 1;
    public static final int GUID_FIELD_NUMBER = 1;
    private long guid_;
    /**
     * <code>required int64 guid = 1;</code>
     *
     * <pre>
     *玩家guid
     * </pre>
     */
    public boolean hasGuid() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int64 guid = 1;</code>
     *
     * <pre>
     *玩家guid
     * </pre>
     */
    public long getGuid() {
      return guid_;
    }

    // optional string name = 2;
    public static final int NAME_FIELD_NUMBER = 2;
    private java.lang.Object name_;
    /**
     * <code>optional string name = 2;</code>
     *
     * <pre>
     *玩家名字
     * </pre>
     */
    public boolean hasName() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional string name = 2;</code>
     *
     * <pre>
     *玩家名字
     * </pre>
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          name_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string name = 2;</code>
     *
     * <pre>
     *玩家名字
     * </pre>
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional int32 icon = 3;
    public static final int ICON_FIELD_NUMBER = 3;
    private int icon_;
    /**
     * <code>optional int32 icon = 3;</code>
     *
     * <pre>
     *玩家头像
     * </pre>
     */
    public boolean hasIcon() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional int32 icon = 3;</code>
     *
     * <pre>
     *玩家头像
     * </pre>
     */
    public int getIcon() {
      return icon_;
    }

    // optional int32 level = 4;
    public static final int LEVEL_FIELD_NUMBER = 4;
    private int level_;
    /**
     * <code>optional int32 level = 4;</code>
     *
     * <pre>
     *玩家等级
     * </pre>
     */
    public boolean hasLevel() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional int32 level = 4;</code>
     *
     * <pre>
     *玩家等级
     * </pre>
     */
    public int getLevel() {
      return level_;
    }

    // optional int32 rank = 5;
    public static final int RANK_FIELD_NUMBER = 5;
    private int rank_;
    /**
     * <code>optional int32 rank = 5;</code>
     *
     * <pre>
     *玩家排名
     * </pre>
     */
    public boolean hasRank() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>optional int32 rank = 5;</code>
     *
     * <pre>
     *玩家排名
     * </pre>
     */
    public int getRank() {
      return rank_;
    }

    // optional int64 fight = 6;
    public static final int FIGHT_FIELD_NUMBER = 6;
    private long fight_;
    /**
     * <code>optional int64 fight = 6;</code>
     *
     * <pre>
     *玩家战斗力
     * </pre>
     */
    public boolean hasFight() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <code>optional int64 fight = 6;</code>
     *
     * <pre>
     *玩家战斗力
     * </pre>
     */
    public long getFight() {
      return fight_;
    }

    // optional int64 score = 7;
    public static final int SCORE_FIELD_NUMBER = 7;
    private long score_;
    /**
     * <code>optional int64 score = 7;</code>
     *
     * <pre>
     *玩家积分
     * </pre>
     */
    public boolean hasScore() {
      return ((bitField0_ & 0x00000040) == 0x00000040);
    }
    /**
     * <code>optional int64 score = 7;</code>
     *
     * <pre>
     *玩家积分
     * </pre>
     */
    public long getScore() {
      return score_;
    }

    private void initFields() {
      guid_ = 0L;
      name_ = "";
      icon_ = 0;
      level_ = 0;
      rank_ = 0;
      fight_ = 0L;
      score_ = 0L;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasGuid()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(1, guid_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getNameBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt32(3, icon_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeInt32(4, level_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeInt32(5, rank_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        output.writeInt64(6, fight_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        output.writeInt64(7, score_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, guid_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getNameBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, icon_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, level_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, rank_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(6, fight_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(7, score_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static ArenaPb.PBArenaChallengePlayerInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ArenaPb.PBArenaChallengePlayerInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ArenaPb.PBArenaChallengePlayerInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ArenaPb.PBArenaChallengePlayerInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ArenaPb.PBArenaChallengePlayerInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static ArenaPb.PBArenaChallengePlayerInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static ArenaPb.PBArenaChallengePlayerInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static ArenaPb.PBArenaChallengePlayerInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static ArenaPb.PBArenaChallengePlayerInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static ArenaPb.PBArenaChallengePlayerInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(ArenaPb.PBArenaChallengePlayerInfo prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code PBArenaChallengePlayerInfo}
     *
     * <pre>
     *竞技场玩家获取被挑战玩家的详细排名等数据
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements ArenaPb.PBArenaChallengePlayerInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ArenaPb.internal_static_PBArenaChallengePlayerInfo_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ArenaPb.internal_static_PBArenaChallengePlayerInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ArenaPb.PBArenaChallengePlayerInfo.class, ArenaPb.PBArenaChallengePlayerInfo.Builder.class);
      }

      // Construct using ArenaPb.PBArenaChallengePlayerInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        guid_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        name_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        icon_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        level_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        rank_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        fight_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000020);
        score_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000040);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ArenaPb.internal_static_PBArenaChallengePlayerInfo_descriptor;
      }

      public ArenaPb.PBArenaChallengePlayerInfo getDefaultInstanceForType() {
        return ArenaPb.PBArenaChallengePlayerInfo.getDefaultInstance();
      }

      public ArenaPb.PBArenaChallengePlayerInfo build() {
        ArenaPb.PBArenaChallengePlayerInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public ArenaPb.PBArenaChallengePlayerInfo buildPartial() {
        ArenaPb.PBArenaChallengePlayerInfo result = new ArenaPb.PBArenaChallengePlayerInfo(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.guid_ = guid_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.name_ = name_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.icon_ = icon_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.level_ = level_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.rank_ = rank_;
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000020;
        }
        result.fight_ = fight_;
        if (((from_bitField0_ & 0x00000040) == 0x00000040)) {
          to_bitField0_ |= 0x00000040;
        }
        result.score_ = score_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof ArenaPb.PBArenaChallengePlayerInfo) {
          return mergeFrom((ArenaPb.PBArenaChallengePlayerInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(ArenaPb.PBArenaChallengePlayerInfo other) {
        if (other == ArenaPb.PBArenaChallengePlayerInfo.getDefaultInstance()) return this;
        if (other.hasGuid()) {
          setGuid(other.getGuid());
        }
        if (other.hasName()) {
          bitField0_ |= 0x00000002;
          name_ = other.name_;
          onChanged();
        }
        if (other.hasIcon()) {
          setIcon(other.getIcon());
        }
        if (other.hasLevel()) {
          setLevel(other.getLevel());
        }
        if (other.hasRank()) {
          setRank(other.getRank());
        }
        if (other.hasFight()) {
          setFight(other.getFight());
        }
        if (other.hasScore()) {
          setScore(other.getScore());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasGuid()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        ArenaPb.PBArenaChallengePlayerInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (ArenaPb.PBArenaChallengePlayerInfo) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int64 guid = 1;
      private long guid_ ;
      /**
       * <code>required int64 guid = 1;</code>
       *
       * <pre>
       *玩家guid
       * </pre>
       */
      public boolean hasGuid() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int64 guid = 1;</code>
       *
       * <pre>
       *玩家guid
       * </pre>
       */
      public long getGuid() {
        return guid_;
      }
      /**
       * <code>required int64 guid = 1;</code>
       *
       * <pre>
       *玩家guid
       * </pre>
       */
      public Builder setGuid(long value) {
        bitField0_ |= 0x00000001;
        guid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int64 guid = 1;</code>
       *
       * <pre>
       *玩家guid
       * </pre>
       */
      public Builder clearGuid() {
        bitField0_ = (bitField0_ & ~0x00000001);
        guid_ = 0L;
        onChanged();
        return this;
      }

      // optional string name = 2;
      private java.lang.Object name_ = "";
      /**
       * <code>optional string name = 2;</code>
       *
       * <pre>
       *玩家名字
       * </pre>
       */
      public boolean hasName() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional string name = 2;</code>
       *
       * <pre>
       *玩家名字
       * </pre>
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          name_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string name = 2;</code>
       *
       * <pre>
       *玩家名字
       * </pre>
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string name = 2;</code>
       *
       * <pre>
       *玩家名字
       * </pre>
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        name_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string name = 2;</code>
       *
       * <pre>
       *玩家名字
       * </pre>
       */
      public Builder clearName() {
        bitField0_ = (bitField0_ & ~0x00000002);
        name_ = getDefaultInstance().getName();
        onChanged();
        return this;
      }
      /**
       * <code>optional string name = 2;</code>
       *
       * <pre>
       *玩家名字
       * </pre>
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        name_ = value;
        onChanged();
        return this;
      }

      // optional int32 icon = 3;
      private int icon_ ;
      /**
       * <code>optional int32 icon = 3;</code>
       *
       * <pre>
       *玩家头像
       * </pre>
       */
      public boolean hasIcon() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional int32 icon = 3;</code>
       *
       * <pre>
       *玩家头像
       * </pre>
       */
      public int getIcon() {
        return icon_;
      }
      /**
       * <code>optional int32 icon = 3;</code>
       *
       * <pre>
       *玩家头像
       * </pre>
       */
      public Builder setIcon(int value) {
        bitField0_ |= 0x00000004;
        icon_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 icon = 3;</code>
       *
       * <pre>
       *玩家头像
       * </pre>
       */
      public Builder clearIcon() {
        bitField0_ = (bitField0_ & ~0x00000004);
        icon_ = 0;
        onChanged();
        return this;
      }

      // optional int32 level = 4;
      private int level_ ;
      /**
       * <code>optional int32 level = 4;</code>
       *
       * <pre>
       *玩家等级
       * </pre>
       */
      public boolean hasLevel() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional int32 level = 4;</code>
       *
       * <pre>
       *玩家等级
       * </pre>
       */
      public int getLevel() {
        return level_;
      }
      /**
       * <code>optional int32 level = 4;</code>
       *
       * <pre>
       *玩家等级
       * </pre>
       */
      public Builder setLevel(int value) {
        bitField0_ |= 0x00000008;
        level_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 level = 4;</code>
       *
       * <pre>
       *玩家等级
       * </pre>
       */
      public Builder clearLevel() {
        bitField0_ = (bitField0_ & ~0x00000008);
        level_ = 0;
        onChanged();
        return this;
      }

      // optional int32 rank = 5;
      private int rank_ ;
      /**
       * <code>optional int32 rank = 5;</code>
       *
       * <pre>
       *玩家排名
       * </pre>
       */
      public boolean hasRank() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional int32 rank = 5;</code>
       *
       * <pre>
       *玩家排名
       * </pre>
       */
      public int getRank() {
        return rank_;
      }
      /**
       * <code>optional int32 rank = 5;</code>
       *
       * <pre>
       *玩家排名
       * </pre>
       */
      public Builder setRank(int value) {
        bitField0_ |= 0x00000010;
        rank_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 rank = 5;</code>
       *
       * <pre>
       *玩家排名
       * </pre>
       */
      public Builder clearRank() {
        bitField0_ = (bitField0_ & ~0x00000010);
        rank_ = 0;
        onChanged();
        return this;
      }

      // optional int64 fight = 6;
      private long fight_ ;
      /**
       * <code>optional int64 fight = 6;</code>
       *
       * <pre>
       *玩家战斗力
       * </pre>
       */
      public boolean hasFight() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <code>optional int64 fight = 6;</code>
       *
       * <pre>
       *玩家战斗力
       * </pre>
       */
      public long getFight() {
        return fight_;
      }
      /**
       * <code>optional int64 fight = 6;</code>
       *
       * <pre>
       *玩家战斗力
       * </pre>
       */
      public Builder setFight(long value) {
        bitField0_ |= 0x00000020;
        fight_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 fight = 6;</code>
       *
       * <pre>
       *玩家战斗力
       * </pre>
       */
      public Builder clearFight() {
        bitField0_ = (bitField0_ & ~0x00000020);
        fight_ = 0L;
        onChanged();
        return this;
      }

      // optional int64 score = 7;
      private long score_ ;
      /**
       * <code>optional int64 score = 7;</code>
       *
       * <pre>
       *玩家积分
       * </pre>
       */
      public boolean hasScore() {
        return ((bitField0_ & 0x00000040) == 0x00000040);
      }
      /**
       * <code>optional int64 score = 7;</code>
       *
       * <pre>
       *玩家积分
       * </pre>
       */
      public long getScore() {
        return score_;
      }
      /**
       * <code>optional int64 score = 7;</code>
       *
       * <pre>
       *玩家积分
       * </pre>
       */
      public Builder setScore(long value) {
        bitField0_ |= 0x00000040;
        score_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 score = 7;</code>
       *
       * <pre>
       *玩家积分
       * </pre>
       */
      public Builder clearScore() {
        bitField0_ = (bitField0_ & ~0x00000040);
        score_ = 0L;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:PBArenaChallengePlayerInfo)
    }

    static {
      defaultInstance = new PBArenaChallengePlayerInfo(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:PBArenaChallengePlayerInfo)
  }

  public interface PBArenaChallengePlayerDetailInfoOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int64 guid = 1;
    /**
     * <code>required int64 guid = 1;</code>
     *
     * <pre>
     *玩家guid
     * </pre>
     */
    boolean hasGuid();
    /**
     * <code>required int64 guid = 1;</code>
     *
     * <pre>
     *玩家guid
     * </pre>
     */
    long getGuid();

    // optional string playerName = 2;
    /**
     * <code>optional string playerName = 2;</code>
     *
     * <pre>
     *玩家名字
     * </pre>
     */
    boolean hasPlayerName();
    /**
     * <code>optional string playerName = 2;</code>
     *
     * <pre>
     *玩家名字
     * </pre>
     */
    java.lang.String getPlayerName();
    /**
     * <code>optional string playerName = 2;</code>
     *
     * <pre>
     *玩家名字
     * </pre>
     */
    com.google.protobuf.ByteString
        getPlayerNameBytes();

    // required int32 wulueSetIndex = 3;
    /**
     * <code>required int32 wulueSetIndex = 3;</code>
     *
     * <pre>
     *武略设置
     * </pre>
     */
    boolean hasWulueSetIndex();
    /**
     * <code>required int32 wulueSetIndex = 3;</code>
     *
     * <pre>
     *武略设置
     * </pre>
     */
    int getWulueSetIndex();

    // required int32 talentSetIndex = 4;
    /**
     * <code>required int32 talentSetIndex = 4;</code>
     *
     * <pre>
     *天赋设置
     * </pre>
     */
    boolean hasTalentSetIndex();
    /**
     * <code>required int32 talentSetIndex = 4;</code>
     *
     * <pre>
     *天赋设置
     * </pre>
     */
    int getTalentSetIndex();

    // repeated .PBHeroLineUpInfo heroList = 5;
    /**
     * <code>repeated .PBHeroLineUpInfo heroList = 5;</code>
     *
     * <pre>
     *武将列表
     * </pre>
     */
    java.util.List<JiaochangPb.PBHeroLineUpInfo> 
        getHeroListList();
    /**
     * <code>repeated .PBHeroLineUpInfo heroList = 5;</code>
     *
     * <pre>
     *武将列表
     * </pre>
     */
    JiaochangPb.PBHeroLineUpInfo getHeroList(int index);
    /**
     * <code>repeated .PBHeroLineUpInfo heroList = 5;</code>
     *
     * <pre>
     *武将列表
     * </pre>
     */
    int getHeroListCount();
    /**
     * <code>repeated .PBHeroLineUpInfo heroList = 5;</code>
     *
     * <pre>
     *武将列表
     * </pre>
     */
    java.util.List<? extends JiaochangPb.PBHeroLineUpInfoOrBuilder> 
        getHeroListOrBuilderList();
    /**
     * <code>repeated .PBHeroLineUpInfo heroList = 5;</code>
     *
     * <pre>
     *武将列表
     * </pre>
     */
    JiaochangPb.PBHeroLineUpInfoOrBuilder getHeroListOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code PBArenaChallengePlayerDetailInfo}
   *
   * <pre>
   *竞技场玩家攻守阵容数据
   * </pre>
   */
  public static final class PBArenaChallengePlayerDetailInfo extends
      com.google.protobuf.GeneratedMessage
      implements PBArenaChallengePlayerDetailInfoOrBuilder {
    // Use PBArenaChallengePlayerDetailInfo.newBuilder() to construct.
    private PBArenaChallengePlayerDetailInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private PBArenaChallengePlayerDetailInfo(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final PBArenaChallengePlayerDetailInfo defaultInstance;
    public static PBArenaChallengePlayerDetailInfo getDefaultInstance() {
      return defaultInstance;
    }

    public PBArenaChallengePlayerDetailInfo getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private PBArenaChallengePlayerDetailInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              guid_ = input.readInt64();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              playerName_ = input.readBytes();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              wulueSetIndex_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              talentSetIndex_ = input.readInt32();
              break;
            }
            case 42: {
              if (!((mutable_bitField0_ & 0x00000010) == 0x00000010)) {
                heroList_ = new java.util.ArrayList<JiaochangPb.PBHeroLineUpInfo>();
                mutable_bitField0_ |= 0x00000010;
              }
              heroList_.add(input.readMessage(JiaochangPb.PBHeroLineUpInfo.PARSER, extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000010) == 0x00000010)) {
          heroList_ = java.util.Collections.unmodifiableList(heroList_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ArenaPb.internal_static_PBArenaChallengePlayerDetailInfo_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ArenaPb.internal_static_PBArenaChallengePlayerDetailInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ArenaPb.PBArenaChallengePlayerDetailInfo.class, ArenaPb.PBArenaChallengePlayerDetailInfo.Builder.class);
    }

    public static com.google.protobuf.Parser<PBArenaChallengePlayerDetailInfo> PARSER =
        new com.google.protobuf.AbstractParser<PBArenaChallengePlayerDetailInfo>() {
      public PBArenaChallengePlayerDetailInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PBArenaChallengePlayerDetailInfo(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<PBArenaChallengePlayerDetailInfo> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int64 guid = 1;
    public static final int GUID_FIELD_NUMBER = 1;
    private long guid_;
    /**
     * <code>required int64 guid = 1;</code>
     *
     * <pre>
     *玩家guid
     * </pre>
     */
    public boolean hasGuid() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int64 guid = 1;</code>
     *
     * <pre>
     *玩家guid
     * </pre>
     */
    public long getGuid() {
      return guid_;
    }

    // optional string playerName = 2;
    public static final int PLAYERNAME_FIELD_NUMBER = 2;
    private java.lang.Object playerName_;
    /**
     * <code>optional string playerName = 2;</code>
     *
     * <pre>
     *玩家名字
     * </pre>
     */
    public boolean hasPlayerName() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional string playerName = 2;</code>
     *
     * <pre>
     *玩家名字
     * </pre>
     */
    public java.lang.String getPlayerName() {
      java.lang.Object ref = playerName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          playerName_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string playerName = 2;</code>
     *
     * <pre>
     *玩家名字
     * </pre>
     */
    public com.google.protobuf.ByteString
        getPlayerNameBytes() {
      java.lang.Object ref = playerName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        playerName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required int32 wulueSetIndex = 3;
    public static final int WULUESETINDEX_FIELD_NUMBER = 3;
    private int wulueSetIndex_;
    /**
     * <code>required int32 wulueSetIndex = 3;</code>
     *
     * <pre>
     *武略设置
     * </pre>
     */
    public boolean hasWulueSetIndex() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>required int32 wulueSetIndex = 3;</code>
     *
     * <pre>
     *武略设置
     * </pre>
     */
    public int getWulueSetIndex() {
      return wulueSetIndex_;
    }

    // required int32 talentSetIndex = 4;
    public static final int TALENTSETINDEX_FIELD_NUMBER = 4;
    private int talentSetIndex_;
    /**
     * <code>required int32 talentSetIndex = 4;</code>
     *
     * <pre>
     *天赋设置
     * </pre>
     */
    public boolean hasTalentSetIndex() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>required int32 talentSetIndex = 4;</code>
     *
     * <pre>
     *天赋设置
     * </pre>
     */
    public int getTalentSetIndex() {
      return talentSetIndex_;
    }

    // repeated .PBHeroLineUpInfo heroList = 5;
    public static final int HEROLIST_FIELD_NUMBER = 5;
    private java.util.List<JiaochangPb.PBHeroLineUpInfo> heroList_;
    /**
     * <code>repeated .PBHeroLineUpInfo heroList = 5;</code>
     *
     * <pre>
     *武将列表
     * </pre>
     */
    public java.util.List<JiaochangPb.PBHeroLineUpInfo> getHeroListList() {
      return heroList_;
    }
    /**
     * <code>repeated .PBHeroLineUpInfo heroList = 5;</code>
     *
     * <pre>
     *武将列表
     * </pre>
     */
    public java.util.List<? extends JiaochangPb.PBHeroLineUpInfoOrBuilder> 
        getHeroListOrBuilderList() {
      return heroList_;
    }
    /**
     * <code>repeated .PBHeroLineUpInfo heroList = 5;</code>
     *
     * <pre>
     *武将列表
     * </pre>
     */
    public int getHeroListCount() {
      return heroList_.size();
    }
    /**
     * <code>repeated .PBHeroLineUpInfo heroList = 5;</code>
     *
     * <pre>
     *武将列表
     * </pre>
     */
    public JiaochangPb.PBHeroLineUpInfo getHeroList(int index) {
      return heroList_.get(index);
    }
    /**
     * <code>repeated .PBHeroLineUpInfo heroList = 5;</code>
     *
     * <pre>
     *武将列表
     * </pre>
     */
    public JiaochangPb.PBHeroLineUpInfoOrBuilder getHeroListOrBuilder(
        int index) {
      return heroList_.get(index);
    }

    private void initFields() {
      guid_ = 0L;
      playerName_ = "";
      wulueSetIndex_ = 0;
      talentSetIndex_ = 0;
      heroList_ = java.util.Collections.emptyList();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasGuid()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasWulueSetIndex()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasTalentSetIndex()) {
        memoizedIsInitialized = 0;
        return false;
      }
      for (int i = 0; i < getHeroListCount(); i++) {
        if (!getHeroList(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(1, guid_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getPlayerNameBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt32(3, wulueSetIndex_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeInt32(4, talentSetIndex_);
      }
      for (int i = 0; i < heroList_.size(); i++) {
        output.writeMessage(5, heroList_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, guid_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getPlayerNameBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, wulueSetIndex_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, talentSetIndex_);
      }
      for (int i = 0; i < heroList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, heroList_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static ArenaPb.PBArenaChallengePlayerDetailInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ArenaPb.PBArenaChallengePlayerDetailInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ArenaPb.PBArenaChallengePlayerDetailInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ArenaPb.PBArenaChallengePlayerDetailInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ArenaPb.PBArenaChallengePlayerDetailInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static ArenaPb.PBArenaChallengePlayerDetailInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static ArenaPb.PBArenaChallengePlayerDetailInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static ArenaPb.PBArenaChallengePlayerDetailInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static ArenaPb.PBArenaChallengePlayerDetailInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static ArenaPb.PBArenaChallengePlayerDetailInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(ArenaPb.PBArenaChallengePlayerDetailInfo prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code PBArenaChallengePlayerDetailInfo}
     *
     * <pre>
     *竞技场玩家攻守阵容数据
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements ArenaPb.PBArenaChallengePlayerDetailInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ArenaPb.internal_static_PBArenaChallengePlayerDetailInfo_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ArenaPb.internal_static_PBArenaChallengePlayerDetailInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ArenaPb.PBArenaChallengePlayerDetailInfo.class, ArenaPb.PBArenaChallengePlayerDetailInfo.Builder.class);
      }

      // Construct using ArenaPb.PBArenaChallengePlayerDetailInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getHeroListFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        guid_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        playerName_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        wulueSetIndex_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        talentSetIndex_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        if (heroListBuilder_ == null) {
          heroList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000010);
        } else {
          heroListBuilder_.clear();
        }
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ArenaPb.internal_static_PBArenaChallengePlayerDetailInfo_descriptor;
      }

      public ArenaPb.PBArenaChallengePlayerDetailInfo getDefaultInstanceForType() {
        return ArenaPb.PBArenaChallengePlayerDetailInfo.getDefaultInstance();
      }

      public ArenaPb.PBArenaChallengePlayerDetailInfo build() {
        ArenaPb.PBArenaChallengePlayerDetailInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public ArenaPb.PBArenaChallengePlayerDetailInfo buildPartial() {
        ArenaPb.PBArenaChallengePlayerDetailInfo result = new ArenaPb.PBArenaChallengePlayerDetailInfo(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.guid_ = guid_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.playerName_ = playerName_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.wulueSetIndex_ = wulueSetIndex_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.talentSetIndex_ = talentSetIndex_;
        if (heroListBuilder_ == null) {
          if (((bitField0_ & 0x00000010) == 0x00000010)) {
            heroList_ = java.util.Collections.unmodifiableList(heroList_);
            bitField0_ = (bitField0_ & ~0x00000010);
          }
          result.heroList_ = heroList_;
        } else {
          result.heroList_ = heroListBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof ArenaPb.PBArenaChallengePlayerDetailInfo) {
          return mergeFrom((ArenaPb.PBArenaChallengePlayerDetailInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(ArenaPb.PBArenaChallengePlayerDetailInfo other) {
        if (other == ArenaPb.PBArenaChallengePlayerDetailInfo.getDefaultInstance()) return this;
        if (other.hasGuid()) {
          setGuid(other.getGuid());
        }
        if (other.hasPlayerName()) {
          bitField0_ |= 0x00000002;
          playerName_ = other.playerName_;
          onChanged();
        }
        if (other.hasWulueSetIndex()) {
          setWulueSetIndex(other.getWulueSetIndex());
        }
        if (other.hasTalentSetIndex()) {
          setTalentSetIndex(other.getTalentSetIndex());
        }
        if (heroListBuilder_ == null) {
          if (!other.heroList_.isEmpty()) {
            if (heroList_.isEmpty()) {
              heroList_ = other.heroList_;
              bitField0_ = (bitField0_ & ~0x00000010);
            } else {
              ensureHeroListIsMutable();
              heroList_.addAll(other.heroList_);
            }
            onChanged();
          }
        } else {
          if (!other.heroList_.isEmpty()) {
            if (heroListBuilder_.isEmpty()) {
              heroListBuilder_.dispose();
              heroListBuilder_ = null;
              heroList_ = other.heroList_;
              bitField0_ = (bitField0_ & ~0x00000010);
              heroListBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getHeroListFieldBuilder() : null;
            } else {
              heroListBuilder_.addAllMessages(other.heroList_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasGuid()) {
          
          return false;
        }
        if (!hasWulueSetIndex()) {
          
          return false;
        }
        if (!hasTalentSetIndex()) {
          
          return false;
        }
        for (int i = 0; i < getHeroListCount(); i++) {
          if (!getHeroList(i).isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        ArenaPb.PBArenaChallengePlayerDetailInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (ArenaPb.PBArenaChallengePlayerDetailInfo) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int64 guid = 1;
      private long guid_ ;
      /**
       * <code>required int64 guid = 1;</code>
       *
       * <pre>
       *玩家guid
       * </pre>
       */
      public boolean hasGuid() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int64 guid = 1;</code>
       *
       * <pre>
       *玩家guid
       * </pre>
       */
      public long getGuid() {
        return guid_;
      }
      /**
       * <code>required int64 guid = 1;</code>
       *
       * <pre>
       *玩家guid
       * </pre>
       */
      public Builder setGuid(long value) {
        bitField0_ |= 0x00000001;
        guid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int64 guid = 1;</code>
       *
       * <pre>
       *玩家guid
       * </pre>
       */
      public Builder clearGuid() {
        bitField0_ = (bitField0_ & ~0x00000001);
        guid_ = 0L;
        onChanged();
        return this;
      }

      // optional string playerName = 2;
      private java.lang.Object playerName_ = "";
      /**
       * <code>optional string playerName = 2;</code>
       *
       * <pre>
       *玩家名字
       * </pre>
       */
      public boolean hasPlayerName() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional string playerName = 2;</code>
       *
       * <pre>
       *玩家名字
       * </pre>
       */
      public java.lang.String getPlayerName() {
        java.lang.Object ref = playerName_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          playerName_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string playerName = 2;</code>
       *
       * <pre>
       *玩家名字
       * </pre>
       */
      public com.google.protobuf.ByteString
          getPlayerNameBytes() {
        java.lang.Object ref = playerName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          playerName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string playerName = 2;</code>
       *
       * <pre>
       *玩家名字
       * </pre>
       */
      public Builder setPlayerName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        playerName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string playerName = 2;</code>
       *
       * <pre>
       *玩家名字
       * </pre>
       */
      public Builder clearPlayerName() {
        bitField0_ = (bitField0_ & ~0x00000002);
        playerName_ = getDefaultInstance().getPlayerName();
        onChanged();
        return this;
      }
      /**
       * <code>optional string playerName = 2;</code>
       *
       * <pre>
       *玩家名字
       * </pre>
       */
      public Builder setPlayerNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        playerName_ = value;
        onChanged();
        return this;
      }

      // required int32 wulueSetIndex = 3;
      private int wulueSetIndex_ ;
      /**
       * <code>required int32 wulueSetIndex = 3;</code>
       *
       * <pre>
       *武略设置
       * </pre>
       */
      public boolean hasWulueSetIndex() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>required int32 wulueSetIndex = 3;</code>
       *
       * <pre>
       *武略设置
       * </pre>
       */
      public int getWulueSetIndex() {
        return wulueSetIndex_;
      }
      /**
       * <code>required int32 wulueSetIndex = 3;</code>
       *
       * <pre>
       *武略设置
       * </pre>
       */
      public Builder setWulueSetIndex(int value) {
        bitField0_ |= 0x00000004;
        wulueSetIndex_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 wulueSetIndex = 3;</code>
       *
       * <pre>
       *武略设置
       * </pre>
       */
      public Builder clearWulueSetIndex() {
        bitField0_ = (bitField0_ & ~0x00000004);
        wulueSetIndex_ = 0;
        onChanged();
        return this;
      }

      // required int32 talentSetIndex = 4;
      private int talentSetIndex_ ;
      /**
       * <code>required int32 talentSetIndex = 4;</code>
       *
       * <pre>
       *天赋设置
       * </pre>
       */
      public boolean hasTalentSetIndex() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>required int32 talentSetIndex = 4;</code>
       *
       * <pre>
       *天赋设置
       * </pre>
       */
      public int getTalentSetIndex() {
        return talentSetIndex_;
      }
      /**
       * <code>required int32 talentSetIndex = 4;</code>
       *
       * <pre>
       *天赋设置
       * </pre>
       */
      public Builder setTalentSetIndex(int value) {
        bitField0_ |= 0x00000008;
        talentSetIndex_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 talentSetIndex = 4;</code>
       *
       * <pre>
       *天赋设置
       * </pre>
       */
      public Builder clearTalentSetIndex() {
        bitField0_ = (bitField0_ & ~0x00000008);
        talentSetIndex_ = 0;
        onChanged();
        return this;
      }

      // repeated .PBHeroLineUpInfo heroList = 5;
      private java.util.List<JiaochangPb.PBHeroLineUpInfo> heroList_ =
        java.util.Collections.emptyList();
      private void ensureHeroListIsMutable() {
        if (!((bitField0_ & 0x00000010) == 0x00000010)) {
          heroList_ = new java.util.ArrayList<JiaochangPb.PBHeroLineUpInfo>(heroList_);
          bitField0_ |= 0x00000010;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          JiaochangPb.PBHeroLineUpInfo, JiaochangPb.PBHeroLineUpInfo.Builder, JiaochangPb.PBHeroLineUpInfoOrBuilder> heroListBuilder_;

      /**
       * <code>repeated .PBHeroLineUpInfo heroList = 5;</code>
       *
       * <pre>
       *武将列表
       * </pre>
       */
      public java.util.List<JiaochangPb.PBHeroLineUpInfo> getHeroListList() {
        if (heroListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(heroList_);
        } else {
          return heroListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .PBHeroLineUpInfo heroList = 5;</code>
       *
       * <pre>
       *武将列表
       * </pre>
       */
      public int getHeroListCount() {
        if (heroListBuilder_ == null) {
          return heroList_.size();
        } else {
          return heroListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .PBHeroLineUpInfo heroList = 5;</code>
       *
       * <pre>
       *武将列表
       * </pre>
       */
      public JiaochangPb.PBHeroLineUpInfo getHeroList(int index) {
        if (heroListBuilder_ == null) {
          return heroList_.get(index);
        } else {
          return heroListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .PBHeroLineUpInfo heroList = 5;</code>
       *
       * <pre>
       *武将列表
       * </pre>
       */
      public Builder setHeroList(
          int index, JiaochangPb.PBHeroLineUpInfo value) {
        if (heroListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureHeroListIsMutable();
          heroList_.set(index, value);
          onChanged();
        } else {
          heroListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .PBHeroLineUpInfo heroList = 5;</code>
       *
       * <pre>
       *武将列表
       * </pre>
       */
      public Builder setHeroList(
          int index, JiaochangPb.PBHeroLineUpInfo.Builder builderForValue) {
        if (heroListBuilder_ == null) {
          ensureHeroListIsMutable();
          heroList_.set(index, builderForValue.build());
          onChanged();
        } else {
          heroListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .PBHeroLineUpInfo heroList = 5;</code>
       *
       * <pre>
       *武将列表
       * </pre>
       */
      public Builder addHeroList(JiaochangPb.PBHeroLineUpInfo value) {
        if (heroListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureHeroListIsMutable();
          heroList_.add(value);
          onChanged();
        } else {
          heroListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .PBHeroLineUpInfo heroList = 5;</code>
       *
       * <pre>
       *武将列表
       * </pre>
       */
      public Builder addHeroList(
          int index, JiaochangPb.PBHeroLineUpInfo value) {
        if (heroListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureHeroListIsMutable();
          heroList_.add(index, value);
          onChanged();
        } else {
          heroListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .PBHeroLineUpInfo heroList = 5;</code>
       *
       * <pre>
       *武将列表
       * </pre>
       */
      public Builder addHeroList(
          JiaochangPb.PBHeroLineUpInfo.Builder builderForValue) {
        if (heroListBuilder_ == null) {
          ensureHeroListIsMutable();
          heroList_.add(builderForValue.build());
          onChanged();
        } else {
          heroListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .PBHeroLineUpInfo heroList = 5;</code>
       *
       * <pre>
       *武将列表
       * </pre>
       */
      public Builder addHeroList(
          int index, JiaochangPb.PBHeroLineUpInfo.Builder builderForValue) {
        if (heroListBuilder_ == null) {
          ensureHeroListIsMutable();
          heroList_.add(index, builderForValue.build());
          onChanged();
        } else {
          heroListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .PBHeroLineUpInfo heroList = 5;</code>
       *
       * <pre>
       *武将列表
       * </pre>
       */
      public Builder addAllHeroList(
          java.lang.Iterable<? extends JiaochangPb.PBHeroLineUpInfo> values) {
        if (heroListBuilder_ == null) {
          ensureHeroListIsMutable();
          super.addAll(values, heroList_);
          onChanged();
        } else {
          heroListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .PBHeroLineUpInfo heroList = 5;</code>
       *
       * <pre>
       *武将列表
       * </pre>
       */
      public Builder clearHeroList() {
        if (heroListBuilder_ == null) {
          heroList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000010);
          onChanged();
        } else {
          heroListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .PBHeroLineUpInfo heroList = 5;</code>
       *
       * <pre>
       *武将列表
       * </pre>
       */
      public Builder removeHeroList(int index) {
        if (heroListBuilder_ == null) {
          ensureHeroListIsMutable();
          heroList_.remove(index);
          onChanged();
        } else {
          heroListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .PBHeroLineUpInfo heroList = 5;</code>
       *
       * <pre>
       *武将列表
       * </pre>
       */
      public JiaochangPb.PBHeroLineUpInfo.Builder getHeroListBuilder(
          int index) {
        return getHeroListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .PBHeroLineUpInfo heroList = 5;</code>
       *
       * <pre>
       *武将列表
       * </pre>
       */
      public JiaochangPb.PBHeroLineUpInfoOrBuilder getHeroListOrBuilder(
          int index) {
        if (heroListBuilder_ == null) {
          return heroList_.get(index);  } else {
          return heroListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .PBHeroLineUpInfo heroList = 5;</code>
       *
       * <pre>
       *武将列表
       * </pre>
       */
      public java.util.List<? extends JiaochangPb.PBHeroLineUpInfoOrBuilder> 
           getHeroListOrBuilderList() {
        if (heroListBuilder_ != null) {
          return heroListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(heroList_);
        }
      }
      /**
       * <code>repeated .PBHeroLineUpInfo heroList = 5;</code>
       *
       * <pre>
       *武将列表
       * </pre>
       */
      public JiaochangPb.PBHeroLineUpInfo.Builder addHeroListBuilder() {
        return getHeroListFieldBuilder().addBuilder(
            JiaochangPb.PBHeroLineUpInfo.getDefaultInstance());
      }
      /**
       * <code>repeated .PBHeroLineUpInfo heroList = 5;</code>
       *
       * <pre>
       *武将列表
       * </pre>
       */
      public JiaochangPb.PBHeroLineUpInfo.Builder addHeroListBuilder(
          int index) {
        return getHeroListFieldBuilder().addBuilder(
            index, JiaochangPb.PBHeroLineUpInfo.getDefaultInstance());
      }
      /**
       * <code>repeated .PBHeroLineUpInfo heroList = 5;</code>
       *
       * <pre>
       *武将列表
       * </pre>
       */
      public java.util.List<JiaochangPb.PBHeroLineUpInfo.Builder> 
           getHeroListBuilderList() {
        return getHeroListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          JiaochangPb.PBHeroLineUpInfo, JiaochangPb.PBHeroLineUpInfo.Builder, JiaochangPb.PBHeroLineUpInfoOrBuilder> 
          getHeroListFieldBuilder() {
        if (heroListBuilder_ == null) {
          heroListBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              JiaochangPb.PBHeroLineUpInfo, JiaochangPb.PBHeroLineUpInfo.Builder, JiaochangPb.PBHeroLineUpInfoOrBuilder>(
                  heroList_,
                  ((bitField0_ & 0x00000010) == 0x00000010),
                  getParentForChildren(),
                  isClean());
          heroList_ = null;
        }
        return heroListBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:PBArenaChallengePlayerDetailInfo)
    }

    static {
      defaultInstance = new PBArenaChallengePlayerDetailInfo(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:PBArenaChallengePlayerDetailInfo)
  }

  public interface PBArenaChallengeStateInfoOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int64 guid = 1;
    /**
     * <code>required int64 guid = 1;</code>
     *
     * <pre>
     *玩家guid
     * </pre>
     */
    boolean hasGuid();
    /**
     * <code>required int64 guid = 1;</code>
     *
     * <pre>
     *玩家guid
     * </pre>
     */
    long getGuid();

    // optional int32 rank = 2;
    /**
     * <code>optional int32 rank = 2;</code>
     *
     * <pre>
     *玩家排名
     * </pre>
     */
    boolean hasRank();
    /**
     * <code>optional int32 rank = 2;</code>
     *
     * <pre>
     *玩家排名
     * </pre>
     */
    int getRank();

    // optional int64 fight = 3;
    /**
     * <code>optional int64 fight = 3;</code>
     *
     * <pre>
     *玩家战斗力
     * </pre>
     */
    boolean hasFight();
    /**
     * <code>optional int64 fight = 3;</code>
     *
     * <pre>
     *玩家战斗力
     * </pre>
     */
    long getFight();

    // optional int64 score = 4;
    /**
     * <code>optional int64 score = 4;</code>
     *
     * <pre>
     *玩家积分
     * </pre>
     */
    boolean hasScore();
    /**
     * <code>optional int64 score = 4;</code>
     *
     * <pre>
     *玩家积分
     * </pre>
     */
    long getScore();

    // optional int32 challengeRemainTimes = 5;
    /**
     * <code>optional int32 challengeRemainTimes = 5;</code>
     *
     * <pre>
     *今日剩余挑战次数
     * </pre>
     */
    boolean hasChallengeRemainTimes();
    /**
     * <code>optional int32 challengeRemainTimes = 5;</code>
     *
     * <pre>
     *今日剩余挑战次数
     * </pre>
     */
    int getChallengeRemainTimes();

    // optional int32 challengeCurBuyTimes = 6;
    /**
     * <code>optional int32 challengeCurBuyTimes = 6;</code>
     *
     * <pre>
     *今日挑战已购买的次数
     * </pre>
     */
    boolean hasChallengeCurBuyTimes();
    /**
     * <code>optional int32 challengeCurBuyTimes = 6;</code>
     *
     * <pre>
     *今日挑战已购买的次数
     * </pre>
     */
    int getChallengeCurBuyTimes();

    // optional int32 challengeMaxBuyTimes = 7;
    /**
     * <code>optional int32 challengeMaxBuyTimes = 7;</code>
     *
     * <pre>
     *今日挑战可购买的最大次数
     * </pre>
     */
    boolean hasChallengeMaxBuyTimes();
    /**
     * <code>optional int32 challengeMaxBuyTimes = 7;</code>
     *
     * <pre>
     *今日挑战可购买的最大次数
     * </pre>
     */
    int getChallengeMaxBuyTimes();

    // optional int32 lastRefreshTime = 8;
    /**
     * <code>optional int32 lastRefreshTime = 8;</code>
     *
     * <pre>
     *上次刷新时间
     * </pre>
     */
    boolean hasLastRefreshTime();
    /**
     * <code>optional int32 lastRefreshTime = 8;</code>
     *
     * <pre>
     *上次刷新时间
     * </pre>
     */
    int getLastRefreshTime();

    // optional .PBArenaChallengePlayerDetailInfo detailInfo = 9;
    /**
     * <code>optional .PBArenaChallengePlayerDetailInfo detailInfo = 9;</code>
     *
     * <pre>
     *玩家防守阵容
     * </pre>
     */
    boolean hasDetailInfo();
    /**
     * <code>optional .PBArenaChallengePlayerDetailInfo detailInfo = 9;</code>
     *
     * <pre>
     *玩家防守阵容
     * </pre>
     */
    ArenaPb.PBArenaChallengePlayerDetailInfo getDetailInfo();
    /**
     * <code>optional .PBArenaChallengePlayerDetailInfo detailInfo = 9;</code>
     *
     * <pre>
     *玩家防守阵容
     * </pre>
     */
    ArenaPb.PBArenaChallengePlayerDetailInfoOrBuilder getDetailInfoOrBuilder();
  }
  /**
   * Protobuf type {@code PBArenaChallengeStateInfo}
   *
   * <pre>
   *竞技场玩家自身挑战排名等状态数据
   * </pre>
   */
  public static final class PBArenaChallengeStateInfo extends
      com.google.protobuf.GeneratedMessage
      implements PBArenaChallengeStateInfoOrBuilder {
    // Use PBArenaChallengeStateInfo.newBuilder() to construct.
    private PBArenaChallengeStateInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private PBArenaChallengeStateInfo(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final PBArenaChallengeStateInfo defaultInstance;
    public static PBArenaChallengeStateInfo getDefaultInstance() {
      return defaultInstance;
    }

    public PBArenaChallengeStateInfo getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private PBArenaChallengeStateInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              guid_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              rank_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              fight_ = input.readInt64();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              score_ = input.readInt64();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              challengeRemainTimes_ = input.readInt32();
              break;
            }
            case 48: {
              bitField0_ |= 0x00000020;
              challengeCurBuyTimes_ = input.readInt32();
              break;
            }
            case 56: {
              bitField0_ |= 0x00000040;
              challengeMaxBuyTimes_ = input.readInt32();
              break;
            }
            case 64: {
              bitField0_ |= 0x00000080;
              lastRefreshTime_ = input.readInt32();
              break;
            }
            case 74: {
              ArenaPb.PBArenaChallengePlayerDetailInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000100) == 0x00000100)) {
                subBuilder = detailInfo_.toBuilder();
              }
              detailInfo_ = input.readMessage(ArenaPb.PBArenaChallengePlayerDetailInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(detailInfo_);
                detailInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000100;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ArenaPb.internal_static_PBArenaChallengeStateInfo_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ArenaPb.internal_static_PBArenaChallengeStateInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ArenaPb.PBArenaChallengeStateInfo.class, ArenaPb.PBArenaChallengeStateInfo.Builder.class);
    }

    public static com.google.protobuf.Parser<PBArenaChallengeStateInfo> PARSER =
        new com.google.protobuf.AbstractParser<PBArenaChallengeStateInfo>() {
      public PBArenaChallengeStateInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PBArenaChallengeStateInfo(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<PBArenaChallengeStateInfo> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int64 guid = 1;
    public static final int GUID_FIELD_NUMBER = 1;
    private long guid_;
    /**
     * <code>required int64 guid = 1;</code>
     *
     * <pre>
     *玩家guid
     * </pre>
     */
    public boolean hasGuid() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int64 guid = 1;</code>
     *
     * <pre>
     *玩家guid
     * </pre>
     */
    public long getGuid() {
      return guid_;
    }

    // optional int32 rank = 2;
    public static final int RANK_FIELD_NUMBER = 2;
    private int rank_;
    /**
     * <code>optional int32 rank = 2;</code>
     *
     * <pre>
     *玩家排名
     * </pre>
     */
    public boolean hasRank() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional int32 rank = 2;</code>
     *
     * <pre>
     *玩家排名
     * </pre>
     */
    public int getRank() {
      return rank_;
    }

    // optional int64 fight = 3;
    public static final int FIGHT_FIELD_NUMBER = 3;
    private long fight_;
    /**
     * <code>optional int64 fight = 3;</code>
     *
     * <pre>
     *玩家战斗力
     * </pre>
     */
    public boolean hasFight() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional int64 fight = 3;</code>
     *
     * <pre>
     *玩家战斗力
     * </pre>
     */
    public long getFight() {
      return fight_;
    }

    // optional int64 score = 4;
    public static final int SCORE_FIELD_NUMBER = 4;
    private long score_;
    /**
     * <code>optional int64 score = 4;</code>
     *
     * <pre>
     *玩家积分
     * </pre>
     */
    public boolean hasScore() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional int64 score = 4;</code>
     *
     * <pre>
     *玩家积分
     * </pre>
     */
    public long getScore() {
      return score_;
    }

    // optional int32 challengeRemainTimes = 5;
    public static final int CHALLENGEREMAINTIMES_FIELD_NUMBER = 5;
    private int challengeRemainTimes_;
    /**
     * <code>optional int32 challengeRemainTimes = 5;</code>
     *
     * <pre>
     *今日剩余挑战次数
     * </pre>
     */
    public boolean hasChallengeRemainTimes() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>optional int32 challengeRemainTimes = 5;</code>
     *
     * <pre>
     *今日剩余挑战次数
     * </pre>
     */
    public int getChallengeRemainTimes() {
      return challengeRemainTimes_;
    }

    // optional int32 challengeCurBuyTimes = 6;
    public static final int CHALLENGECURBUYTIMES_FIELD_NUMBER = 6;
    private int challengeCurBuyTimes_;
    /**
     * <code>optional int32 challengeCurBuyTimes = 6;</code>
     *
     * <pre>
     *今日挑战已购买的次数
     * </pre>
     */
    public boolean hasChallengeCurBuyTimes() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <code>optional int32 challengeCurBuyTimes = 6;</code>
     *
     * <pre>
     *今日挑战已购买的次数
     * </pre>
     */
    public int getChallengeCurBuyTimes() {
      return challengeCurBuyTimes_;
    }

    // optional int32 challengeMaxBuyTimes = 7;
    public static final int CHALLENGEMAXBUYTIMES_FIELD_NUMBER = 7;
    private int challengeMaxBuyTimes_;
    /**
     * <code>optional int32 challengeMaxBuyTimes = 7;</code>
     *
     * <pre>
     *今日挑战可购买的最大次数
     * </pre>
     */
    public boolean hasChallengeMaxBuyTimes() {
      return ((bitField0_ & 0x00000040) == 0x00000040);
    }
    /**
     * <code>optional int32 challengeMaxBuyTimes = 7;</code>
     *
     * <pre>
     *今日挑战可购买的最大次数
     * </pre>
     */
    public int getChallengeMaxBuyTimes() {
      return challengeMaxBuyTimes_;
    }

    // optional int32 lastRefreshTime = 8;
    public static final int LASTREFRESHTIME_FIELD_NUMBER = 8;
    private int lastRefreshTime_;
    /**
     * <code>optional int32 lastRefreshTime = 8;</code>
     *
     * <pre>
     *上次刷新时间
     * </pre>
     */
    public boolean hasLastRefreshTime() {
      return ((bitField0_ & 0x00000080) == 0x00000080);
    }
    /**
     * <code>optional int32 lastRefreshTime = 8;</code>
     *
     * <pre>
     *上次刷新时间
     * </pre>
     */
    public int getLastRefreshTime() {
      return lastRefreshTime_;
    }

    // optional .PBArenaChallengePlayerDetailInfo detailInfo = 9;
    public static final int DETAILINFO_FIELD_NUMBER = 9;
    private ArenaPb.PBArenaChallengePlayerDetailInfo detailInfo_;
    /**
     * <code>optional .PBArenaChallengePlayerDetailInfo detailInfo = 9;</code>
     *
     * <pre>
     *玩家防守阵容
     * </pre>
     */
    public boolean hasDetailInfo() {
      return ((bitField0_ & 0x00000100) == 0x00000100);
    }
    /**
     * <code>optional .PBArenaChallengePlayerDetailInfo detailInfo = 9;</code>
     *
     * <pre>
     *玩家防守阵容
     * </pre>
     */
    public ArenaPb.PBArenaChallengePlayerDetailInfo getDetailInfo() {
      return detailInfo_;
    }
    /**
     * <code>optional .PBArenaChallengePlayerDetailInfo detailInfo = 9;</code>
     *
     * <pre>
     *玩家防守阵容
     * </pre>
     */
    public ArenaPb.PBArenaChallengePlayerDetailInfoOrBuilder getDetailInfoOrBuilder() {
      return detailInfo_;
    }

    private void initFields() {
      guid_ = 0L;
      rank_ = 0;
      fight_ = 0L;
      score_ = 0L;
      challengeRemainTimes_ = 0;
      challengeCurBuyTimes_ = 0;
      challengeMaxBuyTimes_ = 0;
      lastRefreshTime_ = 0;
      detailInfo_ = ArenaPb.PBArenaChallengePlayerDetailInfo.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasGuid()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (hasDetailInfo()) {
        if (!getDetailInfo().isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(1, guid_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, rank_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt64(3, fight_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeInt64(4, score_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeInt32(5, challengeRemainTimes_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        output.writeInt32(6, challengeCurBuyTimes_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        output.writeInt32(7, challengeMaxBuyTimes_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        output.writeInt32(8, lastRefreshTime_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        output.writeMessage(9, detailInfo_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, guid_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, rank_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, fight_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(4, score_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, challengeRemainTimes_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, challengeCurBuyTimes_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(7, challengeMaxBuyTimes_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(8, lastRefreshTime_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(9, detailInfo_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static ArenaPb.PBArenaChallengeStateInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ArenaPb.PBArenaChallengeStateInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ArenaPb.PBArenaChallengeStateInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ArenaPb.PBArenaChallengeStateInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ArenaPb.PBArenaChallengeStateInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static ArenaPb.PBArenaChallengeStateInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static ArenaPb.PBArenaChallengeStateInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static ArenaPb.PBArenaChallengeStateInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static ArenaPb.PBArenaChallengeStateInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static ArenaPb.PBArenaChallengeStateInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(ArenaPb.PBArenaChallengeStateInfo prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code PBArenaChallengeStateInfo}
     *
     * <pre>
     *竞技场玩家自身挑战排名等状态数据
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements ArenaPb.PBArenaChallengeStateInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ArenaPb.internal_static_PBArenaChallengeStateInfo_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ArenaPb.internal_static_PBArenaChallengeStateInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ArenaPb.PBArenaChallengeStateInfo.class, ArenaPb.PBArenaChallengeStateInfo.Builder.class);
      }

      // Construct using ArenaPb.PBArenaChallengeStateInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getDetailInfoFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        guid_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        rank_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        fight_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        score_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000008);
        challengeRemainTimes_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        challengeCurBuyTimes_ = 0;
        bitField0_ = (bitField0_ & ~0x00000020);
        challengeMaxBuyTimes_ = 0;
        bitField0_ = (bitField0_ & ~0x00000040);
        lastRefreshTime_ = 0;
        bitField0_ = (bitField0_ & ~0x00000080);
        if (detailInfoBuilder_ == null) {
          detailInfo_ = ArenaPb.PBArenaChallengePlayerDetailInfo.getDefaultInstance();
        } else {
          detailInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000100);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ArenaPb.internal_static_PBArenaChallengeStateInfo_descriptor;
      }

      public ArenaPb.PBArenaChallengeStateInfo getDefaultInstanceForType() {
        return ArenaPb.PBArenaChallengeStateInfo.getDefaultInstance();
      }

      public ArenaPb.PBArenaChallengeStateInfo build() {
        ArenaPb.PBArenaChallengeStateInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public ArenaPb.PBArenaChallengeStateInfo buildPartial() {
        ArenaPb.PBArenaChallengeStateInfo result = new ArenaPb.PBArenaChallengeStateInfo(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.guid_ = guid_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.rank_ = rank_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.fight_ = fight_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.score_ = score_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.challengeRemainTimes_ = challengeRemainTimes_;
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000020;
        }
        result.challengeCurBuyTimes_ = challengeCurBuyTimes_;
        if (((from_bitField0_ & 0x00000040) == 0x00000040)) {
          to_bitField0_ |= 0x00000040;
        }
        result.challengeMaxBuyTimes_ = challengeMaxBuyTimes_;
        if (((from_bitField0_ & 0x00000080) == 0x00000080)) {
          to_bitField0_ |= 0x00000080;
        }
        result.lastRefreshTime_ = lastRefreshTime_;
        if (((from_bitField0_ & 0x00000100) == 0x00000100)) {
          to_bitField0_ |= 0x00000100;
        }
        if (detailInfoBuilder_ == null) {
          result.detailInfo_ = detailInfo_;
        } else {
          result.detailInfo_ = detailInfoBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof ArenaPb.PBArenaChallengeStateInfo) {
          return mergeFrom((ArenaPb.PBArenaChallengeStateInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(ArenaPb.PBArenaChallengeStateInfo other) {
        if (other == ArenaPb.PBArenaChallengeStateInfo.getDefaultInstance()) return this;
        if (other.hasGuid()) {
          setGuid(other.getGuid());
        }
        if (other.hasRank()) {
          setRank(other.getRank());
        }
        if (other.hasFight()) {
          setFight(other.getFight());
        }
        if (other.hasScore()) {
          setScore(other.getScore());
        }
        if (other.hasChallengeRemainTimes()) {
          setChallengeRemainTimes(other.getChallengeRemainTimes());
        }
        if (other.hasChallengeCurBuyTimes()) {
          setChallengeCurBuyTimes(other.getChallengeCurBuyTimes());
        }
        if (other.hasChallengeMaxBuyTimes()) {
          setChallengeMaxBuyTimes(other.getChallengeMaxBuyTimes());
        }
        if (other.hasLastRefreshTime()) {
          setLastRefreshTime(other.getLastRefreshTime());
        }
        if (other.hasDetailInfo()) {
          mergeDetailInfo(other.getDetailInfo());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasGuid()) {
          
          return false;
        }
        if (hasDetailInfo()) {
          if (!getDetailInfo().isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        ArenaPb.PBArenaChallengeStateInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (ArenaPb.PBArenaChallengeStateInfo) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int64 guid = 1;
      private long guid_ ;
      /**
       * <code>required int64 guid = 1;</code>
       *
       * <pre>
       *玩家guid
       * </pre>
       */
      public boolean hasGuid() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int64 guid = 1;</code>
       *
       * <pre>
       *玩家guid
       * </pre>
       */
      public long getGuid() {
        return guid_;
      }
      /**
       * <code>required int64 guid = 1;</code>
       *
       * <pre>
       *玩家guid
       * </pre>
       */
      public Builder setGuid(long value) {
        bitField0_ |= 0x00000001;
        guid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int64 guid = 1;</code>
       *
       * <pre>
       *玩家guid
       * </pre>
       */
      public Builder clearGuid() {
        bitField0_ = (bitField0_ & ~0x00000001);
        guid_ = 0L;
        onChanged();
        return this;
      }

      // optional int32 rank = 2;
      private int rank_ ;
      /**
       * <code>optional int32 rank = 2;</code>
       *
       * <pre>
       *玩家排名
       * </pre>
       */
      public boolean hasRank() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional int32 rank = 2;</code>
       *
       * <pre>
       *玩家排名
       * </pre>
       */
      public int getRank() {
        return rank_;
      }
      /**
       * <code>optional int32 rank = 2;</code>
       *
       * <pre>
       *玩家排名
       * </pre>
       */
      public Builder setRank(int value) {
        bitField0_ |= 0x00000002;
        rank_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 rank = 2;</code>
       *
       * <pre>
       *玩家排名
       * </pre>
       */
      public Builder clearRank() {
        bitField0_ = (bitField0_ & ~0x00000002);
        rank_ = 0;
        onChanged();
        return this;
      }

      // optional int64 fight = 3;
      private long fight_ ;
      /**
       * <code>optional int64 fight = 3;</code>
       *
       * <pre>
       *玩家战斗力
       * </pre>
       */
      public boolean hasFight() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional int64 fight = 3;</code>
       *
       * <pre>
       *玩家战斗力
       * </pre>
       */
      public long getFight() {
        return fight_;
      }
      /**
       * <code>optional int64 fight = 3;</code>
       *
       * <pre>
       *玩家战斗力
       * </pre>
       */
      public Builder setFight(long value) {
        bitField0_ |= 0x00000004;
        fight_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 fight = 3;</code>
       *
       * <pre>
       *玩家战斗力
       * </pre>
       */
      public Builder clearFight() {
        bitField0_ = (bitField0_ & ~0x00000004);
        fight_ = 0L;
        onChanged();
        return this;
      }

      // optional int64 score = 4;
      private long score_ ;
      /**
       * <code>optional int64 score = 4;</code>
       *
       * <pre>
       *玩家积分
       * </pre>
       */
      public boolean hasScore() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional int64 score = 4;</code>
       *
       * <pre>
       *玩家积分
       * </pre>
       */
      public long getScore() {
        return score_;
      }
      /**
       * <code>optional int64 score = 4;</code>
       *
       * <pre>
       *玩家积分
       * </pre>
       */
      public Builder setScore(long value) {
        bitField0_ |= 0x00000008;
        score_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 score = 4;</code>
       *
       * <pre>
       *玩家积分
       * </pre>
       */
      public Builder clearScore() {
        bitField0_ = (bitField0_ & ~0x00000008);
        score_ = 0L;
        onChanged();
        return this;
      }

      // optional int32 challengeRemainTimes = 5;
      private int challengeRemainTimes_ ;
      /**
       * <code>optional int32 challengeRemainTimes = 5;</code>
       *
       * <pre>
       *今日剩余挑战次数
       * </pre>
       */
      public boolean hasChallengeRemainTimes() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional int32 challengeRemainTimes = 5;</code>
       *
       * <pre>
       *今日剩余挑战次数
       * </pre>
       */
      public int getChallengeRemainTimes() {
        return challengeRemainTimes_;
      }
      /**
       * <code>optional int32 challengeRemainTimes = 5;</code>
       *
       * <pre>
       *今日剩余挑战次数
       * </pre>
       */
      public Builder setChallengeRemainTimes(int value) {
        bitField0_ |= 0x00000010;
        challengeRemainTimes_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 challengeRemainTimes = 5;</code>
       *
       * <pre>
       *今日剩余挑战次数
       * </pre>
       */
      public Builder clearChallengeRemainTimes() {
        bitField0_ = (bitField0_ & ~0x00000010);
        challengeRemainTimes_ = 0;
        onChanged();
        return this;
      }

      // optional int32 challengeCurBuyTimes = 6;
      private int challengeCurBuyTimes_ ;
      /**
       * <code>optional int32 challengeCurBuyTimes = 6;</code>
       *
       * <pre>
       *今日挑战已购买的次数
       * </pre>
       */
      public boolean hasChallengeCurBuyTimes() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <code>optional int32 challengeCurBuyTimes = 6;</code>
       *
       * <pre>
       *今日挑战已购买的次数
       * </pre>
       */
      public int getChallengeCurBuyTimes() {
        return challengeCurBuyTimes_;
      }
      /**
       * <code>optional int32 challengeCurBuyTimes = 6;</code>
       *
       * <pre>
       *今日挑战已购买的次数
       * </pre>
       */
      public Builder setChallengeCurBuyTimes(int value) {
        bitField0_ |= 0x00000020;
        challengeCurBuyTimes_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 challengeCurBuyTimes = 6;</code>
       *
       * <pre>
       *今日挑战已购买的次数
       * </pre>
       */
      public Builder clearChallengeCurBuyTimes() {
        bitField0_ = (bitField0_ & ~0x00000020);
        challengeCurBuyTimes_ = 0;
        onChanged();
        return this;
      }

      // optional int32 challengeMaxBuyTimes = 7;
      private int challengeMaxBuyTimes_ ;
      /**
       * <code>optional int32 challengeMaxBuyTimes = 7;</code>
       *
       * <pre>
       *今日挑战可购买的最大次数
       * </pre>
       */
      public boolean hasChallengeMaxBuyTimes() {
        return ((bitField0_ & 0x00000040) == 0x00000040);
      }
      /**
       * <code>optional int32 challengeMaxBuyTimes = 7;</code>
       *
       * <pre>
       *今日挑战可购买的最大次数
       * </pre>
       */
      public int getChallengeMaxBuyTimes() {
        return challengeMaxBuyTimes_;
      }
      /**
       * <code>optional int32 challengeMaxBuyTimes = 7;</code>
       *
       * <pre>
       *今日挑战可购买的最大次数
       * </pre>
       */
      public Builder setChallengeMaxBuyTimes(int value) {
        bitField0_ |= 0x00000040;
        challengeMaxBuyTimes_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 challengeMaxBuyTimes = 7;</code>
       *
       * <pre>
       *今日挑战可购买的最大次数
       * </pre>
       */
      public Builder clearChallengeMaxBuyTimes() {
        bitField0_ = (bitField0_ & ~0x00000040);
        challengeMaxBuyTimes_ = 0;
        onChanged();
        return this;
      }

      // optional int32 lastRefreshTime = 8;
      private int lastRefreshTime_ ;
      /**
       * <code>optional int32 lastRefreshTime = 8;</code>
       *
       * <pre>
       *上次刷新时间
       * </pre>
       */
      public boolean hasLastRefreshTime() {
        return ((bitField0_ & 0x00000080) == 0x00000080);
      }
      /**
       * <code>optional int32 lastRefreshTime = 8;</code>
       *
       * <pre>
       *上次刷新时间
       * </pre>
       */
      public int getLastRefreshTime() {
        return lastRefreshTime_;
      }
      /**
       * <code>optional int32 lastRefreshTime = 8;</code>
       *
       * <pre>
       *上次刷新时间
       * </pre>
       */
      public Builder setLastRefreshTime(int value) {
        bitField0_ |= 0x00000080;
        lastRefreshTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 lastRefreshTime = 8;</code>
       *
       * <pre>
       *上次刷新时间
       * </pre>
       */
      public Builder clearLastRefreshTime() {
        bitField0_ = (bitField0_ & ~0x00000080);
        lastRefreshTime_ = 0;
        onChanged();
        return this;
      }

      // optional .PBArenaChallengePlayerDetailInfo detailInfo = 9;
      private ArenaPb.PBArenaChallengePlayerDetailInfo detailInfo_ = ArenaPb.PBArenaChallengePlayerDetailInfo.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          ArenaPb.PBArenaChallengePlayerDetailInfo, ArenaPb.PBArenaChallengePlayerDetailInfo.Builder, ArenaPb.PBArenaChallengePlayerDetailInfoOrBuilder> detailInfoBuilder_;
      /**
       * <code>optional .PBArenaChallengePlayerDetailInfo detailInfo = 9;</code>
       *
       * <pre>
       *玩家防守阵容
       * </pre>
       */
      public boolean hasDetailInfo() {
        return ((bitField0_ & 0x00000100) == 0x00000100);
      }
      /**
       * <code>optional .PBArenaChallengePlayerDetailInfo detailInfo = 9;</code>
       *
       * <pre>
       *玩家防守阵容
       * </pre>
       */
      public ArenaPb.PBArenaChallengePlayerDetailInfo getDetailInfo() {
        if (detailInfoBuilder_ == null) {
          return detailInfo_;
        } else {
          return detailInfoBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .PBArenaChallengePlayerDetailInfo detailInfo = 9;</code>
       *
       * <pre>
       *玩家防守阵容
       * </pre>
       */
      public Builder setDetailInfo(ArenaPb.PBArenaChallengePlayerDetailInfo value) {
        if (detailInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          detailInfo_ = value;
          onChanged();
        } else {
          detailInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000100;
        return this;
      }
      /**
       * <code>optional .PBArenaChallengePlayerDetailInfo detailInfo = 9;</code>
       *
       * <pre>
       *玩家防守阵容
       * </pre>
       */
      public Builder setDetailInfo(
          ArenaPb.PBArenaChallengePlayerDetailInfo.Builder builderForValue) {
        if (detailInfoBuilder_ == null) {
          detailInfo_ = builderForValue.build();
          onChanged();
        } else {
          detailInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000100;
        return this;
      }
      /**
       * <code>optional .PBArenaChallengePlayerDetailInfo detailInfo = 9;</code>
       *
       * <pre>
       *玩家防守阵容
       * </pre>
       */
      public Builder mergeDetailInfo(ArenaPb.PBArenaChallengePlayerDetailInfo value) {
        if (detailInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000100) == 0x00000100) &&
              detailInfo_ != ArenaPb.PBArenaChallengePlayerDetailInfo.getDefaultInstance()) {
            detailInfo_ =
              ArenaPb.PBArenaChallengePlayerDetailInfo.newBuilder(detailInfo_).mergeFrom(value).buildPartial();
          } else {
            detailInfo_ = value;
          }
          onChanged();
        } else {
          detailInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000100;
        return this;
      }
      /**
       * <code>optional .PBArenaChallengePlayerDetailInfo detailInfo = 9;</code>
       *
       * <pre>
       *玩家防守阵容
       * </pre>
       */
      public Builder clearDetailInfo() {
        if (detailInfoBuilder_ == null) {
          detailInfo_ = ArenaPb.PBArenaChallengePlayerDetailInfo.getDefaultInstance();
          onChanged();
        } else {
          detailInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000100);
        return this;
      }
      /**
       * <code>optional .PBArenaChallengePlayerDetailInfo detailInfo = 9;</code>
       *
       * <pre>
       *玩家防守阵容
       * </pre>
       */
      public ArenaPb.PBArenaChallengePlayerDetailInfo.Builder getDetailInfoBuilder() {
        bitField0_ |= 0x00000100;
        onChanged();
        return getDetailInfoFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .PBArenaChallengePlayerDetailInfo detailInfo = 9;</code>
       *
       * <pre>
       *玩家防守阵容
       * </pre>
       */
      public ArenaPb.PBArenaChallengePlayerDetailInfoOrBuilder getDetailInfoOrBuilder() {
        if (detailInfoBuilder_ != null) {
          return detailInfoBuilder_.getMessageOrBuilder();
        } else {
          return detailInfo_;
        }
      }
      /**
       * <code>optional .PBArenaChallengePlayerDetailInfo detailInfo = 9;</code>
       *
       * <pre>
       *玩家防守阵容
       * </pre>
       */
      private com.google.protobuf.SingleFieldBuilder<
          ArenaPb.PBArenaChallengePlayerDetailInfo, ArenaPb.PBArenaChallengePlayerDetailInfo.Builder, ArenaPb.PBArenaChallengePlayerDetailInfoOrBuilder> 
          getDetailInfoFieldBuilder() {
        if (detailInfoBuilder_ == null) {
          detailInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              ArenaPb.PBArenaChallengePlayerDetailInfo, ArenaPb.PBArenaChallengePlayerDetailInfo.Builder, ArenaPb.PBArenaChallengePlayerDetailInfoOrBuilder>(
                  detailInfo_,
                  getParentForChildren(),
                  isClean());
          detailInfo_ = null;
        }
        return detailInfoBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:PBArenaChallengeStateInfo)
    }

    static {
      defaultInstance = new PBArenaChallengeStateInfo(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:PBArenaChallengeStateInfo)
  }

  public interface PBArenaChallengeRecordInfoListOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // repeated int32 type = 1;
    /**
     * <code>repeated int32 type = 1;</code>
     *
     * <pre>
     *1我的记录 2巅峰之战
     * </pre>
     */
    java.util.List<java.lang.Integer> getTypeList();
    /**
     * <code>repeated int32 type = 1;</code>
     *
     * <pre>
     *1我的记录 2巅峰之战
     * </pre>
     */
    int getTypeCount();
    /**
     * <code>repeated int32 type = 1;</code>
     *
     * <pre>
     *1我的记录 2巅峰之战
     * </pre>
     */
    int getType(int index);

    // repeated .PBArenaChallengeRecordInfo recordInfo = 2;
    /**
     * <code>repeated .PBArenaChallengeRecordInfo recordInfo = 2;</code>
     *
     * <pre>
     *玩家挑战结果列表数据
     * </pre>
     */
    java.util.List<ArenaPb.PBArenaChallengeRecordInfo> 
        getRecordInfoList();
    /**
     * <code>repeated .PBArenaChallengeRecordInfo recordInfo = 2;</code>
     *
     * <pre>
     *玩家挑战结果列表数据
     * </pre>
     */
    ArenaPb.PBArenaChallengeRecordInfo getRecordInfo(int index);
    /**
     * <code>repeated .PBArenaChallengeRecordInfo recordInfo = 2;</code>
     *
     * <pre>
     *玩家挑战结果列表数据
     * </pre>
     */
    int getRecordInfoCount();
    /**
     * <code>repeated .PBArenaChallengeRecordInfo recordInfo = 2;</code>
     *
     * <pre>
     *玩家挑战结果列表数据
     * </pre>
     */
    java.util.List<? extends ArenaPb.PBArenaChallengeRecordInfoOrBuilder> 
        getRecordInfoOrBuilderList();
    /**
     * <code>repeated .PBArenaChallengeRecordInfo recordInfo = 2;</code>
     *
     * <pre>
     *玩家挑战结果列表数据
     * </pre>
     */
    ArenaPb.PBArenaChallengeRecordInfoOrBuilder getRecordInfoOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code PBArenaChallengeRecordInfoList}
   *
   * <pre>
   *竞技场玩家获取我的挑战或者巅峰对决等战报返回列表
   * </pre>
   */
  public static final class PBArenaChallengeRecordInfoList extends
      com.google.protobuf.GeneratedMessage
      implements PBArenaChallengeRecordInfoListOrBuilder {
    // Use PBArenaChallengeRecordInfoList.newBuilder() to construct.
    private PBArenaChallengeRecordInfoList(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private PBArenaChallengeRecordInfoList(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final PBArenaChallengeRecordInfoList defaultInstance;
    public static PBArenaChallengeRecordInfoList getDefaultInstance() {
      return defaultInstance;
    }

    public PBArenaChallengeRecordInfoList getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private PBArenaChallengeRecordInfoList(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                type_ = new java.util.ArrayList<java.lang.Integer>();
                mutable_bitField0_ |= 0x00000001;
              }
              type_.add(input.readInt32());
              break;
            }
            case 10: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001) && input.getBytesUntilLimit() > 0) {
                type_ = new java.util.ArrayList<java.lang.Integer>();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                type_.add(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                recordInfo_ = new java.util.ArrayList<ArenaPb.PBArenaChallengeRecordInfo>();
                mutable_bitField0_ |= 0x00000002;
              }
              recordInfo_.add(input.readMessage(ArenaPb.PBArenaChallengeRecordInfo.PARSER, extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          type_ = java.util.Collections.unmodifiableList(type_);
        }
        if (((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
          recordInfo_ = java.util.Collections.unmodifiableList(recordInfo_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ArenaPb.internal_static_PBArenaChallengeRecordInfoList_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ArenaPb.internal_static_PBArenaChallengeRecordInfoList_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ArenaPb.PBArenaChallengeRecordInfoList.class, ArenaPb.PBArenaChallengeRecordInfoList.Builder.class);
    }

    public static com.google.protobuf.Parser<PBArenaChallengeRecordInfoList> PARSER =
        new com.google.protobuf.AbstractParser<PBArenaChallengeRecordInfoList>() {
      public PBArenaChallengeRecordInfoList parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PBArenaChallengeRecordInfoList(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<PBArenaChallengeRecordInfoList> getParserForType() {
      return PARSER;
    }

    // repeated int32 type = 1;
    public static final int TYPE_FIELD_NUMBER = 1;
    private java.util.List<java.lang.Integer> type_;
    /**
     * <code>repeated int32 type = 1;</code>
     *
     * <pre>
     *1我的记录 2巅峰之战
     * </pre>
     */
    public java.util.List<java.lang.Integer>
        getTypeList() {
      return type_;
    }
    /**
     * <code>repeated int32 type = 1;</code>
     *
     * <pre>
     *1我的记录 2巅峰之战
     * </pre>
     */
    public int getTypeCount() {
      return type_.size();
    }
    /**
     * <code>repeated int32 type = 1;</code>
     *
     * <pre>
     *1我的记录 2巅峰之战
     * </pre>
     */
    public int getType(int index) {
      return type_.get(index);
    }

    // repeated .PBArenaChallengeRecordInfo recordInfo = 2;
    public static final int RECORDINFO_FIELD_NUMBER = 2;
    private java.util.List<ArenaPb.PBArenaChallengeRecordInfo> recordInfo_;
    /**
     * <code>repeated .PBArenaChallengeRecordInfo recordInfo = 2;</code>
     *
     * <pre>
     *玩家挑战结果列表数据
     * </pre>
     */
    public java.util.List<ArenaPb.PBArenaChallengeRecordInfo> getRecordInfoList() {
      return recordInfo_;
    }
    /**
     * <code>repeated .PBArenaChallengeRecordInfo recordInfo = 2;</code>
     *
     * <pre>
     *玩家挑战结果列表数据
     * </pre>
     */
    public java.util.List<? extends ArenaPb.PBArenaChallengeRecordInfoOrBuilder> 
        getRecordInfoOrBuilderList() {
      return recordInfo_;
    }
    /**
     * <code>repeated .PBArenaChallengeRecordInfo recordInfo = 2;</code>
     *
     * <pre>
     *玩家挑战结果列表数据
     * </pre>
     */
    public int getRecordInfoCount() {
      return recordInfo_.size();
    }
    /**
     * <code>repeated .PBArenaChallengeRecordInfo recordInfo = 2;</code>
     *
     * <pre>
     *玩家挑战结果列表数据
     * </pre>
     */
    public ArenaPb.PBArenaChallengeRecordInfo getRecordInfo(int index) {
      return recordInfo_.get(index);
    }
    /**
     * <code>repeated .PBArenaChallengeRecordInfo recordInfo = 2;</code>
     *
     * <pre>
     *玩家挑战结果列表数据
     * </pre>
     */
    public ArenaPb.PBArenaChallengeRecordInfoOrBuilder getRecordInfoOrBuilder(
        int index) {
      return recordInfo_.get(index);
    }

    private void initFields() {
      type_ = java.util.Collections.emptyList();
      recordInfo_ = java.util.Collections.emptyList();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      for (int i = 0; i < getRecordInfoCount(); i++) {
        if (!getRecordInfo(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      for (int i = 0; i < type_.size(); i++) {
        output.writeInt32(1, type_.get(i));
      }
      for (int i = 0; i < recordInfo_.size(); i++) {
        output.writeMessage(2, recordInfo_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < type_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(type_.get(i));
        }
        size += dataSize;
        size += 1 * getTypeList().size();
      }
      for (int i = 0; i < recordInfo_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, recordInfo_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static ArenaPb.PBArenaChallengeRecordInfoList parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ArenaPb.PBArenaChallengeRecordInfoList parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ArenaPb.PBArenaChallengeRecordInfoList parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ArenaPb.PBArenaChallengeRecordInfoList parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ArenaPb.PBArenaChallengeRecordInfoList parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static ArenaPb.PBArenaChallengeRecordInfoList parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static ArenaPb.PBArenaChallengeRecordInfoList parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static ArenaPb.PBArenaChallengeRecordInfoList parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static ArenaPb.PBArenaChallengeRecordInfoList parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static ArenaPb.PBArenaChallengeRecordInfoList parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(ArenaPb.PBArenaChallengeRecordInfoList prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code PBArenaChallengeRecordInfoList}
     *
     * <pre>
     *竞技场玩家获取我的挑战或者巅峰对决等战报返回列表
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements ArenaPb.PBArenaChallengeRecordInfoListOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ArenaPb.internal_static_PBArenaChallengeRecordInfoList_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ArenaPb.internal_static_PBArenaChallengeRecordInfoList_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ArenaPb.PBArenaChallengeRecordInfoList.class, ArenaPb.PBArenaChallengeRecordInfoList.Builder.class);
      }

      // Construct using ArenaPb.PBArenaChallengeRecordInfoList.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getRecordInfoFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        type_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        if (recordInfoBuilder_ == null) {
          recordInfo_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          recordInfoBuilder_.clear();
        }
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ArenaPb.internal_static_PBArenaChallengeRecordInfoList_descriptor;
      }

      public ArenaPb.PBArenaChallengeRecordInfoList getDefaultInstanceForType() {
        return ArenaPb.PBArenaChallengeRecordInfoList.getDefaultInstance();
      }

      public ArenaPb.PBArenaChallengeRecordInfoList build() {
        ArenaPb.PBArenaChallengeRecordInfoList result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public ArenaPb.PBArenaChallengeRecordInfoList buildPartial() {
        ArenaPb.PBArenaChallengeRecordInfoList result = new ArenaPb.PBArenaChallengeRecordInfoList(this);
        int from_bitField0_ = bitField0_;
        if (((bitField0_ & 0x00000001) == 0x00000001)) {
          type_ = java.util.Collections.unmodifiableList(type_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.type_ = type_;
        if (recordInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000002) == 0x00000002)) {
            recordInfo_ = java.util.Collections.unmodifiableList(recordInfo_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.recordInfo_ = recordInfo_;
        } else {
          result.recordInfo_ = recordInfoBuilder_.build();
        }
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof ArenaPb.PBArenaChallengeRecordInfoList) {
          return mergeFrom((ArenaPb.PBArenaChallengeRecordInfoList)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(ArenaPb.PBArenaChallengeRecordInfoList other) {
        if (other == ArenaPb.PBArenaChallengeRecordInfoList.getDefaultInstance()) return this;
        if (!other.type_.isEmpty()) {
          if (type_.isEmpty()) {
            type_ = other.type_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureTypeIsMutable();
            type_.addAll(other.type_);
          }
          onChanged();
        }
        if (recordInfoBuilder_ == null) {
          if (!other.recordInfo_.isEmpty()) {
            if (recordInfo_.isEmpty()) {
              recordInfo_ = other.recordInfo_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureRecordInfoIsMutable();
              recordInfo_.addAll(other.recordInfo_);
            }
            onChanged();
          }
        } else {
          if (!other.recordInfo_.isEmpty()) {
            if (recordInfoBuilder_.isEmpty()) {
              recordInfoBuilder_.dispose();
              recordInfoBuilder_ = null;
              recordInfo_ = other.recordInfo_;
              bitField0_ = (bitField0_ & ~0x00000002);
              recordInfoBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getRecordInfoFieldBuilder() : null;
            } else {
              recordInfoBuilder_.addAllMessages(other.recordInfo_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        for (int i = 0; i < getRecordInfoCount(); i++) {
          if (!getRecordInfo(i).isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        ArenaPb.PBArenaChallengeRecordInfoList parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (ArenaPb.PBArenaChallengeRecordInfoList) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // repeated int32 type = 1;
      private java.util.List<java.lang.Integer> type_ = java.util.Collections.emptyList();
      private void ensureTypeIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          type_ = new java.util.ArrayList<java.lang.Integer>(type_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <code>repeated int32 type = 1;</code>
       *
       * <pre>
       *1我的记录 2巅峰之战
       * </pre>
       */
      public java.util.List<java.lang.Integer>
          getTypeList() {
        return java.util.Collections.unmodifiableList(type_);
      }
      /**
       * <code>repeated int32 type = 1;</code>
       *
       * <pre>
       *1我的记录 2巅峰之战
       * </pre>
       */
      public int getTypeCount() {
        return type_.size();
      }
      /**
       * <code>repeated int32 type = 1;</code>
       *
       * <pre>
       *1我的记录 2巅峰之战
       * </pre>
       */
      public int getType(int index) {
        return type_.get(index);
      }
      /**
       * <code>repeated int32 type = 1;</code>
       *
       * <pre>
       *1我的记录 2巅峰之战
       * </pre>
       */
      public Builder setType(
          int index, int value) {
        ensureTypeIsMutable();
        type_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 type = 1;</code>
       *
       * <pre>
       *1我的记录 2巅峰之战
       * </pre>
       */
      public Builder addType(int value) {
        ensureTypeIsMutable();
        type_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 type = 1;</code>
       *
       * <pre>
       *1我的记录 2巅峰之战
       * </pre>
       */
      public Builder addAllType(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureTypeIsMutable();
        super.addAll(values, type_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 type = 1;</code>
       *
       * <pre>
       *1我的记录 2巅峰之战
       * </pre>
       */
      public Builder clearType() {
        type_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }

      // repeated .PBArenaChallengeRecordInfo recordInfo = 2;
      private java.util.List<ArenaPb.PBArenaChallengeRecordInfo> recordInfo_ =
        java.util.Collections.emptyList();
      private void ensureRecordInfoIsMutable() {
        if (!((bitField0_ & 0x00000002) == 0x00000002)) {
          recordInfo_ = new java.util.ArrayList<ArenaPb.PBArenaChallengeRecordInfo>(recordInfo_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          ArenaPb.PBArenaChallengeRecordInfo, ArenaPb.PBArenaChallengeRecordInfo.Builder, ArenaPb.PBArenaChallengeRecordInfoOrBuilder> recordInfoBuilder_;

      /**
       * <code>repeated .PBArenaChallengeRecordInfo recordInfo = 2;</code>
       *
       * <pre>
       *玩家挑战结果列表数据
       * </pre>
       */
      public java.util.List<ArenaPb.PBArenaChallengeRecordInfo> getRecordInfoList() {
        if (recordInfoBuilder_ == null) {
          return java.util.Collections.unmodifiableList(recordInfo_);
        } else {
          return recordInfoBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .PBArenaChallengeRecordInfo recordInfo = 2;</code>
       *
       * <pre>
       *玩家挑战结果列表数据
       * </pre>
       */
      public int getRecordInfoCount() {
        if (recordInfoBuilder_ == null) {
          return recordInfo_.size();
        } else {
          return recordInfoBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .PBArenaChallengeRecordInfo recordInfo = 2;</code>
       *
       * <pre>
       *玩家挑战结果列表数据
       * </pre>
       */
      public ArenaPb.PBArenaChallengeRecordInfo getRecordInfo(int index) {
        if (recordInfoBuilder_ == null) {
          return recordInfo_.get(index);
        } else {
          return recordInfoBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .PBArenaChallengeRecordInfo recordInfo = 2;</code>
       *
       * <pre>
       *玩家挑战结果列表数据
       * </pre>
       */
      public Builder setRecordInfo(
          int index, ArenaPb.PBArenaChallengeRecordInfo value) {
        if (recordInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRecordInfoIsMutable();
          recordInfo_.set(index, value);
          onChanged();
        } else {
          recordInfoBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .PBArenaChallengeRecordInfo recordInfo = 2;</code>
       *
       * <pre>
       *玩家挑战结果列表数据
       * </pre>
       */
      public Builder setRecordInfo(
          int index, ArenaPb.PBArenaChallengeRecordInfo.Builder builderForValue) {
        if (recordInfoBuilder_ == null) {
          ensureRecordInfoIsMutable();
          recordInfo_.set(index, builderForValue.build());
          onChanged();
        } else {
          recordInfoBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .PBArenaChallengeRecordInfo recordInfo = 2;</code>
       *
       * <pre>
       *玩家挑战结果列表数据
       * </pre>
       */
      public Builder addRecordInfo(ArenaPb.PBArenaChallengeRecordInfo value) {
        if (recordInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRecordInfoIsMutable();
          recordInfo_.add(value);
          onChanged();
        } else {
          recordInfoBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .PBArenaChallengeRecordInfo recordInfo = 2;</code>
       *
       * <pre>
       *玩家挑战结果列表数据
       * </pre>
       */
      public Builder addRecordInfo(
          int index, ArenaPb.PBArenaChallengeRecordInfo value) {
        if (recordInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRecordInfoIsMutable();
          recordInfo_.add(index, value);
          onChanged();
        } else {
          recordInfoBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .PBArenaChallengeRecordInfo recordInfo = 2;</code>
       *
       * <pre>
       *玩家挑战结果列表数据
       * </pre>
       */
      public Builder addRecordInfo(
          ArenaPb.PBArenaChallengeRecordInfo.Builder builderForValue) {
        if (recordInfoBuilder_ == null) {
          ensureRecordInfoIsMutable();
          recordInfo_.add(builderForValue.build());
          onChanged();
        } else {
          recordInfoBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .PBArenaChallengeRecordInfo recordInfo = 2;</code>
       *
       * <pre>
       *玩家挑战结果列表数据
       * </pre>
       */
      public Builder addRecordInfo(
          int index, ArenaPb.PBArenaChallengeRecordInfo.Builder builderForValue) {
        if (recordInfoBuilder_ == null) {
          ensureRecordInfoIsMutable();
          recordInfo_.add(index, builderForValue.build());
          onChanged();
        } else {
          recordInfoBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .PBArenaChallengeRecordInfo recordInfo = 2;</code>
       *
       * <pre>
       *玩家挑战结果列表数据
       * </pre>
       */
      public Builder addAllRecordInfo(
          java.lang.Iterable<? extends ArenaPb.PBArenaChallengeRecordInfo> values) {
        if (recordInfoBuilder_ == null) {
          ensureRecordInfoIsMutable();
          super.addAll(values, recordInfo_);
          onChanged();
        } else {
          recordInfoBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .PBArenaChallengeRecordInfo recordInfo = 2;</code>
       *
       * <pre>
       *玩家挑战结果列表数据
       * </pre>
       */
      public Builder clearRecordInfo() {
        if (recordInfoBuilder_ == null) {
          recordInfo_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          recordInfoBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .PBArenaChallengeRecordInfo recordInfo = 2;</code>
       *
       * <pre>
       *玩家挑战结果列表数据
       * </pre>
       */
      public Builder removeRecordInfo(int index) {
        if (recordInfoBuilder_ == null) {
          ensureRecordInfoIsMutable();
          recordInfo_.remove(index);
          onChanged();
        } else {
          recordInfoBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .PBArenaChallengeRecordInfo recordInfo = 2;</code>
       *
       * <pre>
       *玩家挑战结果列表数据
       * </pre>
       */
      public ArenaPb.PBArenaChallengeRecordInfo.Builder getRecordInfoBuilder(
          int index) {
        return getRecordInfoFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .PBArenaChallengeRecordInfo recordInfo = 2;</code>
       *
       * <pre>
       *玩家挑战结果列表数据
       * </pre>
       */
      public ArenaPb.PBArenaChallengeRecordInfoOrBuilder getRecordInfoOrBuilder(
          int index) {
        if (recordInfoBuilder_ == null) {
          return recordInfo_.get(index);  } else {
          return recordInfoBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .PBArenaChallengeRecordInfo recordInfo = 2;</code>
       *
       * <pre>
       *玩家挑战结果列表数据
       * </pre>
       */
      public java.util.List<? extends ArenaPb.PBArenaChallengeRecordInfoOrBuilder> 
           getRecordInfoOrBuilderList() {
        if (recordInfoBuilder_ != null) {
          return recordInfoBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(recordInfo_);
        }
      }
      /**
       * <code>repeated .PBArenaChallengeRecordInfo recordInfo = 2;</code>
       *
       * <pre>
       *玩家挑战结果列表数据
       * </pre>
       */
      public ArenaPb.PBArenaChallengeRecordInfo.Builder addRecordInfoBuilder() {
        return getRecordInfoFieldBuilder().addBuilder(
            ArenaPb.PBArenaChallengeRecordInfo.getDefaultInstance());
      }
      /**
       * <code>repeated .PBArenaChallengeRecordInfo recordInfo = 2;</code>
       *
       * <pre>
       *玩家挑战结果列表数据
       * </pre>
       */
      public ArenaPb.PBArenaChallengeRecordInfo.Builder addRecordInfoBuilder(
          int index) {
        return getRecordInfoFieldBuilder().addBuilder(
            index, ArenaPb.PBArenaChallengeRecordInfo.getDefaultInstance());
      }
      /**
       * <code>repeated .PBArenaChallengeRecordInfo recordInfo = 2;</code>
       *
       * <pre>
       *玩家挑战结果列表数据
       * </pre>
       */
      public java.util.List<ArenaPb.PBArenaChallengeRecordInfo.Builder> 
           getRecordInfoBuilderList() {
        return getRecordInfoFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          ArenaPb.PBArenaChallengeRecordInfo, ArenaPb.PBArenaChallengeRecordInfo.Builder, ArenaPb.PBArenaChallengeRecordInfoOrBuilder> 
          getRecordInfoFieldBuilder() {
        if (recordInfoBuilder_ == null) {
          recordInfoBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              ArenaPb.PBArenaChallengeRecordInfo, ArenaPb.PBArenaChallengeRecordInfo.Builder, ArenaPb.PBArenaChallengeRecordInfoOrBuilder>(
                  recordInfo_,
                  ((bitField0_ & 0x00000002) == 0x00000002),
                  getParentForChildren(),
                  isClean());
          recordInfo_ = null;
        }
        return recordInfoBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:PBArenaChallengeRecordInfoList)
    }

    static {
      defaultInstance = new PBArenaChallengeRecordInfoList(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:PBArenaChallengeRecordInfoList)
  }

  public interface PBArenaChallengeRecordInfoOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int64 id = 1;
    /**
     * <code>required int64 id = 1;</code>
     *
     * <pre>
     *战斗结果信息存储id，不是生成的战报详情或者回放的id
     * </pre>
     */
    boolean hasId();
    /**
     * <code>required int64 id = 1;</code>
     *
     * <pre>
     *战斗结果信息存储id，不是生成的战报详情或者回放的id
     * </pre>
     */
    long getId();

    // optional int32 round = 2;
    /**
     * <code>optional int32 round = 2;</code>
     *
     * <pre>
     * </pre>
     */
    boolean hasRound();
    /**
     * <code>optional int32 round = 2;</code>
     *
     * <pre>
     * </pre>
     */
    int getRound();

    // optional .PBBattlePlayerInfo attacker = 3;
    /**
     * <code>optional .PBBattlePlayerInfo attacker = 3;</code>
     *
     * <pre>
     *攻方
     * </pre>
     */
    boolean hasAttacker();
    /**
     * <code>optional .PBBattlePlayerInfo attacker = 3;</code>
     *
     * <pre>
     *攻方
     * </pre>
     */
    MailPb.PBBattlePlayerInfo getAttacker();
    /**
     * <code>optional .PBBattlePlayerInfo attacker = 3;</code>
     *
     * <pre>
     *攻方
     * </pre>
     */
    MailPb.PBBattlePlayerInfoOrBuilder getAttackerOrBuilder();

    // optional .PBBattlePlayerInfo defender = 4;
    /**
     * <code>optional .PBBattlePlayerInfo defender = 4;</code>
     *
     * <pre>
     *守方
     * </pre>
     */
    boolean hasDefender();
    /**
     * <code>optional .PBBattlePlayerInfo defender = 4;</code>
     *
     * <pre>
     *守方
     * </pre>
     */
    MailPb.PBBattlePlayerInfo getDefender();
    /**
     * <code>optional .PBBattlePlayerInfo defender = 4;</code>
     *
     * <pre>
     *守方
     * </pre>
     */
    MailPb.PBBattlePlayerInfoOrBuilder getDefenderOrBuilder();

    // optional int32 time = 5;
    /**
     * <code>optional int32 time = 5;</code>
     *
     * <pre>
     *挑战发生时间戳
     * </pre>
     */
    boolean hasTime();
    /**
     * <code>optional int32 time = 5;</code>
     *
     * <pre>
     *挑战发生时间戳
     * </pre>
     */
    int getTime();

    // optional int64 reportId = 6;
    /**
     * <code>optional int64 reportId = 6;</code>
     *
     * <pre>
     *生成的战报详情或者回放的id
     * </pre>
     */
    boolean hasReportId();
    /**
     * <code>optional int64 reportId = 6;</code>
     *
     * <pre>
     *生成的战报详情或者回放的id
     * </pre>
     */
    long getReportId();

    // optional int32 result = 7;
    /**
     * <code>optional int32 result = 7;</code>
     *
     * <pre>
     *战斗结果
     * </pre>
     */
    boolean hasResult();
    /**
     * <code>optional int32 result = 7;</code>
     *
     * <pre>
     *战斗结果
     * </pre>
     */
    int getResult();

    // optional int32 season = 8;
    /**
     * <code>optional int32 season = 8;</code>
     *
     * <pre>
     *赛季
     * </pre>
     */
    boolean hasSeason();
    /**
     * <code>optional int32 season = 8;</code>
     *
     * <pre>
     *赛季
     * </pre>
     */
    int getSeason();

    // optional .PBPairLongList againstRecords = 9;
    /**
     * <code>optional .PBPairLongList againstRecords = 9;</code>
     *
     * <pre>
     *攻方玩家&lt;原兵数-&gt;存兵活数&gt; 守方玩家&lt;原兵数-&gt;存兵活数&gt;
     * </pre>
     */
    boolean hasAgainstRecords();
    /**
     * <code>optional .PBPairLongList againstRecords = 9;</code>
     *
     * <pre>
     *攻方玩家&lt;原兵数-&gt;存兵活数&gt; 守方玩家&lt;原兵数-&gt;存兵活数&gt;
     * </pre>
     */
    CommonPb.PBPairLongList getAgainstRecords();
    /**
     * <code>optional .PBPairLongList againstRecords = 9;</code>
     *
     * <pre>
     *攻方玩家&lt;原兵数-&gt;存兵活数&gt; 守方玩家&lt;原兵数-&gt;存兵活数&gt;
     * </pre>
     */
    CommonPb.PBPairLongListOrBuilder getAgainstRecordsOrBuilder();
  }
  /**
   * Protobuf type {@code PBArenaChallengeRecordInfo}
   *
   * <pre>
   *竞技场玩家获取我的挑战或者巅峰对决等战报返回列表
   * </pre>
   */
  public static final class PBArenaChallengeRecordInfo extends
      com.google.protobuf.GeneratedMessage
      implements PBArenaChallengeRecordInfoOrBuilder {
    // Use PBArenaChallengeRecordInfo.newBuilder() to construct.
    private PBArenaChallengeRecordInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private PBArenaChallengeRecordInfo(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final PBArenaChallengeRecordInfo defaultInstance;
    public static PBArenaChallengeRecordInfo getDefaultInstance() {
      return defaultInstance;
    }

    public PBArenaChallengeRecordInfo getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private PBArenaChallengeRecordInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              id_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              round_ = input.readInt32();
              break;
            }
            case 26: {
              MailPb.PBBattlePlayerInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000004) == 0x00000004)) {
                subBuilder = attacker_.toBuilder();
              }
              attacker_ = input.readMessage(MailPb.PBBattlePlayerInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(attacker_);
                attacker_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000004;
              break;
            }
            case 34: {
              MailPb.PBBattlePlayerInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000008) == 0x00000008)) {
                subBuilder = defender_.toBuilder();
              }
              defender_ = input.readMessage(MailPb.PBBattlePlayerInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(defender_);
                defender_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000008;
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              time_ = input.readInt32();
              break;
            }
            case 48: {
              bitField0_ |= 0x00000020;
              reportId_ = input.readInt64();
              break;
            }
            case 56: {
              bitField0_ |= 0x00000040;
              result_ = input.readInt32();
              break;
            }
            case 64: {
              bitField0_ |= 0x00000080;
              season_ = input.readInt32();
              break;
            }
            case 74: {
              CommonPb.PBPairLongList.Builder subBuilder = null;
              if (((bitField0_ & 0x00000100) == 0x00000100)) {
                subBuilder = againstRecords_.toBuilder();
              }
              againstRecords_ = input.readMessage(CommonPb.PBPairLongList.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(againstRecords_);
                againstRecords_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000100;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ArenaPb.internal_static_PBArenaChallengeRecordInfo_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ArenaPb.internal_static_PBArenaChallengeRecordInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ArenaPb.PBArenaChallengeRecordInfo.class, ArenaPb.PBArenaChallengeRecordInfo.Builder.class);
    }

    public static com.google.protobuf.Parser<PBArenaChallengeRecordInfo> PARSER =
        new com.google.protobuf.AbstractParser<PBArenaChallengeRecordInfo>() {
      public PBArenaChallengeRecordInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PBArenaChallengeRecordInfo(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<PBArenaChallengeRecordInfo> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int64 id = 1;
    public static final int ID_FIELD_NUMBER = 1;
    private long id_;
    /**
     * <code>required int64 id = 1;</code>
     *
     * <pre>
     *战斗结果信息存储id，不是生成的战报详情或者回放的id
     * </pre>
     */
    public boolean hasId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int64 id = 1;</code>
     *
     * <pre>
     *战斗结果信息存储id，不是生成的战报详情或者回放的id
     * </pre>
     */
    public long getId() {
      return id_;
    }

    // optional int32 round = 2;
    public static final int ROUND_FIELD_NUMBER = 2;
    private int round_;
    /**
     * <code>optional int32 round = 2;</code>
     *
     * <pre>
     * </pre>
     */
    public boolean hasRound() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional int32 round = 2;</code>
     *
     * <pre>
     * </pre>
     */
    public int getRound() {
      return round_;
    }

    // optional .PBBattlePlayerInfo attacker = 3;
    public static final int ATTACKER_FIELD_NUMBER = 3;
    private MailPb.PBBattlePlayerInfo attacker_;
    /**
     * <code>optional .PBBattlePlayerInfo attacker = 3;</code>
     *
     * <pre>
     *攻方
     * </pre>
     */
    public boolean hasAttacker() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional .PBBattlePlayerInfo attacker = 3;</code>
     *
     * <pre>
     *攻方
     * </pre>
     */
    public MailPb.PBBattlePlayerInfo getAttacker() {
      return attacker_;
    }
    /**
     * <code>optional .PBBattlePlayerInfo attacker = 3;</code>
     *
     * <pre>
     *攻方
     * </pre>
     */
    public MailPb.PBBattlePlayerInfoOrBuilder getAttackerOrBuilder() {
      return attacker_;
    }

    // optional .PBBattlePlayerInfo defender = 4;
    public static final int DEFENDER_FIELD_NUMBER = 4;
    private MailPb.PBBattlePlayerInfo defender_;
    /**
     * <code>optional .PBBattlePlayerInfo defender = 4;</code>
     *
     * <pre>
     *守方
     * </pre>
     */
    public boolean hasDefender() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional .PBBattlePlayerInfo defender = 4;</code>
     *
     * <pre>
     *守方
     * </pre>
     */
    public MailPb.PBBattlePlayerInfo getDefender() {
      return defender_;
    }
    /**
     * <code>optional .PBBattlePlayerInfo defender = 4;</code>
     *
     * <pre>
     *守方
     * </pre>
     */
    public MailPb.PBBattlePlayerInfoOrBuilder getDefenderOrBuilder() {
      return defender_;
    }

    // optional int32 time = 5;
    public static final int TIME_FIELD_NUMBER = 5;
    private int time_;
    /**
     * <code>optional int32 time = 5;</code>
     *
     * <pre>
     *挑战发生时间戳
     * </pre>
     */
    public boolean hasTime() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>optional int32 time = 5;</code>
     *
     * <pre>
     *挑战发生时间戳
     * </pre>
     */
    public int getTime() {
      return time_;
    }

    // optional int64 reportId = 6;
    public static final int REPORTID_FIELD_NUMBER = 6;
    private long reportId_;
    /**
     * <code>optional int64 reportId = 6;</code>
     *
     * <pre>
     *生成的战报详情或者回放的id
     * </pre>
     */
    public boolean hasReportId() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <code>optional int64 reportId = 6;</code>
     *
     * <pre>
     *生成的战报详情或者回放的id
     * </pre>
     */
    public long getReportId() {
      return reportId_;
    }

    // optional int32 result = 7;
    public static final int RESULT_FIELD_NUMBER = 7;
    private int result_;
    /**
     * <code>optional int32 result = 7;</code>
     *
     * <pre>
     *战斗结果
     * </pre>
     */
    public boolean hasResult() {
      return ((bitField0_ & 0x00000040) == 0x00000040);
    }
    /**
     * <code>optional int32 result = 7;</code>
     *
     * <pre>
     *战斗结果
     * </pre>
     */
    public int getResult() {
      return result_;
    }

    // optional int32 season = 8;
    public static final int SEASON_FIELD_NUMBER = 8;
    private int season_;
    /**
     * <code>optional int32 season = 8;</code>
     *
     * <pre>
     *赛季
     * </pre>
     */
    public boolean hasSeason() {
      return ((bitField0_ & 0x00000080) == 0x00000080);
    }
    /**
     * <code>optional int32 season = 8;</code>
     *
     * <pre>
     *赛季
     * </pre>
     */
    public int getSeason() {
      return season_;
    }

    // optional .PBPairLongList againstRecords = 9;
    public static final int AGAINSTRECORDS_FIELD_NUMBER = 9;
    private CommonPb.PBPairLongList againstRecords_;
    /**
     * <code>optional .PBPairLongList againstRecords = 9;</code>
     *
     * <pre>
     *攻方玩家&lt;原兵数-&gt;存兵活数&gt; 守方玩家&lt;原兵数-&gt;存兵活数&gt;
     * </pre>
     */
    public boolean hasAgainstRecords() {
      return ((bitField0_ & 0x00000100) == 0x00000100);
    }
    /**
     * <code>optional .PBPairLongList againstRecords = 9;</code>
     *
     * <pre>
     *攻方玩家&lt;原兵数-&gt;存兵活数&gt; 守方玩家&lt;原兵数-&gt;存兵活数&gt;
     * </pre>
     */
    public CommonPb.PBPairLongList getAgainstRecords() {
      return againstRecords_;
    }
    /**
     * <code>optional .PBPairLongList againstRecords = 9;</code>
     *
     * <pre>
     *攻方玩家&lt;原兵数-&gt;存兵活数&gt; 守方玩家&lt;原兵数-&gt;存兵活数&gt;
     * </pre>
     */
    public CommonPb.PBPairLongListOrBuilder getAgainstRecordsOrBuilder() {
      return againstRecords_;
    }

    private void initFields() {
      id_ = 0L;
      round_ = 0;
      attacker_ = MailPb.PBBattlePlayerInfo.getDefaultInstance();
      defender_ = MailPb.PBBattlePlayerInfo.getDefaultInstance();
      time_ = 0;
      reportId_ = 0L;
      result_ = 0;
      season_ = 0;
      againstRecords_ = CommonPb.PBPairLongList.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (hasAttacker()) {
        if (!getAttacker().isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      if (hasDefender()) {
        if (!getDefender().isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      if (hasAgainstRecords()) {
        if (!getAgainstRecords().isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(1, id_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, round_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeMessage(3, attacker_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeMessage(4, defender_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeInt32(5, time_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        output.writeInt64(6, reportId_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        output.writeInt32(7, result_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        output.writeInt32(8, season_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        output.writeMessage(9, againstRecords_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, id_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, round_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, attacker_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, defender_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, time_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(6, reportId_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(7, result_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(8, season_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(9, againstRecords_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static ArenaPb.PBArenaChallengeRecordInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ArenaPb.PBArenaChallengeRecordInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ArenaPb.PBArenaChallengeRecordInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ArenaPb.PBArenaChallengeRecordInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ArenaPb.PBArenaChallengeRecordInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static ArenaPb.PBArenaChallengeRecordInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static ArenaPb.PBArenaChallengeRecordInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static ArenaPb.PBArenaChallengeRecordInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static ArenaPb.PBArenaChallengeRecordInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static ArenaPb.PBArenaChallengeRecordInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(ArenaPb.PBArenaChallengeRecordInfo prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code PBArenaChallengeRecordInfo}
     *
     * <pre>
     *竞技场玩家获取我的挑战或者巅峰对决等战报返回列表
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements ArenaPb.PBArenaChallengeRecordInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ArenaPb.internal_static_PBArenaChallengeRecordInfo_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ArenaPb.internal_static_PBArenaChallengeRecordInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ArenaPb.PBArenaChallengeRecordInfo.class, ArenaPb.PBArenaChallengeRecordInfo.Builder.class);
      }

      // Construct using ArenaPb.PBArenaChallengeRecordInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getAttackerFieldBuilder();
          getDefenderFieldBuilder();
          getAgainstRecordsFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        id_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        round_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        if (attackerBuilder_ == null) {
          attacker_ = MailPb.PBBattlePlayerInfo.getDefaultInstance();
        } else {
          attackerBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        if (defenderBuilder_ == null) {
          defender_ = MailPb.PBBattlePlayerInfo.getDefaultInstance();
        } else {
          defenderBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        time_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        reportId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000020);
        result_ = 0;
        bitField0_ = (bitField0_ & ~0x00000040);
        season_ = 0;
        bitField0_ = (bitField0_ & ~0x00000080);
        if (againstRecordsBuilder_ == null) {
          againstRecords_ = CommonPb.PBPairLongList.getDefaultInstance();
        } else {
          againstRecordsBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000100);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ArenaPb.internal_static_PBArenaChallengeRecordInfo_descriptor;
      }

      public ArenaPb.PBArenaChallengeRecordInfo getDefaultInstanceForType() {
        return ArenaPb.PBArenaChallengeRecordInfo.getDefaultInstance();
      }

      public ArenaPb.PBArenaChallengeRecordInfo build() {
        ArenaPb.PBArenaChallengeRecordInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public ArenaPb.PBArenaChallengeRecordInfo buildPartial() {
        ArenaPb.PBArenaChallengeRecordInfo result = new ArenaPb.PBArenaChallengeRecordInfo(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.id_ = id_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.round_ = round_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        if (attackerBuilder_ == null) {
          result.attacker_ = attacker_;
        } else {
          result.attacker_ = attackerBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        if (defenderBuilder_ == null) {
          result.defender_ = defender_;
        } else {
          result.defender_ = defenderBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.time_ = time_;
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000020;
        }
        result.reportId_ = reportId_;
        if (((from_bitField0_ & 0x00000040) == 0x00000040)) {
          to_bitField0_ |= 0x00000040;
        }
        result.result_ = result_;
        if (((from_bitField0_ & 0x00000080) == 0x00000080)) {
          to_bitField0_ |= 0x00000080;
        }
        result.season_ = season_;
        if (((from_bitField0_ & 0x00000100) == 0x00000100)) {
          to_bitField0_ |= 0x00000100;
        }
        if (againstRecordsBuilder_ == null) {
          result.againstRecords_ = againstRecords_;
        } else {
          result.againstRecords_ = againstRecordsBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof ArenaPb.PBArenaChallengeRecordInfo) {
          return mergeFrom((ArenaPb.PBArenaChallengeRecordInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(ArenaPb.PBArenaChallengeRecordInfo other) {
        if (other == ArenaPb.PBArenaChallengeRecordInfo.getDefaultInstance()) return this;
        if (other.hasId()) {
          setId(other.getId());
        }
        if (other.hasRound()) {
          setRound(other.getRound());
        }
        if (other.hasAttacker()) {
          mergeAttacker(other.getAttacker());
        }
        if (other.hasDefender()) {
          mergeDefender(other.getDefender());
        }
        if (other.hasTime()) {
          setTime(other.getTime());
        }
        if (other.hasReportId()) {
          setReportId(other.getReportId());
        }
        if (other.hasResult()) {
          setResult(other.getResult());
        }
        if (other.hasSeason()) {
          setSeason(other.getSeason());
        }
        if (other.hasAgainstRecords()) {
          mergeAgainstRecords(other.getAgainstRecords());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasId()) {
          
          return false;
        }
        if (hasAttacker()) {
          if (!getAttacker().isInitialized()) {
            
            return false;
          }
        }
        if (hasDefender()) {
          if (!getDefender().isInitialized()) {
            
            return false;
          }
        }
        if (hasAgainstRecords()) {
          if (!getAgainstRecords().isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        ArenaPb.PBArenaChallengeRecordInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (ArenaPb.PBArenaChallengeRecordInfo) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int64 id = 1;
      private long id_ ;
      /**
       * <code>required int64 id = 1;</code>
       *
       * <pre>
       *战斗结果信息存储id，不是生成的战报详情或者回放的id
       * </pre>
       */
      public boolean hasId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int64 id = 1;</code>
       *
       * <pre>
       *战斗结果信息存储id，不是生成的战报详情或者回放的id
       * </pre>
       */
      public long getId() {
        return id_;
      }
      /**
       * <code>required int64 id = 1;</code>
       *
       * <pre>
       *战斗结果信息存储id，不是生成的战报详情或者回放的id
       * </pre>
       */
      public Builder setId(long value) {
        bitField0_ |= 0x00000001;
        id_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int64 id = 1;</code>
       *
       * <pre>
       *战斗结果信息存储id，不是生成的战报详情或者回放的id
       * </pre>
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        id_ = 0L;
        onChanged();
        return this;
      }

      // optional int32 round = 2;
      private int round_ ;
      /**
       * <code>optional int32 round = 2;</code>
       *
       * <pre>
       * </pre>
       */
      public boolean hasRound() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional int32 round = 2;</code>
       *
       * <pre>
       * </pre>
       */
      public int getRound() {
        return round_;
      }
      /**
       * <code>optional int32 round = 2;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setRound(int value) {
        bitField0_ |= 0x00000002;
        round_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 round = 2;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder clearRound() {
        bitField0_ = (bitField0_ & ~0x00000002);
        round_ = 0;
        onChanged();
        return this;
      }

      // optional .PBBattlePlayerInfo attacker = 3;
      private MailPb.PBBattlePlayerInfo attacker_ = MailPb.PBBattlePlayerInfo.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          MailPb.PBBattlePlayerInfo, MailPb.PBBattlePlayerInfo.Builder, MailPb.PBBattlePlayerInfoOrBuilder> attackerBuilder_;
      /**
       * <code>optional .PBBattlePlayerInfo attacker = 3;</code>
       *
       * <pre>
       *攻方
       * </pre>
       */
      public boolean hasAttacker() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional .PBBattlePlayerInfo attacker = 3;</code>
       *
       * <pre>
       *攻方
       * </pre>
       */
      public MailPb.PBBattlePlayerInfo getAttacker() {
        if (attackerBuilder_ == null) {
          return attacker_;
        } else {
          return attackerBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .PBBattlePlayerInfo attacker = 3;</code>
       *
       * <pre>
       *攻方
       * </pre>
       */
      public Builder setAttacker(MailPb.PBBattlePlayerInfo value) {
        if (attackerBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          attacker_ = value;
          onChanged();
        } else {
          attackerBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .PBBattlePlayerInfo attacker = 3;</code>
       *
       * <pre>
       *攻方
       * </pre>
       */
      public Builder setAttacker(
          MailPb.PBBattlePlayerInfo.Builder builderForValue) {
        if (attackerBuilder_ == null) {
          attacker_ = builderForValue.build();
          onChanged();
        } else {
          attackerBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .PBBattlePlayerInfo attacker = 3;</code>
       *
       * <pre>
       *攻方
       * </pre>
       */
      public Builder mergeAttacker(MailPb.PBBattlePlayerInfo value) {
        if (attackerBuilder_ == null) {
          if (((bitField0_ & 0x00000004) == 0x00000004) &&
              attacker_ != MailPb.PBBattlePlayerInfo.getDefaultInstance()) {
            attacker_ =
              MailPb.PBBattlePlayerInfo.newBuilder(attacker_).mergeFrom(value).buildPartial();
          } else {
            attacker_ = value;
          }
          onChanged();
        } else {
          attackerBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .PBBattlePlayerInfo attacker = 3;</code>
       *
       * <pre>
       *攻方
       * </pre>
       */
      public Builder clearAttacker() {
        if (attackerBuilder_ == null) {
          attacker_ = MailPb.PBBattlePlayerInfo.getDefaultInstance();
          onChanged();
        } else {
          attackerBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }
      /**
       * <code>optional .PBBattlePlayerInfo attacker = 3;</code>
       *
       * <pre>
       *攻方
       * </pre>
       */
      public MailPb.PBBattlePlayerInfo.Builder getAttackerBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getAttackerFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .PBBattlePlayerInfo attacker = 3;</code>
       *
       * <pre>
       *攻方
       * </pre>
       */
      public MailPb.PBBattlePlayerInfoOrBuilder getAttackerOrBuilder() {
        if (attackerBuilder_ != null) {
          return attackerBuilder_.getMessageOrBuilder();
        } else {
          return attacker_;
        }
      }
      /**
       * <code>optional .PBBattlePlayerInfo attacker = 3;</code>
       *
       * <pre>
       *攻方
       * </pre>
       */
      private com.google.protobuf.SingleFieldBuilder<
          MailPb.PBBattlePlayerInfo, MailPb.PBBattlePlayerInfo.Builder, MailPb.PBBattlePlayerInfoOrBuilder> 
          getAttackerFieldBuilder() {
        if (attackerBuilder_ == null) {
          attackerBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              MailPb.PBBattlePlayerInfo, MailPb.PBBattlePlayerInfo.Builder, MailPb.PBBattlePlayerInfoOrBuilder>(
                  attacker_,
                  getParentForChildren(),
                  isClean());
          attacker_ = null;
        }
        return attackerBuilder_;
      }

      // optional .PBBattlePlayerInfo defender = 4;
      private MailPb.PBBattlePlayerInfo defender_ = MailPb.PBBattlePlayerInfo.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          MailPb.PBBattlePlayerInfo, MailPb.PBBattlePlayerInfo.Builder, MailPb.PBBattlePlayerInfoOrBuilder> defenderBuilder_;
      /**
       * <code>optional .PBBattlePlayerInfo defender = 4;</code>
       *
       * <pre>
       *守方
       * </pre>
       */
      public boolean hasDefender() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional .PBBattlePlayerInfo defender = 4;</code>
       *
       * <pre>
       *守方
       * </pre>
       */
      public MailPb.PBBattlePlayerInfo getDefender() {
        if (defenderBuilder_ == null) {
          return defender_;
        } else {
          return defenderBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .PBBattlePlayerInfo defender = 4;</code>
       *
       * <pre>
       *守方
       * </pre>
       */
      public Builder setDefender(MailPb.PBBattlePlayerInfo value) {
        if (defenderBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          defender_ = value;
          onChanged();
        } else {
          defenderBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <code>optional .PBBattlePlayerInfo defender = 4;</code>
       *
       * <pre>
       *守方
       * </pre>
       */
      public Builder setDefender(
          MailPb.PBBattlePlayerInfo.Builder builderForValue) {
        if (defenderBuilder_ == null) {
          defender_ = builderForValue.build();
          onChanged();
        } else {
          defenderBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <code>optional .PBBattlePlayerInfo defender = 4;</code>
       *
       * <pre>
       *守方
       * </pre>
       */
      public Builder mergeDefender(MailPb.PBBattlePlayerInfo value) {
        if (defenderBuilder_ == null) {
          if (((bitField0_ & 0x00000008) == 0x00000008) &&
              defender_ != MailPb.PBBattlePlayerInfo.getDefaultInstance()) {
            defender_ =
              MailPb.PBBattlePlayerInfo.newBuilder(defender_).mergeFrom(value).buildPartial();
          } else {
            defender_ = value;
          }
          onChanged();
        } else {
          defenderBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <code>optional .PBBattlePlayerInfo defender = 4;</code>
       *
       * <pre>
       *守方
       * </pre>
       */
      public Builder clearDefender() {
        if (defenderBuilder_ == null) {
          defender_ = MailPb.PBBattlePlayerInfo.getDefaultInstance();
          onChanged();
        } else {
          defenderBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }
      /**
       * <code>optional .PBBattlePlayerInfo defender = 4;</code>
       *
       * <pre>
       *守方
       * </pre>
       */
      public MailPb.PBBattlePlayerInfo.Builder getDefenderBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getDefenderFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .PBBattlePlayerInfo defender = 4;</code>
       *
       * <pre>
       *守方
       * </pre>
       */
      public MailPb.PBBattlePlayerInfoOrBuilder getDefenderOrBuilder() {
        if (defenderBuilder_ != null) {
          return defenderBuilder_.getMessageOrBuilder();
        } else {
          return defender_;
        }
      }
      /**
       * <code>optional .PBBattlePlayerInfo defender = 4;</code>
       *
       * <pre>
       *守方
       * </pre>
       */
      private com.google.protobuf.SingleFieldBuilder<
          MailPb.PBBattlePlayerInfo, MailPb.PBBattlePlayerInfo.Builder, MailPb.PBBattlePlayerInfoOrBuilder> 
          getDefenderFieldBuilder() {
        if (defenderBuilder_ == null) {
          defenderBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              MailPb.PBBattlePlayerInfo, MailPb.PBBattlePlayerInfo.Builder, MailPb.PBBattlePlayerInfoOrBuilder>(
                  defender_,
                  getParentForChildren(),
                  isClean());
          defender_ = null;
        }
        return defenderBuilder_;
      }

      // optional int32 time = 5;
      private int time_ ;
      /**
       * <code>optional int32 time = 5;</code>
       *
       * <pre>
       *挑战发生时间戳
       * </pre>
       */
      public boolean hasTime() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional int32 time = 5;</code>
       *
       * <pre>
       *挑战发生时间戳
       * </pre>
       */
      public int getTime() {
        return time_;
      }
      /**
       * <code>optional int32 time = 5;</code>
       *
       * <pre>
       *挑战发生时间戳
       * </pre>
       */
      public Builder setTime(int value) {
        bitField0_ |= 0x00000010;
        time_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 time = 5;</code>
       *
       * <pre>
       *挑战发生时间戳
       * </pre>
       */
      public Builder clearTime() {
        bitField0_ = (bitField0_ & ~0x00000010);
        time_ = 0;
        onChanged();
        return this;
      }

      // optional int64 reportId = 6;
      private long reportId_ ;
      /**
       * <code>optional int64 reportId = 6;</code>
       *
       * <pre>
       *生成的战报详情或者回放的id
       * </pre>
       */
      public boolean hasReportId() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <code>optional int64 reportId = 6;</code>
       *
       * <pre>
       *生成的战报详情或者回放的id
       * </pre>
       */
      public long getReportId() {
        return reportId_;
      }
      /**
       * <code>optional int64 reportId = 6;</code>
       *
       * <pre>
       *生成的战报详情或者回放的id
       * </pre>
       */
      public Builder setReportId(long value) {
        bitField0_ |= 0x00000020;
        reportId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 reportId = 6;</code>
       *
       * <pre>
       *生成的战报详情或者回放的id
       * </pre>
       */
      public Builder clearReportId() {
        bitField0_ = (bitField0_ & ~0x00000020);
        reportId_ = 0L;
        onChanged();
        return this;
      }

      // optional int32 result = 7;
      private int result_ ;
      /**
       * <code>optional int32 result = 7;</code>
       *
       * <pre>
       *战斗结果
       * </pre>
       */
      public boolean hasResult() {
        return ((bitField0_ & 0x00000040) == 0x00000040);
      }
      /**
       * <code>optional int32 result = 7;</code>
       *
       * <pre>
       *战斗结果
       * </pre>
       */
      public int getResult() {
        return result_;
      }
      /**
       * <code>optional int32 result = 7;</code>
       *
       * <pre>
       *战斗结果
       * </pre>
       */
      public Builder setResult(int value) {
        bitField0_ |= 0x00000040;
        result_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 result = 7;</code>
       *
       * <pre>
       *战斗结果
       * </pre>
       */
      public Builder clearResult() {
        bitField0_ = (bitField0_ & ~0x00000040);
        result_ = 0;
        onChanged();
        return this;
      }

      // optional int32 season = 8;
      private int season_ ;
      /**
       * <code>optional int32 season = 8;</code>
       *
       * <pre>
       *赛季
       * </pre>
       */
      public boolean hasSeason() {
        return ((bitField0_ & 0x00000080) == 0x00000080);
      }
      /**
       * <code>optional int32 season = 8;</code>
       *
       * <pre>
       *赛季
       * </pre>
       */
      public int getSeason() {
        return season_;
      }
      /**
       * <code>optional int32 season = 8;</code>
       *
       * <pre>
       *赛季
       * </pre>
       */
      public Builder setSeason(int value) {
        bitField0_ |= 0x00000080;
        season_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 season = 8;</code>
       *
       * <pre>
       *赛季
       * </pre>
       */
      public Builder clearSeason() {
        bitField0_ = (bitField0_ & ~0x00000080);
        season_ = 0;
        onChanged();
        return this;
      }

      // optional .PBPairLongList againstRecords = 9;
      private CommonPb.PBPairLongList againstRecords_ = CommonPb.PBPairLongList.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          CommonPb.PBPairLongList, CommonPb.PBPairLongList.Builder, CommonPb.PBPairLongListOrBuilder> againstRecordsBuilder_;
      /**
       * <code>optional .PBPairLongList againstRecords = 9;</code>
       *
       * <pre>
       *攻方玩家&lt;原兵数-&gt;存兵活数&gt; 守方玩家&lt;原兵数-&gt;存兵活数&gt;
       * </pre>
       */
      public boolean hasAgainstRecords() {
        return ((bitField0_ & 0x00000100) == 0x00000100);
      }
      /**
       * <code>optional .PBPairLongList againstRecords = 9;</code>
       *
       * <pre>
       *攻方玩家&lt;原兵数-&gt;存兵活数&gt; 守方玩家&lt;原兵数-&gt;存兵活数&gt;
       * </pre>
       */
      public CommonPb.PBPairLongList getAgainstRecords() {
        if (againstRecordsBuilder_ == null) {
          return againstRecords_;
        } else {
          return againstRecordsBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .PBPairLongList againstRecords = 9;</code>
       *
       * <pre>
       *攻方玩家&lt;原兵数-&gt;存兵活数&gt; 守方玩家&lt;原兵数-&gt;存兵活数&gt;
       * </pre>
       */
      public Builder setAgainstRecords(CommonPb.PBPairLongList value) {
        if (againstRecordsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          againstRecords_ = value;
          onChanged();
        } else {
          againstRecordsBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000100;
        return this;
      }
      /**
       * <code>optional .PBPairLongList againstRecords = 9;</code>
       *
       * <pre>
       *攻方玩家&lt;原兵数-&gt;存兵活数&gt; 守方玩家&lt;原兵数-&gt;存兵活数&gt;
       * </pre>
       */
      public Builder setAgainstRecords(
          CommonPb.PBPairLongList.Builder builderForValue) {
        if (againstRecordsBuilder_ == null) {
          againstRecords_ = builderForValue.build();
          onChanged();
        } else {
          againstRecordsBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000100;
        return this;
      }
      /**
       * <code>optional .PBPairLongList againstRecords = 9;</code>
       *
       * <pre>
       *攻方玩家&lt;原兵数-&gt;存兵活数&gt; 守方玩家&lt;原兵数-&gt;存兵活数&gt;
       * </pre>
       */
      public Builder mergeAgainstRecords(CommonPb.PBPairLongList value) {
        if (againstRecordsBuilder_ == null) {
          if (((bitField0_ & 0x00000100) == 0x00000100) &&
              againstRecords_ != CommonPb.PBPairLongList.getDefaultInstance()) {
            againstRecords_ =
              CommonPb.PBPairLongList.newBuilder(againstRecords_).mergeFrom(value).buildPartial();
          } else {
            againstRecords_ = value;
          }
          onChanged();
        } else {
          againstRecordsBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000100;
        return this;
      }
      /**
       * <code>optional .PBPairLongList againstRecords = 9;</code>
       *
       * <pre>
       *攻方玩家&lt;原兵数-&gt;存兵活数&gt; 守方玩家&lt;原兵数-&gt;存兵活数&gt;
       * </pre>
       */
      public Builder clearAgainstRecords() {
        if (againstRecordsBuilder_ == null) {
          againstRecords_ = CommonPb.PBPairLongList.getDefaultInstance();
          onChanged();
        } else {
          againstRecordsBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000100);
        return this;
      }
      /**
       * <code>optional .PBPairLongList againstRecords = 9;</code>
       *
       * <pre>
       *攻方玩家&lt;原兵数-&gt;存兵活数&gt; 守方玩家&lt;原兵数-&gt;存兵活数&gt;
       * </pre>
       */
      public CommonPb.PBPairLongList.Builder getAgainstRecordsBuilder() {
        bitField0_ |= 0x00000100;
        onChanged();
        return getAgainstRecordsFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .PBPairLongList againstRecords = 9;</code>
       *
       * <pre>
       *攻方玩家&lt;原兵数-&gt;存兵活数&gt; 守方玩家&lt;原兵数-&gt;存兵活数&gt;
       * </pre>
       */
      public CommonPb.PBPairLongListOrBuilder getAgainstRecordsOrBuilder() {
        if (againstRecordsBuilder_ != null) {
          return againstRecordsBuilder_.getMessageOrBuilder();
        } else {
          return againstRecords_;
        }
      }
      /**
       * <code>optional .PBPairLongList againstRecords = 9;</code>
       *
       * <pre>
       *攻方玩家&lt;原兵数-&gt;存兵活数&gt; 守方玩家&lt;原兵数-&gt;存兵活数&gt;
       * </pre>
       */
      private com.google.protobuf.SingleFieldBuilder<
          CommonPb.PBPairLongList, CommonPb.PBPairLongList.Builder, CommonPb.PBPairLongListOrBuilder> 
          getAgainstRecordsFieldBuilder() {
        if (againstRecordsBuilder_ == null) {
          againstRecordsBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              CommonPb.PBPairLongList, CommonPb.PBPairLongList.Builder, CommonPb.PBPairLongListOrBuilder>(
                  againstRecords_,
                  getParentForChildren(),
                  isClean());
          againstRecords_ = null;
        }
        return againstRecordsBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:PBArenaChallengeRecordInfo)
    }

    static {
      defaultInstance = new PBArenaChallengeRecordInfo(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:PBArenaChallengeRecordInfo)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_PBArenaChallengeInfo_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_PBArenaChallengeInfo_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_PBArenaChallengePlayerInfo_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_PBArenaChallengePlayerInfo_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_PBArenaChallengePlayerDetailInfo_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_PBArenaChallengePlayerDetailInfo_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_PBArenaChallengeStateInfo_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_PBArenaChallengeStateInfo_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_PBArenaChallengeRecordInfoList_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_PBArenaChallengeRecordInfoList_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_PBArenaChallengeRecordInfo_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_PBArenaChallengeRecordInfo_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\010Arena.pb\032\tCommon.pb\032\014Jiaochang.pb\032\007Mai" +
      "l.pb\"H\n\024PBArenaChallengeInfo\0220\n\013playerIn" +
      "fos\030\001 \003(\0132\033.PBArenaChallengePlayerInfo\"\201" +
      "\001\n\032PBArenaChallengePlayerInfo\022\014\n\004guid\030\001 " +
      "\002(\003\022\014\n\004name\030\002 \001(\t\022\014\n\004icon\030\003 \001(\005\022\r\n\005level" +
      "\030\004 \001(\005\022\014\n\004rank\030\005 \001(\005\022\r\n\005fight\030\006 \001(\003\022\r\n\005s" +
      "core\030\007 \001(\003\"\230\001\n PBArenaChallengePlayerDet" +
      "ailInfo\022\014\n\004guid\030\001 \002(\003\022\022\n\nplayerName\030\002 \001(" +
      "\t\022\025\n\rwulueSetIndex\030\003 \002(\005\022\026\n\016talentSetInd" +
      "ex\030\004 \002(\005\022#\n\010heroList\030\005 \003(\0132\021.PBHeroLineU",
      "pInfo\"\377\001\n\031PBArenaChallengeStateInfo\022\014\n\004g" +
      "uid\030\001 \002(\003\022\014\n\004rank\030\002 \001(\005\022\r\n\005fight\030\003 \001(\003\022\r" +
      "\n\005score\030\004 \001(\003\022\034\n\024challengeRemainTimes\030\005 " +
      "\001(\005\022\034\n\024challengeCurBuyTimes\030\006 \001(\005\022\034\n\024cha" +
      "llengeMaxBuyTimes\030\007 \001(\005\022\027\n\017lastRefreshTi" +
      "me\030\010 \001(\005\0225\n\ndetailInfo\030\t \001(\0132!.PBArenaCh" +
      "allengePlayerDetailInfo\"_\n\036PBArenaChalle" +
      "ngeRecordInfoList\022\014\n\004type\030\001 \003(\005\022/\n\nrecor" +
      "dInfo\030\002 \003(\0132\033.PBArenaChallengeRecordInfo" +
      "\"\356\001\n\032PBArenaChallengeRecordInfo\022\n\n\002id\030\001 ",
      "\002(\003\022\r\n\005round\030\002 \001(\005\022%\n\010attacker\030\003 \001(\0132\023.P" +
      "BBattlePlayerInfo\022%\n\010defender\030\004 \001(\0132\023.PB" +
      "BattlePlayerInfo\022\014\n\004time\030\005 \001(\005\022\020\n\010report" +
      "Id\030\006 \001(\003\022\016\n\006result\030\007 \001(\005\022\016\n\006season\030\010 \001(\005" +
      "\022\'\n\016againstRecords\030\t \001(\0132\017.PBPairLongLis" +
      "t"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_PBArenaChallengeInfo_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_PBArenaChallengeInfo_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_PBArenaChallengeInfo_descriptor,
              new java.lang.String[] { "PlayerInfos", });
          internal_static_PBArenaChallengePlayerInfo_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_PBArenaChallengePlayerInfo_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_PBArenaChallengePlayerInfo_descriptor,
              new java.lang.String[] { "Guid", "Name", "Icon", "Level", "Rank", "Fight", "Score", });
          internal_static_PBArenaChallengePlayerDetailInfo_descriptor =
            getDescriptor().getMessageTypes().get(2);
          internal_static_PBArenaChallengePlayerDetailInfo_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_PBArenaChallengePlayerDetailInfo_descriptor,
              new java.lang.String[] { "Guid", "PlayerName", "WulueSetIndex", "TalentSetIndex", "HeroList", });
          internal_static_PBArenaChallengeStateInfo_descriptor =
            getDescriptor().getMessageTypes().get(3);
          internal_static_PBArenaChallengeStateInfo_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_PBArenaChallengeStateInfo_descriptor,
              new java.lang.String[] { "Guid", "Rank", "Fight", "Score", "ChallengeRemainTimes", "ChallengeCurBuyTimes", "ChallengeMaxBuyTimes", "LastRefreshTime", "DetailInfo", });
          internal_static_PBArenaChallengeRecordInfoList_descriptor =
            getDescriptor().getMessageTypes().get(4);
          internal_static_PBArenaChallengeRecordInfoList_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_PBArenaChallengeRecordInfoList_descriptor,
              new java.lang.String[] { "Type", "RecordInfo", });
          internal_static_PBArenaChallengeRecordInfo_descriptor =
            getDescriptor().getMessageTypes().get(5);
          internal_static_PBArenaChallengeRecordInfo_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_PBArenaChallengeRecordInfo_descriptor,
              new java.lang.String[] { "Id", "Round", "Attacker", "Defender", "Time", "ReportId", "Result", "Season", "AgainstRecords", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          CommonPb.getDescriptor(),
          JiaochangPb.getDescriptor(),
          MailPb.getDescriptor(),
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
