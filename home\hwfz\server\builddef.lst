-Xajruntimetarget:1.5
-1.6
-encoding
UTF-8
-source
1.6
-target
1.6
-verbose
-classpath
/Users/<USER>/temp/spring-data-shell/repository/org/springframework/data/spring-data-commons/1.12.4.RELEASE/spring-data-commons-1.12.4.RELEASE.jar:/Users/<USER>/temp/spring-data-shell/repository/org/springframework/spring-orm/4.2.8.RELEASE/spring-orm-4.2.8.RELEASE.jar:/Users/<USER>/temp/spring-data-shell/repository/org/springframework/spring-context/4.2.8.RELEASE/spring-context-4.2.8.RELEASE.jar:/Users/<USER>/temp/spring-data-shell/repository/org/springframework/spring-aop/4.2.8.RELEASE/spring-aop-4.2.8.RELEASE.jar:/Users/<USER>/temp/spring-data-shell/repository/org/springframework/spring-tx/4.2.8.RELEASE/spring-tx-4.2.8.RELEASE.jar:/Users/<USER>/temp/spring-data-shell/repository/org/springframework/spring-beans/4.2.8.RELEASE/spring-beans-4.2.8.RELEASE.jar:/Users/<USER>/temp/spring-data-shell/repository/org/springframework/spring-instrument/4.2.8.RELEASE/spring-instrument-4.2.8.RELEASE.jar:/Users/<USER>/temp/spring-data-shell/repository/org/springframework/spring-core/4.2.8.RELEASE/spring-core-4.2.8.RELEASE.jar:/Users/<USER>/temp/spring-data-shell/repository/org/aspectj/aspectjrt/1.8.9/aspectjrt-1.8.9.jar:/Users/<USER>/temp/spring-data-shell/repository/org/aspectj/aspectjweaver/1.8.9/aspectjweaver-1.8.9.jar:/Users/<USER>/temp/spring-data-shell/repository/org/springframework/spring-aspects/4.2.8.RELEASE/spring-aspects-4.2.8.RELEASE.jar:/Users/<USER>/temp/spring-data-shell/repository/org/hsqldb/hsqldb/2.2.8/hsqldb-2.2.8.jar:/Users/<USER>/temp/spring-data-shell/repository/joda-time/joda-time/2.9.4/joda-time-2.9.4.jar:/Users/<USER>/temp/spring-data-shell/repository/org/threeten/threetenbp/1.3.2/threetenbp-1.3.2.jar:/Users/<USER>/temp/spring-data-shell/repository/org/eclipse/persistence/org.eclipse.persistence.jpa/2.6.2/org.eclipse.persistence.jpa-2.6.2.jar:/Users/<USER>/temp/spring-data-shell/repository/org/hibernate/hibernate-entitymanager/3.6.10.Final/hibernate-entitymanager-3.6.10.Final.jar:/Users/<USER>/temp/spring-data-shell/repository/org/apache/openjpa/openjpa-persistence-jdbc/2.4.1/openjpa-persistence-jdbc-2.4.1.jar:/Users/<USER>/temp/spring-data-shell/repository/com/querydsl/querydsl-apt/4.1.4/querydsl-apt-4.1.4.jar:/Users/<USER>/temp/spring-data-shell/repository/com/querydsl/querydsl-jpa/4.1.4/querydsl-jpa-4.1.4.jar:/Users/<USER>/temp/spring-data-shell/repository/javax/enterprise/cdi-api/1.0/cdi-api-1.0.jar:/Users/<USER>/temp/spring-data-shell/repository/javax/el/el-api/1.0/el-api-1.0.jar:/Users/<USER>/temp/spring-data-shell/repository/org/apache/openwebbeans/test/cditest-owb/1.2.8/cditest-owb-1.2.8.jar:/Users/<USER>/temp/spring-data-shell/repository/javax/servlet/javax.servlet-api/3.0.1/javax.servlet-api-3.0.1.jar:/Users/<USER>/temp/spring-data-shell/repository/com/google/guava/guava/17.0/guava-17.0.jar:/Users/<USER>/temp/spring-data-shell/repository/org/hamcrest/hamcrest-library/1.3/hamcrest-library-1.3.jar:/Users/<USER>/temp/spring-data-shell/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/temp/spring-data-shell/repository/junit/junit/4.12/junit-4.12.jar:/Users/<USER>/temp/spring-data-shell/repository/org/mockito/mockito-core/1.10.19/mockito-core-1.10.19.jar:/Users/<USER>/temp/spring-data-shell/repository/org/springframework/spring-test/4.2.8.RELEASE/spring-test-4.2.8.RELEASE.jar:/Users/<USER>/temp/spring-data-shell/repository/org/slf4j/slf4j-api/1.7.21/slf4j-api-1.7.21.jar:/Users/<USER>/temp/spring-data-shell/repository/org/slf4j/jcl-over-slf4j/1.7.21/jcl-over-slf4j-1.7.21.jar:/Users/<USER>/temp/spring-data-shell/repository/ch/qos/logback/logback-classic/1.1.7/logback-classic-1.1.7.jar:/Users/<USER>/temp/spring-data-shell/repository/org/projectlombok/lombok/1.16.10/lombok-1.16.10.jar:/Users/<USER>/temp/spring-data-shell/repository/org/springframework/spring-jdbc/4.2.8.RELEASE/spring-jdbc-4.2.8.RELEASE.jar:/Users/<USER>/temp/spring-data-shell/repository/org/springframework/spring-expression/4.2.8.RELEASE/spring-expression-4.2.8.RELEASE.jar:/Users/<USER>/temp/spring-data-shell/repository/aopalliance/aopalliance/1.0/aopalliance-1.0.jar:/Users/<USER>/temp/spring-data-shell/repository/org/eclipse/persistence/javax.persistence/2.1.1/javax.persistence-2.1.1.jar:/Users/<USER>/temp/spring-data-shell/repository/org/eclipse/persistence/org.eclipse.persistence.asm/2.6.2/org.eclipse.persistence.asm-2.6.2.jar:/Users/<USER>/temp/spring-data-shell/repository/org/eclipse/persistence/org.eclipse.persistence.antlr/2.6.2/org.eclipse.persistence.antlr-2.6.2.jar:/Users/<USER>/temp/spring-data-shell/repository/org/glassfish/javax.json/1.0.4/javax.json-1.0.4.jar:/Users/<USER>/temp/spring-data-shell/repository/org/eclipse/persistence/org.eclipse.persistence.jpa.jpql/2.6.2/org.eclipse.persistence.jpa.jpql-2.6.2.jar:/Users/<USER>/temp/spring-data-shell/repository/org/eclipse/persistence/org.eclipse.persistence.core/2.6.2/org.eclipse.persistence.core-2.6.2.jar:/Users/<USER>/temp/spring-data-shell/repository/org/hibernate/hibernate-core/3.6.10.Final/hibernate-core-3.6.10.Final.jar:/Users/<USER>/temp/spring-data-shell/repository/antlr/antlr/2.7.6/antlr-2.7.6.jar:/Users/<USER>/temp/spring-data-shell/repository/commons-collections/commons-collections/3.1/commons-collections-3.1.jar:/Users/<USER>/temp/spring-data-shell/repository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar:/Users/<USER>/temp/spring-data-shell/repository/org/hibernate/hibernate-commons-annotations/3.2.0.Final/hibernate-commons-annotations-3.2.0.Final.jar:/Users/<USER>/temp/spring-data-shell/repository/javax/transaction/jta/1.1/jta-1.1.jar:/Users/<USER>/temp/spring-data-shell/repository/cglib/cglib/2.2/cglib-2.2.jar:/Users/<USER>/temp/spring-data-shell/repository/asm/asm/3.1/asm-3.1.jar:/Users/<USER>/temp/spring-data-shell/repository/javassist/javassist/3.12.0.GA/javassist-3.12.0.GA.jar:/Users/<USER>/temp/spring-data-shell/repository/org/hibernate/javax/persistence/hibernate-jpa-2.0-api/1.0.1.Final/hibernate-jpa-2.0-api-1.0.1.Final.jar:/Users/<USER>/temp/spring-data-shell/repository/org/apache/openjpa/openjpa-jdbc/2.4.1/openjpa-jdbc-2.4.1.jar:/Users/<USER>/temp/spring-data-shell/repository/org/apache/openjpa/openjpa-kernel/2.4.1/openjpa-kernel-2.4.1.jar:/Users/<USER>/temp/spring-data-shell/repository/org/apache/openjpa/openjpa-lib/2.4.1/openjpa-lib-2.4.1.jar:/Users/<USER>/temp/spring-data-shell/repository/commons-lang/commons-lang/2.4/commons-lang-2.4.jar:/Users/<USER>/temp/spring-data-shell/repository/net/sourceforge/serp/serp/1.15.1/serp-1.15.1.jar:/Users/<USER>/temp/spring-data-shell/repository/org/apache/geronimo/specs/geronimo-jms_1.1_spec/1.1.1/geronimo-jms_1.1_spec-1.1.1.jar:/Users/<USER>/temp/spring-data-shell/repository/org/apache/geronimo/specs/geronimo-jta_1.1_spec/1.1.1/geronimo-jta_1.1_spec-1.1.1.jar:/Users/<USER>/temp/spring-data-shell/repository/commons-pool/commons-pool/1.5.4/commons-pool-1.5.4.jar:/Users/<USER>/temp/spring-data-shell/repository/org/apache/openjpa/openjpa-persistence/2.4.1/openjpa-persistence-2.4.1.jar:/Users/<USER>/temp/spring-data-shell/repository/org/apache/geronimo/specs/geronimo-jpa_2.0_spec/1.1/geronimo-jpa_2.0_spec-1.1.jar:/Users/<USER>/temp/spring-data-shell/repository/com/querydsl/querydsl-codegen/4.1.4/querydsl-codegen-4.1.4.jar:/Users/<USER>/temp/spring-data-shell/repository/com/mysema/codegen/codegen/0.6.8/codegen-0.6.8.jar:/Users/<USER>/temp/spring-data-shell/repository/org/eclipse/jdt/core/compiler/ecj/4.3.1/ecj-4.3.1.jar:/Users/<USER>/temp/spring-data-shell/repository/org/reflections/reflections/0.9.9/reflections-0.9.9.jar:/Users/<USER>/temp/spring-data-shell/repository/org/javassist/javassist/3.18.2-GA/javassist-3.18.2-GA.jar:/Users/<USER>/temp/spring-data-shell/repository/com/google/code/findbugs/annotations/2.0.1/annotations-2.0.1.jar:/Users/<USER>/temp/spring-data-shell/repository/javax/jdo/jdo-api/3.0.1/jdo-api-3.0.1.jar:/Users/<USER>/temp/spring-data-shell/repository/com/querydsl/querydsl-core/4.1.4/querydsl-core-4.1.4.jar:/Users/<USER>/temp/spring-data-shell/repository/com/google/code/findbugs/jsr305/1.3.9/jsr305-1.3.9.jar:/Users/<USER>/temp/spring-data-shell/repository/com/mysema/commons/mysema-commons-lang/0.2.4/mysema-commons-lang-0.2.4.jar:/Users/<USER>/temp/spring-data-shell/repository/com/infradna/tool/bridge-method-annotation/1.13/bridge-method-annotation-1.13.jar:/Users/<USER>/temp/spring-data-shell/repository/org/jboss/interceptor/jboss-interceptor-api/1.1/jboss-interceptor-api-1.1.jar:/Users/<USER>/temp/spring-data-shell/repository/javax/annotation/jsr250-api/1.0/jsr250-api-1.0.jar:/Users/<USER>/temp/spring-data-shell/repository/javax/inject/javax.inject/1/javax.inject-1.jar:/Users/<USER>/temp/spring-data-shell/repository/org/apache/xbean/xbean-asm5-shaded/4.1/xbean-asm5-shaded-4.1.jar:/Users/<USER>/temp/spring-data-shell/workspace/spring-data-jpa/target/classes
-aspectpath
/Users/<USER>/temp/spring-data-shell/repository/org/springframework/spring-aspects/4.2.8.RELEASE/spring-aspects-4.2.8.RELEASE.jar
-xmlConfigured
/Users/<USER>/temp/spring-data-shell/workspace/spring-data-jpa/aop.xml
-d
/Users/<USER>/temp/spring-data-shell/workspace/spring-data-jpa/target/classes
/Users/<USER>/temp/spring-data-shell/workspace/spring-data-jpa/src/main/java/org/springframework/data/jpa/domain/support/AuditingEntityListener.java
