<?xml version="1.0" encoding="UTF-8" standalone="no"?>

<xsd:schema xmlns="http://www.springframework.org/schema/tx"
		xmlns:xsd="http://www.w3.org/2001/XMLSchema"
		xmlns:beans="http://www.springframework.org/schema/beans"
		xmlns:tool="http://www.springframework.org/schema/tool"
		targetNamespace="http://www.springframework.org/schema/tx"
		elementFormDefault="qualified"
		attributeFormDefault="unqualified">

	<xsd:import namespace="http://www.springframework.org/schema/beans" schemaLocation="http://www.springframework.org/schema/beans/spring-beans-3.1.xsd"/>
	<xsd:import namespace="http://www.springframework.org/schema/tool" schemaLocation="http://www.springframework.org/schema/tool/spring-tool-3.1.xsd"/>

	<xsd:annotation>
		<xsd:documentation><![CDATA[
	Defines the elements used in the Spring Framework's declarative
	transaction management infrastructure.
		]]></xsd:documentation>
	</xsd:annotation>

	<xsd:element name="advice">
		<xsd:complexType>
			<xsd:annotation>
				<xsd:documentation source="java:org.springframework.transaction.interceptor.TransactionInterceptor"><![CDATA[
	Defines the transactional semantics of the AOP advice that is to be
	executed.

	That is, this advice element is where the transactional semantics of
	any	number of methods are defined (where transactional semantics
	includes the propagation settings, the isolation level, the rollback
	rules, and suchlike).
				]]></xsd:documentation>
				<xsd:appinfo>
					<tool:annotation>
						<tool:exports type="org.springframework.transaction.interceptor.TransactionInterceptor"/>
					</tool:annotation>
				</xsd:appinfo>
			</xsd:annotation>
			<xsd:complexContent>
				<xsd:extension base="beans:identifiedType">
					<xsd:sequence>
						<xsd:element name="attributes" type="attributesType" minOccurs="0" maxOccurs="1"/>
					</xsd:sequence>
					<xsd:attribute name="transaction-manager" type="xsd:string" default="transactionManager">
						<xsd:annotation>
							<xsd:documentation source="java:org.springframework.transaction.PlatformTransactionManager"><![CDATA[
	The bean name of the PlatformTransactionManager that is to be used
	to drive transactions.

	This attribute is not required, and only needs to be specified
	explicitly if the bean name of the desired PlatformTransactionManager
	is not 'transactionManager'.
							]]></xsd:documentation>
							<xsd:appinfo>
								<tool:annotation kind="ref">
									<tool:expected-type type="org.springframework.transaction.PlatformTransactionManager"/>
								</tool:annotation>
							</xsd:appinfo>
						</xsd:annotation>
					</xsd:attribute>
				</xsd:extension>
			</xsd:complexContent>
		</xsd:complexType>
	</xsd:element>

	<xsd:element name="annotation-driven">
		<xsd:complexType>
			<xsd:annotation>
				<xsd:documentation source="java:org.springframework.transaction.annotation.AnnotationTransactionAttributeSource"><![CDATA[
	Indicates that transaction configuration is defined by Java 5
	annotations on bean classes, and that proxies are automatically
	to be created for the relevant annotated beans.

	The default annotations supported are Spring's @Transactional
	and EJB3's @TransactionAttribute (if available).

	Transaction semantics such as propagation settings, the isolation level,
	the rollback rules, etc are all defined in the annotation metadata.

	See org.springframework.transaction.annotation.EnableTransactionManagement Javadoc
	for information on code-based alternatives to this XML element.
				]]></xsd:documentation>
			</xsd:annotation>
			<xsd:attribute name="transaction-manager" type="xsd:string" default="transactionManager">
				<xsd:annotation>
					<xsd:documentation source="java:org.springframework.transaction.PlatformTransactionManager"><![CDATA[
	The bean name of the PlatformTransactionManager that is to be used
	to drive transactions.

	This attribute is not required, and only needs to be specified
	explicitly if the bean name of the desired PlatformTransactionManager
	is not 'transactionManager'.
					]]></xsd:documentation>
					<xsd:appinfo>
						<tool:annotation kind="ref">
							<tool:expected-type type="org.springframework.transaction.PlatformTransactionManager"/>
						</tool:annotation>
					</xsd:appinfo>
				</xsd:annotation>
			</xsd:attribute>
			<xsd:attribute name="mode" default="proxy">
				<xsd:annotation>
					<xsd:documentation><![CDATA[
	Should annotated beans be proxied using Spring's AOP framework,
	or should they rather be weaved with an AspectJ transaction aspect?

	AspectJ weaving requires spring-aspects.jar on the classpath,
	as well as load-time weaving (or compile-time weaving) enabled.

	Note: The weaving-based aspect requires the @Transactional annotation to be
	defined on the concrete class. Annotations in interfaces will not work
	in that case (they will rather only work with interface-based proxies)!
					]]></xsd:documentation>
				</xsd:annotation>
				<xsd:simpleType>
					<xsd:restriction base="xsd:string">
						<xsd:enumeration value="proxy"/>
						<xsd:enumeration value="aspectj"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:attribute>
			<xsd:attribute name="proxy-target-class" type="xsd:boolean" default="false">
				<xsd:annotation>
					<xsd:documentation><![CDATA[
	Are class-based (CGLIB) proxies to be created? By default, standard
	Java interface-based proxies are created.

	Note: Class-based proxies require the @Transactional annotation to be
	defined on the concrete class. Annotations in interfaces will not work
	in that case (they will rather only work with interface-based proxies)!
					]]></xsd:documentation>
				</xsd:annotation>
			</xsd:attribute>
			<xsd:attribute name="order" type="xsd:int">
				<xsd:annotation>
					<xsd:documentation source="java:org.springframework.core.Ordered"><![CDATA[
	Controls the ordering of the execution of the transaction advisor
	when multiple advice executes at a specific joinpoint.
					]]></xsd:documentation>
				</xsd:annotation>
			</xsd:attribute>
		</xsd:complexType>
	</xsd:element>

	<xsd:element name="jta-transaction-manager">
		<xsd:annotation>
			<xsd:documentation><![CDATA[
	Creates a default JtaTransactionManager bean with name "transactionManager",
	matching the default bean name expected by the "annotation-driven" tag.
	Automatically detects WebLogic, WebSphere and OC4J: creating a WebLogicJtaTransactionManager,
	WebSphereUowTransactionManager or OC4JJtaTransactionManager, respectively.

	For customization needs, consider defining a JtaTransactionManager bean as a regular
	Spring bean definition with name "transactionManager", replacing this element.
			]]></xsd:documentation>
			<xsd:appinfo>
				<tool:annotation>
					<tool:exports type="org.springframework.transaction.jta.JtaTransactionManager"/>
				</tool:annotation>
			</xsd:appinfo>
		</xsd:annotation>
	</xsd:element>

	<xsd:complexType name="attributesType">
		<xsd:sequence>
			<xsd:element name="method" minOccurs="1" maxOccurs="unbounded">
				<xsd:complexType>
					<xsd:attribute name="name" type="xsd:string" use="required">
						<xsd:annotation>
							<xsd:documentation><![CDATA[
	The method name(s) with which the transaction attributes are to be
	associated. The wildcard (*) character can be used to associate the
	same transaction attribute settings with a number of methods; for
	example, 'get*', 'handle*', '*Order', 'on*Event', etc.
							]]></xsd:documentation>
						</xsd:annotation>
					</xsd:attribute>
					<xsd:attribute name="propagation" default="REQUIRED">
						<xsd:annotation>
							<xsd:documentation source="java:org.springframework.transaction.annotation.Propagation"><![CDATA[
	The transaction propagation behavior.
							]]></xsd:documentation>
						</xsd:annotation>
						<xsd:simpleType>
							<xsd:restriction base="xsd:string">
								<xsd:enumeration value="REQUIRED"/>
								<xsd:enumeration value="SUPPORTS"/>
								<xsd:enumeration value="MANDATORY"/>
								<xsd:enumeration value="REQUIRES_NEW"/>
								<xsd:enumeration value="NOT_SUPPORTED"/>
								<xsd:enumeration value="NEVER"/>
								<xsd:enumeration value="NESTED"/>
							</xsd:restriction>
						</xsd:simpleType>
					</xsd:attribute>
					<xsd:attribute name="isolation" default="DEFAULT">
						<xsd:annotation>
							<xsd:documentation source="java:org.springframework.transaction.annotation.Isolation"><![CDATA[
	The transaction isolation level.
							]]></xsd:documentation>
						</xsd:annotation>
						<xsd:simpleType>
							<xsd:restriction base="xsd:string">
								<xsd:enumeration value="DEFAULT"/>
								<xsd:enumeration value="READ_UNCOMMITTED"/>
								<xsd:enumeration value="READ_COMMITTED"/>
								<xsd:enumeration value="REPEATABLE_READ"/>
								<xsd:enumeration value="SERIALIZABLE"/>
							</xsd:restriction>
						</xsd:simpleType>
					</xsd:attribute>
					<xsd:attribute name="timeout" type="xsd:integer" default="-1">
						<xsd:annotation>
							<xsd:documentation><![CDATA[
	The transaction timeout value (in seconds).
							]]></xsd:documentation>
						</xsd:annotation>
					</xsd:attribute>
					<xsd:attribute name="read-only" type="xsd:boolean" default="false">
						<xsd:annotation>
							<xsd:documentation><![CDATA[
	Is this transaction read-only?
							]]></xsd:documentation>
						</xsd:annotation>
					</xsd:attribute>
					<xsd:attribute name="rollback-for" type="xsd:string">
						<xsd:annotation>
							<xsd:documentation><![CDATA[
	The Exception(s) that will trigger rollback; comma-delimited.
	For example, 'com.foo.MyBusinessException,ServletException'
							]]></xsd:documentation>
						</xsd:annotation>
					</xsd:attribute>
					<xsd:attribute name="no-rollback-for" type="xsd:string">
						<xsd:annotation>
							<xsd:documentation><![CDATA[
	The Exception(s) that will *not* trigger rollback; comma-delimited.
	For example, 'com.foo.MyBusinessException,ServletException'
							]]></xsd:documentation>
						</xsd:annotation>
					</xsd:attribute>
				</xsd:complexType>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>

</xsd:schema>
