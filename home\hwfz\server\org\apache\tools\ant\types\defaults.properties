# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
description=org.apache.tools.ant.types.Description
filterchain=org.apache.tools.ant.types.FilterChain
filterreader=org.apache.tools.ant.types.AntFilterReader
filterset=org.apache.tools.ant.types.FilterSet
mapper=org.apache.tools.ant.types.Mapper
redirector=org.apache.tools.ant.types.RedirectorElement
patternset=org.apache.tools.ant.types.PatternSet
regexp=org.apache.tools.ant.types.RegularExpression
substitution=org.apache.tools.ant.types.Substitution
xmlcatalog=org.apache.tools.ant.types.XMLCatalog
extensionSet=org.apache.tools.ant.taskdefs.optional.extension.ExtensionSet
extension=org.apache.tools.ant.taskdefs.optional.extension.ExtensionAdapter
selector=org.apache.tools.ant.types.selectors.SelectSelector
signedselector=org.apache.tools.ant.types.selectors.SignedSelector
scriptfilter=org.apache.tools.ant.types.optional.ScriptFilter
assertions=org.apache.tools.ant.types.Assertions
concatfilter=org.apache.tools.ant.filters.ConcatFilter
mavenrepository=org.apache.tools.ant.taskdefs.repository.MavenRepository
scriptselector=org.apache.tools.ant.types.optional.ScriptSelector
scriptmapper=org.apache.tools.ant.types.optional.ScriptMapper

# different filename mappers
identitymapper=org.apache.tools.ant.util.IdentityMapper
flattenmapper=org.apache.tools.ant.util.FlatFileNameMapper
globmapper=org.apache.tools.ant.util.GlobPatternMapper
mergemapper=org.apache.tools.ant.util.MergingMapper
regexpmapper=org.apache.tools.ant.util.RegexpPatternMapper
packagemapper=org.apache.tools.ant.util.PackageNameMapper
unpackagemapper=org.apache.tools.ant.util.UnPackageNameMapper
compositemapper=org.apache.tools.ant.util.CompositeMapper
chainedmapper=org.apache.tools.ant.util.ChainedMapper
filtermapper=org.apache.tools.ant.types.mappers.FilterMapper
firstmatchmapper=org.apache.tools.ant.util.FirstMatchMapper
cutdirsmapper=org.apache.tools.ant.types.mappers.CutDirsMapper

#this condition is in here because it is the sole
#condition defined in Ant1.6
#please add new conditions to oata.types.conditions/antlib.xml instead of
#here, to avoid namespace clash with things like selectors.
isfileselected=org.apache.tools.ant.taskdefs.condition.IsFileSelected
scriptcondition=org.apache.tools.ant.types.optional.ScriptCondition

#ResourceCollections:
dirset=org.apache.tools.ant.types.DirSet
filelist=org.apache.tools.ant.types.FileList
fileset=org.apache.tools.ant.types.FileSet
path=org.apache.tools.ant.types.Path
propertyset=org.apache.tools.ant.types.PropertySet
zipfileset=org.apache.tools.ant.types.ZipFileSet
classfileset=org.apache.tools.ant.types.optional.depend.ClassfileSet
libfileset=org.apache.tools.ant.taskdefs.optional.extension.LibFileSet
files=org.apache.tools.ant.types.resources.Files
restrict=org.apache.tools.ant.types.resources.Restrict
union=org.apache.tools.ant.types.resources.Union
difference=org.apache.tools.ant.types.resources.Difference
intersect=org.apache.tools.ant.types.resources.Intersect
sort=org.apache.tools.ant.types.resources.Sort
resources=org.apache.tools.ant.types.resources.Resources
allbutfirst=org.apache.tools.ant.types.resources.AllButFirst
allbutlast=org.apache.tools.ant.types.resources.AllButLast
first=org.apache.tools.ant.types.resources.First
last=org.apache.tools.ant.types.resources.Last
tarfileset=org.apache.tools.ant.types.TarFileSet
tokens=org.apache.tools.ant.types.resources.Tokens
mappedresources=org.apache.tools.ant.types.resources.MappedResourceCollection
archives=org.apache.tools.ant.types.resources.Archives
resourcelist=org.apache.tools.ant.types.resources.ResourceList

#Resources (single-element ResourceCollections):
resource=org.apache.tools.ant.types.Resource
file=org.apache.tools.ant.types.resources.FileResource
url=org.apache.tools.ant.types.resources.URLResource
string=org.apache.tools.ant.types.resources.StringResource
zipentry=org.apache.tools.ant.types.resources.ZipResource
propertyresource=org.apache.tools.ant.types.resources.PropertyResource
tarentry=org.apache.tools.ant.types.resources.TarResource
gzipresource=org.apache.tools.ant.types.resources.GZipResource
bzip2resource=org.apache.tools.ant.types.resources.BZip2Resource
javaresource=org.apache.tools.ant.types.resources.JavaResource
multirootfileset=org.apache.tools.ant.types.resources.MultiRootFileSet
javaconstant=org.apache.tools.ant.types.resources.JavaConstantResource

#tokenizer implementations
linetokenizer=org.apache.tools.ant.util.LineTokenizer
stringtokenizer=org.apache.tools.ant.util.StringTokenizer
filetokenizer=org.apache.tools.ant.util.FileTokenizer
