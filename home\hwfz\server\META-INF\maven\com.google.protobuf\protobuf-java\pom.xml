<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.google</groupId>
    <artifactId>google</artifactId>
    <version>1</version>
  </parent>
  <groupId>com.google.protobuf</groupId>
  <artifactId>protobuf-java</artifactId>
  <version>2.5.0</version>
  <packaging>bundle</packaging>
  <name>Protocol Buffer Java API</name>
  <description>
    Protocol Buffers are a way of encoding structured data in an efficient yet
    extensible format.
  </description>
  <inceptionYear>2008</inceptionYear>
  <url>http://code.google.com/p/protobuf</url>
  <licenses>
    <license>
      <name>New BSD license</name>
      <url>http://www.opensource.org/licenses/bsd-license.php</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <scm>
    <url>http://code.google.com/p/protobuf/source/browse</url>
    <connection>
      scm:svn:http://protobuf.googlecode.com/svn/trunk/
    </connection>
  </scm>
  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.4</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.easymock</groupId>
      <artifactId>easymock</artifactId>
      <version>2.2</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.easymock</groupId>
      <artifactId>easymockclassextension</artifactId>
      <version>2.2.1</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <source>1.5</source>
          <target>1.5</target>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <includes>
            <include>**/*Test.java</include>
          </includes>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-antrun-plugin</artifactId>
        <executions>
          <execution>
            <id>generate-sources</id>
            <phase>generate-sources</phase>
            <configuration>
              <tasks>
                <mkdir dir="target/generated-sources" />
                <exec executable="../src/protoc">
                  <arg value="--java_out=target/generated-sources" />
                  <arg value="--proto_path=../src" />
                  <arg value="../src/google/protobuf/descriptor.proto" />
                </exec>
              </tasks>
              <sourceRoot>target/generated-sources</sourceRoot>
            </configuration>
            <goals>
              <goal>run</goal>
            </goals>
          </execution>
          <execution>
            <id>generate-test-sources</id>
            <phase>generate-test-sources</phase>
            <configuration>
              <tasks>
                <mkdir dir="target/generated-test-sources" />
                <exec executable="../src/protoc">
                  <arg value="--java_out=target/generated-test-sources" />
                  <arg value="--proto_path=../src" />
                  <arg value="--proto_path=src/test/java" />
                  <arg value="../src/google/protobuf/unittest.proto" />
                  <arg value="../src/google/protobuf/unittest_import.proto" />
                  <arg value="../src/google/protobuf/unittest_import_public.proto" />
                  <arg value="../src/google/protobuf/unittest_mset.proto" />
                  <arg
                    value="src/test/java/com/google/protobuf/multiple_files_test.proto" />
                  <arg value="src/test/java/com/google/protobuf/nested_builders_test.proto" />
                  <arg value="src/test/java/com/google/protobuf/nested_extension.proto" />
                  <arg value="src/test/java/com/google/protobuf/nested_extension_lite.proto" />
                  <arg value="src/test/java/com/google/protobuf/non_nested_extension.proto" />
                  <arg value="src/test/java/com/google/protobuf/non_nested_extension_lite.proto" />
                  <arg value="src/test/java/com/google/protobuf/test_bad_identifiers.proto" />
                  <arg
                    value="../src/google/protobuf/unittest_optimize_for.proto" />
                  <arg
                    value="../src/google/protobuf/unittest_custom_options.proto" />
                  <arg value="../src/google/protobuf/unittest_lite.proto" />
                  <arg value="../src/google/protobuf/unittest_import_lite.proto" />
                  <arg value="../src/google/protobuf/unittest_import_public_lite.proto" />
                  <arg value="../src/google/protobuf/unittest_lite_imports_nonlite.proto" />
                  <arg value="../src/google/protobuf/unittest_enormous_descriptor.proto" />
                  <arg value="../src/google/protobuf/unittest_no_generic_services.proto" />
                </exec>
              </tasks>
              <testSourceRoot>target/generated-test-sources</testSourceRoot>
            </configuration>
            <goals>
              <goal>run</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <extensions>true</extensions>
        <configuration>
          <instructions>
            <Bundle-DocURL>http://code.google.com/p/protobuf</Bundle-DocURL>
            <Bundle-SymbolicName>com.google.protobuf</Bundle-SymbolicName>
            <Export-Package>com.google.protobuf;version=2.5.0</Export-Package>
          </instructions>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <profiles>
    <profile>
      <id>lite</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-compiler-plugin</artifactId>
            <configuration>
              <includes>
                <include>**/AbstractMessageLite.java</include>
                <include>**/ByteString.java</include>
                <include>**/CodedInputStream.java</include>
                <include>**/CodedOutputStream.java</include>
                <include>**/ExtensionRegistryLite.java</include>
                <include>**/FieldSet.java</include>
                <include>**/GeneratedMessageLite.java</include>
                <include>**/Internal.java</include>
                <include>**/InvalidProtocolBufferException.java</include>
                <include>**/LazyStringArrayList.java</include>
                <include>**/LazyStringList.java</include>
                <include>**/MessageLite.java</include>
                <include>**/MessageLiteOrBuilder.java</include>
                <include>**/SmallSortedMap.java</include>
                <include>**/UninitializedMessageException.java</include>
                <include>**/UnmodifiableLazyStringList.java</include>
                <include>**/WireFormat.java</include>
                <include>**/Parser.java</include>
                <include>**/AbstractParser.java</include>
                <include>**/BoundedByteString.java</include>
                <include>**/LiteralByteString.java</include>
                <include>**/RopeByteString.java</include>
                <include>**/Utf8.java</include>
                <include>**/LazyField.java</include>
              </includes>
              <testIncludes>
                <testInclude>**/LiteTest.java</testInclude>
                <testInclude>**/*Lite.java</testInclude>
              </testIncludes>
            </configuration>
          </plugin>
          <plugin>
            <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
              <includes>
                <include>**/LiteTest.java</include>
              </includes>
            </configuration>
          </plugin>
          <plugin>
            <artifactId>maven-jar-plugin</artifactId>
            <configuration>
              <classifier>lite</classifier>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
