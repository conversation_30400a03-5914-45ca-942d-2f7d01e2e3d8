<?xml version="1.0" encoding="utf-8" ?>
<xs:schema targetNamespace="http://www.w3.org/2005/Atom" elementFormDefault="qualified" 
	attributeFormDefault="unqualified"
	xmlns:atom="http://www.w3.org/2005/Atom" xmlns:xs="http://www.w3.org/2001/XMLSchema">
	<xs:annotation>
		<xs:documentation>
				This version of the Atom schema is based on version 1.0 of the format specifications,
				found here http://www.atomenabled.org/developers/syndication/atom-format-spec.php.
			</xs:documentation>
	</xs:annotation>
	<xs:import namespace="http://www.w3.org/XML/1998/namespace" schemaLocation="http://www.w3.org/2001/03/xml.xsd" />
	<xs:annotation>
		<xs:documentation>
			An Atom document may have two root elements, feed and entry, as defined in section 2.
		</xs:documentation>
	</xs:annotation>
	<xs:element name="feed" type="atom:feedType"/>
	<xs:element name="entry" type="atom:entryType"/>
	<xs:complexType name="textType" mixed="true">
		<xs:annotation>
			<xs:documentation>
				The Atom text construct is defined in section 3.1 of the format spec.
			</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:any namespace="http://www.w3.org/1999/xhtml" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="type" >
			<xs:simpleType>
				<xs:restriction base="xs:token">
					<xs:enumeration value="text"/>
					<xs:enumeration value="html"/>
					<xs:enumeration value="xhtml"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attributeGroup ref="atom:commonAttributes"/>
	</xs:complexType>
	<xs:complexType name="personType">
		<xs:annotation>
			<xs:documentation>
				The Atom person construct is defined in section 3.2 of the format spec.
			</xs:documentation>
		</xs:annotation>
		<xs:choice minOccurs="1" maxOccurs="unbounded">
			<xs:element name="name" type="xs:string" minOccurs="1" maxOccurs="1" />
			<xs:element name="uri" type="atom:uriType" minOccurs="0" maxOccurs="1" />
			<xs:element name="email" type="atom:emailType" minOccurs="0" maxOccurs="1" />
			<xs:any namespace="##other"/>
		</xs:choice>
		<xs:attributeGroup ref="atom:commonAttributes"/>
	</xs:complexType>
	<xs:simpleType name="emailType">
		<xs:annotation>
			<xs:documentation>
				Schema definition for an email address.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:normalizedString">
			<xs:pattern value="\w+@(\w+\.)+\w+" />
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="feedType">
		<xs:annotation>
			<xs:documentation>
				The Atom feed construct is defined in section 4.1.1 of the format spec.
			</xs:documentation>
		</xs:annotation>
		<xs:choice minOccurs="3" maxOccurs="unbounded">
			<xs:element name="author" type="atom:personType" minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="category" type="atom:categoryType" minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="contributor" type="atom:personType" minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="generator" type="atom:generatorType" minOccurs="0" maxOccurs="1" />
			<xs:element name="icon" type="atom:iconType" minOccurs="0" maxOccurs="1" />
			<xs:element name="id" type="atom:idType" minOccurs="1" maxOccurs="1" />
			<xs:element name="link" type="atom:linkType" minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="logo" type="atom:logoType" minOccurs="0" maxOccurs="1" />
			<xs:element name="rights" type="atom:textType" minOccurs="0" maxOccurs="1" />
			<xs:element name="subtitle" type="atom:textType" minOccurs="0" maxOccurs="1" />
			<xs:element name="title" type="atom:textType" minOccurs="1" maxOccurs="1" />
			<xs:element name="updated" type="atom:dateTimeType" minOccurs="1" maxOccurs="1" />
			<xs:element name="entry" type="atom:entryType" minOccurs="0" maxOccurs="unbounded" />
			<xs:any namespace="##other" minOccurs="0" maxOccurs="unbounded"/>
		</xs:choice>
		<xs:attributeGroup ref="atom:commonAttributes"/>
	</xs:complexType>
	<xs:complexType name="entryType">
		<xs:annotation>
			<xs:documentation>
				The Atom entry construct is defined in section 4.1.2 of the format spec.
			</xs:documentation>
		</xs:annotation>
		<xs:choice maxOccurs="unbounded">
			<xs:element name="author" type="atom:personType" minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="category" type="atom:categoryType" minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="content" type="atom:contentType" minOccurs="0" maxOccurs="1" />
			<xs:element name="contributor" type="atom:personType" minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="id" type="atom:idType" minOccurs="1" maxOccurs="1" />
			<xs:element name="link" type="atom:linkType" minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="published" type="atom:dateTimeType" minOccurs="0" maxOccurs="1" />
			<xs:element name="rights" type="atom:textType" minOccurs="0" maxOccurs="1" />
			<xs:element name="source" type="atom:textType" minOccurs="0" maxOccurs="1" />
			<xs:element name="summary" type="atom:textType" minOccurs="0" maxOccurs="1" />
			<xs:element name="title" type="atom:textType" minOccurs="1" maxOccurs="1" />
			<xs:element name="updated" type="atom:dateTimeType" minOccurs="1" maxOccurs="1" />
			<xs:any namespace="##other" minOccurs="0" maxOccurs="unbounded"/>
		</xs:choice>
		<xs:attributeGroup ref="atom:commonAttributes"/>
	</xs:complexType>
	<xs:complexType name="contentType" mixed="true">
		<xs:annotation>
			<xs:documentation>
				The Atom content construct is defined in section 4.1.3 of the format spec.
			</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:any namespace="##other" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
		<xs:attribute name="type" type="xs:string"/>
		<xs:attribute name="src" type="xs:anyURI"/>
		<xs:attributeGroup ref="atom:commonAttributes"/>
	</xs:complexType>
	<xs:complexType name="categoryType">
		<xs:annotation>
			<xs:documentation>
				The Atom cagegory construct is defined in section 4.2.2 of the format spec.
			</xs:documentation>
		</xs:annotation>
		<xs:attribute name="term" type="xs:string" use="required"/>
		<xs:attribute name="scheme" type="xs:anyURI" use="optional"/>
		<xs:attribute name="label" type="xs:string" use="optional"/>
		<xs:attributeGroup ref="atom:commonAttributes" />
	</xs:complexType>
	<xs:complexType name="generatorType">
		<xs:annotation>
			<xs:documentation>
				The Atom generator element is defined in section 4.2.4 of the format spec.
			</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:extension base="xs:string">
				<xs:attribute name="uri" use="optional" type="xs:anyURI" />
				<xs:attribute name="version" use="optional" type="xs:string" />
				<xs:attributeGroup ref="atom:commonAttributes"/>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:complexType name="iconType">
		<xs:annotation>
			<xs:documentation>
				The Atom icon construct is defined in section 4.2.5 of the format spec.
			</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:extension base="xs:anyURI">
				<xs:attributeGroup ref="atom:commonAttributes"/>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:complexType name="idType">
		<xs:annotation>
			<xs:documentation>
				The Atom id construct is defined in section 4.2.6 of the format spec.
			</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:extension base="xs:anyURI">
				<xs:attributeGroup ref="atom:commonAttributes"/>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:complexType name="linkType" mixed="true">
		<xs:annotation>
			<xs:documentation>
				The Atom link construct is defined in section 3.4 of the format spec.
			</xs:documentation>
		</xs:annotation>
		<xs:attribute name="href" use="required" type="xs:anyURI" />
		<xs:attribute name="rel" type="xs:string" use="optional"/>
		<xs:attribute name="type" use="optional" type="xs:string" />
		<xs:attribute name="hreflang" use="optional" type="xs:NMTOKEN" />
		<xs:attribute name="title" use="optional" type="xs:string" />
		<xs:attribute name="length" use="optional" type="xs:positiveInteger" />
		<xs:attributeGroup ref="atom:commonAttributes"/>
	</xs:complexType>
	<xs:complexType name="logoType">
		<xs:annotation>
			<xs:documentation>
				The Atom logo construct is defined in section 4.2.8 of the format spec.
			</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:extension base="xs:anyURI">
				<xs:attributeGroup ref="atom:commonAttributes"/>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:complexType name="sourceType">
		<xs:annotation>
			<xs:documentation>
				The Atom source construct is defined in section 4.2.11 of the format spec.
			</xs:documentation>
		</xs:annotation>
		<xs:choice maxOccurs="unbounded">
			<xs:element name="author" type="atom:personType" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="category" type="atom:categoryType" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="contributor" type="atom:personType" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="generator" type="atom:generatorType" minOccurs="0" maxOccurs="1"/>
			<xs:element name="icon" type="atom:iconType" minOccurs="0" maxOccurs="1"/>
			<xs:element name="id" type="atom:idType" minOccurs="0" maxOccurs="1"/>
			<xs:element name="link" type="atom:linkType" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="logo" type="atom:logoType" minOccurs="0" maxOccurs="1"/>
			<xs:element name="rights" type="atom:textType" minOccurs="0" maxOccurs="1"/>
			<xs:element name="subtitle" type="atom:textType" minOccurs="0" maxOccurs="1"/>
			<xs:element name="title" type="atom:textType" minOccurs="0" maxOccurs="1"/>
			<xs:element name="updated" type="atom:dateTimeType" minOccurs="0" maxOccurs="1"/>
			<xs:any namespace="##other" minOccurs="0" maxOccurs="unbounded"/>
		</xs:choice>
		<xs:attributeGroup ref="atom:commonAttributes"/>
	</xs:complexType>
	<xs:complexType name="uriType">
		<xs:simpleContent>
			<xs:extension base="xs:anyURI">
				<xs:attributeGroup ref="atom:commonAttributes"/>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:complexType name="dateTimeType">
		<xs:simpleContent>
			<xs:extension base="xs:dateTime">
				<xs:attributeGroup ref="atom:commonAttributes"/>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:attributeGroup name="commonAttributes">
		<xs:attribute ref="xml:base" />
		<xs:attribute ref="xml:lang" />
		<xs:anyAttribute namespace="##other"/>
	</xs:attributeGroup>
</xs:schema>