<?xml version="1.0"?>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<antlib>
  <typedef name="name"
    classname="org.apache.tools.ant.types.resources.comparators.Name" />
  <typedef name="size"
    classname="org.apache.tools.ant.types.resources.comparators.Size" />
  <typedef name="date"
    classname="org.apache.tools.ant.types.resources.comparators.Date" />
  <typedef name="exists"
    classname="org.apache.tools.ant.types.resources.comparators.Exists" />
  <typedef name="type"
    classname="org.apache.tools.ant.types.resources.comparators.Type" />
  <typedef name="content"
    classname="org.apache.tools.ant.types.resources.comparators.Content" />
  <typedef name="reverse"
    classname="org.apache.tools.ant.types.resources.comparators.Reverse" />
</antlib>
