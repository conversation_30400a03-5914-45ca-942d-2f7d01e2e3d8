<?xml version="1.0" encoding="UTF-8" standalone="no"?>

<xsd:schema xmlns="http://www.springframework.org/schema/aop"
			xmlns:xsd="http://www.w3.org/2001/XMLSchema"
			xmlns:tool="http://www.springframework.org/schema/tool"
			targetNamespace="http://www.springframework.org/schema/aop"
			elementFormDefault="qualified"
			attributeFormDefault="unqualified">

	<xsd:import namespace="http://www.springframework.org/schema/beans" schemaLocation="http://www.springframework.org/schema/beans/spring-beans-3.1.xsd"/>
	<xsd:import namespace="http://www.springframework.org/schema/tool" schemaLocation="http://www.springframework.org/schema/tool/spring-tool-3.1.xsd"/>

	<xsd:annotation>
		<xsd:documentation><![CDATA[
	Defines the configuration elements for the Spring Framework's AOP support.
		]]></xsd:documentation>
	</xsd:annotation>

	<xsd:element name="config">
		<xsd:annotation>
			<xsd:documentation><![CDATA[
	A section (compartmentalization) of AOP-specific configuration (including
	aspects, pointcuts, etc).
			]]></xsd:documentation>
		</xsd:annotation>
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="pointcut" type="pointcutType" minOccurs="0" maxOccurs="unbounded">
					<xsd:annotation>
						<xsd:documentation><![CDATA[
	A named pointcut definition.
						]]></xsd:documentation>
					</xsd:annotation>
				</xsd:element>
				<xsd:element name="advisor" type="advisorType" minOccurs="0" maxOccurs="unbounded">
					<xsd:annotation>
						<xsd:documentation source="java:org.springframework.aop.Advisor"><![CDATA[
	A named advisor definition.
						]]></xsd:documentation>
					</xsd:annotation>
				</xsd:element>
				<xsd:element name="aspect" type="aspectType" minOccurs="0" maxOccurs="unbounded">
					<xsd:annotation>
						<xsd:documentation><![CDATA[
	A named aspect definition.
						]]></xsd:documentation>
					</xsd:annotation>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="proxy-target-class" type="xsd:boolean" default="false">
				<xsd:annotation>
					<xsd:documentation><![CDATA[
	Are class-based (CGLIB) proxies to be created? By default, standard
	Java interface-based proxies are created.
					]]></xsd:documentation>
				</xsd:annotation>
			</xsd:attribute>
			<xsd:attribute name="expose-proxy" type="xsd:boolean" default="false">
				<xsd:annotation>
					<xsd:documentation><![CDATA[
	Indicate that the proxy should be exposed by the AOP framework as a
	ThreadLocal for retrieval via the AopContext class. Off by default,
	i.e. no guarantees that AopContext access will work.
					]]></xsd:documentation>
				</xsd:annotation>
			</xsd:attribute>
		</xsd:complexType>
	</xsd:element>

	<xsd:element name="aspectj-autoproxy">
		<xsd:annotation>
			<xsd:documentation source="java:org.springframework.aop.aspectj.annotation.AnnotationAwareAspectJAutoProxyCreator"><![CDATA[
	Enables the use of the @AspectJ style of Spring AOP.

	See org.springframework.context.annotation.EnableAspectJAutoProxy Javadoc
	for information on code-based alternatives to this XML element.
			]]></xsd:documentation>
		</xsd:annotation>
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="include" type="includeType" minOccurs="0" maxOccurs="unbounded">
					<xsd:annotation>
						<xsd:documentation><![CDATA[
	Indicates that only @AspectJ beans with names matched by the (regex)
	pattern will be considered as defining aspects to use for Spring autoproxying.
						]]></xsd:documentation>
					</xsd:annotation>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="proxy-target-class" type="xsd:boolean" default="false">
				<xsd:annotation>
					<xsd:documentation><![CDATA[
	Are class-based (CGLIB) proxies to be created? By default, standard
	Java interface-based proxies are created.
					]]></xsd:documentation>
				</xsd:annotation>
			</xsd:attribute>
			<xsd:attribute name="expose-proxy" type="xsd:boolean" default="false">
				<xsd:annotation>
					<xsd:documentation><![CDATA[
	Indicate that the proxy should be exposed by the AOP framework as a
	ThreadLocal for retrieval via the AopContext class. Off by default,
	i.e. no guarantees that AopContext access will work.
					]]></xsd:documentation>
				</xsd:annotation>
			</xsd:attribute>
		</xsd:complexType>
	</xsd:element>

	<xsd:element name="scoped-proxy">
		<xsd:complexType>
			<xsd:annotation>
				<xsd:documentation source="java:org.springframework.aop.scope.ScopedProxyFactoryBean"><![CDATA[
	Marks a bean definition as being a scoped proxy.

	A bean marked as such will be exposed via a proxy, with the 'real'
	bean instance being retrieved from some other source (such as a
	HttpSession) as and when required.
				]]></xsd:documentation>
			</xsd:annotation>
			<xsd:attribute name="proxy-target-class" type="xsd:boolean" default="true">
				<xsd:annotation>
					<xsd:documentation><![CDATA[
	Are class-based (CGLIB) proxies to be created? This is the default; in order to
	switch to standard Java interface-based proxies, turn this flag to "false".
					]]></xsd:documentation>
				</xsd:annotation>
			</xsd:attribute>
		</xsd:complexType>
	</xsd:element>

	<xsd:complexType name="aspectType">
		<xsd:choice minOccurs="0" maxOccurs="unbounded">
			<xsd:element name="pointcut" type="pointcutType">
				<xsd:annotation>
					<xsd:documentation><![CDATA[
	A named pointcut definition.
					]]></xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="declare-parents" type="declareParentsType">
				<xsd:annotation>
					<xsd:documentation><![CDATA[
	Allows this aspect to introduce additional interfaces that the advised
	object will transparently implement.
					]]></xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="before" type="basicAdviceType">
				<xsd:annotation>
					<xsd:documentation><![CDATA[
	A before advice definition.
					]]></xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="after" type="basicAdviceType">
				<xsd:annotation>
					<xsd:documentation><![CDATA[
	An after advice definition.
					]]></xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="after-returning" type="afterReturningAdviceType">
				<xsd:annotation>
					<xsd:documentation><![CDATA[
	An after-returning advice definition.
					]]></xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="after-throwing" type="afterThrowingAdviceType">
				<xsd:annotation>
					<xsd:documentation><![CDATA[
	An after-throwing advice definition.
					]]></xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="around" type="basicAdviceType">
				<xsd:annotation>
					<xsd:documentation><![CDATA[
	An around advice definition.
					]]></xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:choice>
		<xsd:attribute name="id" type="xsd:string">
			<xsd:annotation>
				<xsd:documentation><![CDATA[
	The unique identifier for an aspect.
				]]></xsd:documentation>
			</xsd:annotation>
		</xsd:attribute>
		<xsd:attribute name="ref" type="xsd:string">
			<xsd:annotation>
				<xsd:documentation><![CDATA[
	The name of the (backing) bean that encapsulates the aspect.
				]]></xsd:documentation>
			</xsd:annotation>
		</xsd:attribute>
		<xsd:attribute name="order" type="xsd:int">
			<xsd:annotation>
				<xsd:documentation source="java:org.springframework.core.Ordered"><![CDATA[
	Controls the ordering of the execution of this aspect when multiple
	advice executes at a specific joinpoint.
				]]></xsd:documentation>
			</xsd:annotation>
		</xsd:attribute>
	</xsd:complexType>

	<xsd:complexType name="includeType">
		<xsd:attribute name="name" type="xsd:string">
			<xsd:annotation>
				<xsd:documentation source="java:java.util.regex.Pattern"><![CDATA[
	The regular expression defining which beans are to be included in the
	list of @AspectJ beans; beans with names matched by the pattern will
	be included.
				]]></xsd:documentation>
			</xsd:annotation>
		</xsd:attribute>
	</xsd:complexType>

	<xsd:complexType name="pointcutType">
		<xsd:annotation>
			<xsd:appinfo>
				<tool:annotation>
					<tool:exports type="org.springframework.aop.Pointcut"/>
				</tool:annotation>
			</xsd:appinfo>
		</xsd:annotation>
		<xsd:attribute name="id" type="xsd:string" use="required">
			<xsd:annotation>
				<xsd:documentation><![CDATA[
	The unique identifier for a pointcut.
				]]></xsd:documentation>
			</xsd:annotation>
		</xsd:attribute>
		<xsd:attribute name="expression" use="required" type="xsd:string">
			<xsd:annotation>
				<xsd:documentation><![CDATA[
	The pointcut expression.

	For example : 'execution(* com.xyz.myapp.service.*.*(..))'
				]]></xsd:documentation>
			</xsd:annotation>
		</xsd:attribute>
	</xsd:complexType>

	<xsd:complexType name="declareParentsType">
		<xsd:attribute name="types-matching" type="xsd:string" use="required">
			<xsd:annotation>
				<xsd:documentation source="java:org.springframework.aop.aspectj.TypePatternClassFilter"><![CDATA[
	The AspectJ type expression that defines what types (classes) the
	introduction is restricted to.

	An example would be 'org.springframework.beans.ITestBean+'.
				]]></xsd:documentation>
			</xsd:annotation>
		</xsd:attribute>
		<xsd:attribute name="implement-interface" type="xsd:string" use="required">
			<xsd:annotation>
				<xsd:documentation source="java:java.lang.Class"><![CDATA[
	The fully qualified name of the interface that will be introduced.
				]]></xsd:documentation>
			</xsd:annotation>
		</xsd:attribute>
		<xsd:attribute name="default-impl" type="xsd:string">
			<xsd:annotation>
				<xsd:documentation source="java:java.lang.Class"><![CDATA[
	The fully qualified name of the class that will be instantiated to serve
	as the default implementation of the introduced interface.
				]]></xsd:documentation>
			</xsd:annotation>
		</xsd:attribute>
		<xsd:attribute name="delegate-ref" type="xsd:string">
			<xsd:annotation>
				<xsd:documentation><![CDATA[
	A reference to the bean that will serve
	as the default implementation of the introduced interface.
				]]></xsd:documentation>
				<xsd:appinfo>
					<tool:annotation kind="ref"/>
				</xsd:appinfo>
			</xsd:annotation>
		</xsd:attribute>
	</xsd:complexType>

	<xsd:complexType name="basicAdviceType">
		<xsd:attribute name="pointcut" type="xsd:string">
			<xsd:annotation>
				<xsd:documentation><![CDATA[
	The associated pointcut expression.
				]]></xsd:documentation>
			</xsd:annotation>
		</xsd:attribute>
		<xsd:attribute name="pointcut-ref" type="pointcutRefType">
			<xsd:annotation>
				<xsd:documentation><![CDATA[
	The name of an associated pointcut definition.
				]]></xsd:documentation>
				<xsd:appinfo>
					<tool:annotation kind="ref">
						<tool:expected-type type="org.springframework.aop.Pointcut"/>
					</tool:annotation>
				</xsd:appinfo>
			</xsd:annotation>
		</xsd:attribute>
		<xsd:attribute name="method" type="xsd:string" use="required">
			<xsd:annotation>
				<xsd:documentation><![CDATA[
	The name of the method that defines the logic of the advice.
				]]></xsd:documentation>
			</xsd:annotation>
		</xsd:attribute>
		<xsd:attribute name="arg-names" type="xsd:string">
			<xsd:annotation>
				<xsd:documentation><![CDATA[
	The comma-delimited list of advice method argument (parameter) names
	that will be matched from pointcut parameters.
				]]></xsd:documentation>
			</xsd:annotation>
		</xsd:attribute>
	</xsd:complexType>

	<xsd:complexType name="afterReturningAdviceType">
		<xsd:complexContent>
			<xsd:extension base="basicAdviceType">
				<xsd:attribute name="returning" type="xsd:string">
					<xsd:annotation>
						<xsd:documentation><![CDATA[
	The name of the method parameter to which the return value must
	be passed.
						]]></xsd:documentation>
					</xsd:annotation>
				</xsd:attribute>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:complexType name="afterThrowingAdviceType">
		<xsd:complexContent>
			<xsd:extension base="basicAdviceType">
				<xsd:attribute name="throwing" type="xsd:string">
					<xsd:annotation>
						<xsd:documentation><![CDATA[
	The name of the method parameter to which the thrown exception must
	be passed.
						]]></xsd:documentation>
					</xsd:annotation>
				</xsd:attribute>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:complexType name="advisorType">
		<xsd:annotation>
			<xsd:appinfo>
				<tool:annotation>
					<tool:exports type="org.springframework.aop.Advisor"/>
				</tool:annotation>
			</xsd:appinfo>
		</xsd:annotation>
		<xsd:attribute name="id" type="xsd:string"/>
		<xsd:attribute name="advice-ref" type="xsd:string" use="required">
			<xsd:annotation>
				<xsd:documentation><![CDATA[
	A reference to an advice bean.
				]]></xsd:documentation>
				<xsd:appinfo>
					<tool:annotation kind="ref">
						<tool:expected-type type="org.aopalliance.aop.Advice"/>
					</tool:annotation>
				</xsd:appinfo>
			</xsd:annotation>
		</xsd:attribute>
		<xsd:attribute name="pointcut" type="xsd:string">
			<xsd:annotation>
				<xsd:documentation><![CDATA[
	A pointcut expression.
				]]></xsd:documentation>
			</xsd:annotation>
		</xsd:attribute>
		<xsd:attribute name="pointcut-ref" type="pointcutRefType">
			<xsd:annotation>
				<xsd:documentation><![CDATA[
	A reference to a pointcut definition.
				]]></xsd:documentation>
			</xsd:annotation>
		</xsd:attribute>
		<xsd:attribute name="order" type="xsd:int">
			<xsd:annotation>
				<xsd:documentation source="java:org.springframework.core.Ordered"><![CDATA[
	Controls the ordering of the execution of this advice when multiple
	advice executes at a specific joinpoint.
				]]></xsd:documentation>
			</xsd:annotation>
		</xsd:attribute>
	</xsd:complexType>

	<xsd:simpleType name="pointcutRefType">
		<xsd:annotation>
			<xsd:appinfo>
				<tool:annotation kind="ref">
					<tool:expected-type type="org.springframework.aop.Pointcut"/>
				</tool:annotation>
			</xsd:appinfo>
		</xsd:annotation>
		<xsd:union memberTypes="xsd:string"/>
	</xsd:simpleType>

</xsd:schema>
