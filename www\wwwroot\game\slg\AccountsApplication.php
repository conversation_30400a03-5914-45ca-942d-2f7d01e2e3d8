<?php
	include("DbUtil.php");
	
	class AccountsApplication
	{
		
		function fastRegist( ){
			
		}
		
		public function server_list($list_version , $server_list , $recommend ,  $version)
		{
			if($version == $list_version)
			{
				echo "";
			}
			else
			{
				$info = new ServerInfo();
				$info->list_version = $list_version;
				$info->server_list = $server_list;
				$info->recommend = $recommend;
				echo(json_encode($info));
			}
		}
		
		public function regist($account_name , $account_pwd ){
			$dbUtil = new DbUtil();
			$con = $dbUtil->getConnection();
			if( !$con ){
				return;
			}
			$sql = "select * from accounts where user_name = '".$account_name."'";
			//echo($sql);
			if ($result = mysqli_query($con, $sql)) {
				$rows = mysqli_num_rows($result);
				if( $rows === 0)
				{
					$add_user_sql = "insert into accounts (user_name , user_pwd) values ('".$account_name."' , '".$account_pwd."')";
					//echo($add_user_sql);
					if (mysqli_query($con, $add_user_sql) === FALSE) {
			    		mysqli_close($con); //关闭数据库连接
						return 401;
					}
					mysqli_close($con);//关闭数据库连接
					return 200;
				}else{
					return 400;
				}
			}
		}
		
		// 根据server_id到server_list里面去拿  ip地址
		function login( $account_name , $account_pwd , $host, $loginport){
			//首先验证玩家的账号密码是否正确
			$dbUtil = new DBUtil();
			$con =  $dbUtil->getConnection();
			$resultArray = array();
			if(!$con ){
				$resultArray['result'] = 401;
				return json_encode($resultArray);
			}
			//mysql_select_db("accounts");
			$sql = "select user_pwd,user_id from accounts where user_name = '".$account_name."'";
			//echo($sql);
			$result =  mysqli_query($con, $sql);
			if(!$result ){
				mysqli_close($con);//关闭数据库连接
				$resultArray['result'] = 402;
				return json_encode($resultArray);
			}
			$isPasswordOK = false;
			$user_id = 0;
			while( $row = mysqli_fetch_array($result)){
				$passwd = $row["user_pwd"];
				$user_id = $row["user_id"];
				if( $account_pwd == $passwd){
					$isPasswordOK = true;
				}
			}
			mysqli_close($con);//关闭数据库连接
			if( !$isPasswordOK ){
				//返回账号或者密码错误给客户端
				$resultArray['result'] = 403;
				return json_encode($resultArray);
			}
			$socket = socket_create(AF_INET, SOCK_STREAM, SOL_TCP);
			if( $socket == false ){
				$resultArray['result'] = 404;
				return json_encode($resultArray);
			}
 			
			$in = "12";
			$in.=",";
			$in.=$account_name;
			$in.=",";
			$in.=$user_id;
			//socket_write($socket, $in , strlen($in));
			//$out = socket_read($socket, 50);
			
			socket_close($socket);
			$resultArray["account_id"] = $user_id;
			$resultArray["message"] = $out;
			$resultArray["result"] = 201;
			echo json_encode($resultArray);
			
		}
	}
?>
