<?php
error_reporting(0);
header("Content-type: text/html; charset=utf-8");
ini_set('date.timezone','Asia/Shanghai');
session_start();
$quarr = array (
 '6001' => array (
  'db_ip' => '127.0.0.1',
  'db_port' => 3306,
  'db_user' => 'root',
  'db_pswd' => '123456',
  'db_name' => 'accounts',
  'url' => 'http://127.0.0.1:46001/river/',  
  'name'=>'一区',
  'hidde'=> false
 ),
 '1' => array (
  'db_ip' => '127.0.0.1',
  'db_port' => 3306,
  'db_user' => 'root',
  'db_pswd' => '123456',
  'db_name' => 'accounts',
  'httpport' => '20201',
  'name'=>'二区',
  'hidde'=>true
 ),
);
/*
 * 邮件字符串相关
 */
$code = array (
 'title' => '%E7%B3%BB%E7%BB%9F%E9%82%AE%E4%BB%B6',
 'content' => '%E6%AC%A2%E8%BF%8E%E6%9D%A5%E5%88%B0%E6%B1%89%E7%8E%8B%E7%BA%B7%E4%BA%89%2CGM%E4%B8%BA%E6%82%A8%E5%8F%91%E9%80%81%E4%BA%86%E4%B8%80%E5%B0%81%E9%82%AE%E4%BB%B6%2C%E8%AF%B7%E7%AC%91%E7%BA%B3%EF%BC%81',
);

/*
 数据库连接
 */
$conn = @mysql_connect ( '127.0.0.1', 'root', '123456' ) or die ( "数据库连接失败,请联系管理员！" );
mysql_select_db ( 'accounts', $conn );
mysql_query ( "SET NAMES utf8" );

//gm码
$gmcode='ruankor.com';


function poststr($str){
 if(isset($_POST[$str])){
  return $_POST[$str];
 }
die("this link server do not exist".$str);
}
